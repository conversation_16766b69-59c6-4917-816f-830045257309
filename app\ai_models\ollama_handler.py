# {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的 JSON，不要添加其他文字"},

import logging
import json
import os
import requests
import asyncio
import aiohttp
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Dict, Any, List, Optional
from .base import BaseAIModel

class OllamaHandler(BaseAIModel):
    """Ollama模型处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_url = config["api_url"]
        self.model = config["model"]
        
        # 配置重试策略
        self.session = aiohttp.ClientSession()  # 创建异步会话
    async def close(self):
        """关闭模型"""
        await self.session.close()
    async def chat(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """调用Ollama API (最终修正版)"""
        try:
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {"temperature": 0.1, "top_p": 0.9}
            }
            async with self.session.post(
                f"{self.api_url}/api/chat",
                json=payload,
                timeout=200
            ) as response:
                # 先统一获取原始响应文本
                response_text = await response.text()

                # HTTP错误处理
                if response.status != 200:
                    logging.error(f"API错误 [{response.status}]: {response_text[:500]}")
                    return None

                # 手动解析JSON
                try:
                    result = json.loads(response_text)
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败 | 错误: {e} | 响应内容: {response_text[:200]}...")
                    return None

                # 验证响应结构
                if not isinstance(result.get("message"), dict):
                    logging.error(f"响应结构异常，缺少message字段: {result.keys()}")
                    return None

                return result["message"].get("content")

        except aiohttp.ClientError as e:
            logging.error(f"网络请求失败: {str(e)}")
        except asyncio.TimeoutError:
            logging.error("API请求超时")
        except Exception as e:
            logging.error(f"未知错误: {repr(e)}")
        
        return None
    
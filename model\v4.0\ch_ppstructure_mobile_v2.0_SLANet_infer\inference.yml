PreProcess:
  transform_ops:
  - DecodeImage:
      channel_first: false
      img_mode: BGR
  - TableLabelEncode:
      learn_empty_box: false
      loc_reg_num: 8
      max_text_length: 500
      merge_no_span_structure: true
      replace_empty_cell_token: false
  - TableBoxEncode:
      in_box_format: xyxyxyxy
      out_box_format: xyxyxyxy
  - ResizeTableImage:
      max_len: 488
  - NormalizeImage:
      mean:
      - 0.485
      - 0.456
      - 0.406
      order: hwc
      scale: 1./255.
      std:
      - 0.229
      - 0.224
      - 0.225
  - PaddingTableImage:
      size:
      - 488
      - 488
  - ToCHWImage: null
  - KeepKeys:
      keep_keys:
      - image
      - structure
      - bboxes
      - bbox_masks
      - length
      - shape
PostProcess:
  name: TableLabelDecode
  merge_no_span_structure: true
  character_dict:
  - <thead>
  - </thead>
  - <tbody>
  - </tbody>
  - <tr>
  - </tr>
  - <td>
  - <td
  - '>'
  - </td>
  - ' colspan="2"'
  - ' colspan="3"'
  - ' colspan="4"'
  - ' colspan="5"'
  - ' colspan="6"'
  - ' colspan="7"'
  - ' colspan="8"'
  - ' colspan="9"'
  - ' colspan="10"'
  - ' colspan="11"'
  - ' colspan="12"'
  - ' colspan="13"'
  - ' colspan="14"'
  - ' colspan="15"'
  - ' colspan="16"'
  - ' colspan="17"'
  - ' colspan="18"'
  - ' colspan="19"'
  - ' colspan="20"'
  - ' rowspan="2"'
  - ' rowspan="3"'
  - ' rowspan="4"'
  - ' rowspan="5"'
  - ' rowspan="6"'
  - ' rowspan="7"'
  - ' rowspan="8"'
  - ' rowspan="9"'
  - ' rowspan="10"'
  - ' rowspan="11"'
  - ' rowspan="12"'
  - ' rowspan="13"'
  - ' rowspan="14"'
  - ' rowspan="15"'
  - ' rowspan="16"'
  - ' rowspan="17"'
  - ' rowspan="18"'
  - ' rowspan="19"'
  - ' rowspan="20"'

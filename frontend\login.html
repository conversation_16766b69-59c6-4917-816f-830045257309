<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>登录 - 外贸单据处理系统</title>
    <link href="/static/js/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/js/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #4a6baf, #2c3e50);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            width: 100%;
            max-width: 400px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .login-header {
            background: linear-gradient(135deg, #4a6baf, #2c3e50);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }
        .form-control:focus {
            border-color: #4a6baf;
            box-shadow: 0 0 0 0.25rem rgba(74, 107, 175, 0.25);
        }
    </style>
</head>
<body>
    <div class="login-card card">
        <div class="login-header text-center">
            <h4><i class="fas fa-file-alt me-2"></i>外贸单据处理系统</h4>
        </div>
        <div class="card-body p-4">
            <form id="loginForm">
                <div class="mb-3">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>登录
                </button>
            </form>
        </div>
    </div>

    <script>
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({ username, password })
            });
            
            const data = await response.json();
            if (data.error) throw new Error(data.error);
            
            // 保存token
            localStorage.setItem('token', data.token);
            // 跳转到首页
            window.location.href = '/static/index.html';
        } catch (e) {
            alert('登录失败: ' + e.message);
        }
    });
    </script>
</body>
</html> 
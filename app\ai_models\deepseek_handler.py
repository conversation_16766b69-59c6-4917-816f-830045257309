import logging
import json
import os
import httpx
from typing import Dict, Any, List, Optional
from .base import BaseAIModel

class DeepSeekHandler(BaseAIModel):
    """DeepSeek模型处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.max_tokens = config.get("max_tokens", 8000)
        self.api_url = config["api_url"]
        self.api_key = config["api_key"]
    async def close(self):
        """关闭模型"""
        await self.session.close()
    async def chat(self, prompt: str) -> Optional[str]:
        """调用DeepSeek API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": prompt}],
                "stream": False
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/v1/chat/completions",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    logging.error(f"DeepSeek API错误: {response.status_code}")
                    return None
                    
                result = response.json()
                return result.get("choices", [{}])[0].get("message", {}).get("content")
                
        except Exception as e:
            logging.error(f"调用DeepSeek API失败: {str(e)}")
            return None
    
    async def generate_text(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> Optional[str]:
        """调用DeepSeek API生成文本"""
        try:
            api_key = self.config["api_key"]
            api_url = self.config["api_url"]
            model = self.config["model"]
            
            if not api_key or not api_url:
                logging.error("未正确配置DeepSeek API")
                return None
                
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(api_url, json=payload, headers=headers)
                if response.status_code != 200:
                    logging.error(f"DeepSeek API错误: {response.status_code}, {response.text}")
                    return None
                    
                result = response.json()
                return result.get("choices", [{}])[0].get("message", {}).get("content")
        except Exception as e:
            logging.error(f"调用DeepSeek API失败: {str(e)}")
            return None
    
    
import logging
import os
from typing import Dict, Any, List
from pdf2image import convert_from_path
from .base import BaseFileProcessor
from ..ai_models.base import BaseAIModel
from fastapi import Depends
from app.dependencies import get_ocr_initializer
from app.ocr.ocr_initializer import OCRInitializer

class PDFProcessor(BaseFileProcessor):
    """PDF文件处理器"""
    
    def __init__(self, ai_model: BaseAIModel, ocr_initializer: OCRInitializer):
        super().__init__(ai_model)
        if not isinstance(ocr_initializer, OCRInitializer):
            raise ValueError("ocr_initializer必须是OCRInitializer实例")
        try:
            from ..ocr import OCRProcessor
            self.ocr_processor = OCRProcessor(ocr_initializer)
            self.has_ocr = True
            logging.info("PDF处理器初始化成功")
        except ImportError as e:
            logging.error(f"OCR功能初始化失败: {str(e)}")
            self.has_ocr = False
        except Exception as e:
            logging.error(f"PDF处理器初始化失败: {str(e)}")
            self.has_ocr = False
    
    async def process(self, file_path: str) -> Dict[str, Any]:
        """处理PDF文件"""
        try:
            logging.info(f"开始处理PDF文件: {file_path}")
            
            if not self.has_ocr:
                return {
                    "error": "OCR功能不可用，无法处理PDF",
                    "file_name": os.path.basename(file_path)
                }
            
            # 转换PDF为图片
            images = convert_from_path(file_path, dpi=300)
            if not images:
                return {
                    "error": "PDF转换失败或为空文件",
                    "file_name": os.path.basename(file_path)
                }
            
            # 处理每一页
            all_text = []
            max_pages = 5  # 设置最大处理页数为5
            for i, image in enumerate(images):
                if i >= max_pages:
                    break
                logging.info(f"处理第 {i+1}/{len(images)} 页")
                result = await self.ocr_processor.process_image(image)
                if result["status"] == "success":
                    text = "\n".join(result["text"])
                    all_text.append(text)
            
            if not all_text:
                return {
                    "error": "未能提取到文本内容",
                    "file_name": os.path.basename(file_path)
                }
            
            # 合并所有页面的文本
            content = "\n\n".join(all_text)
            
            # 返回提取的内容和文件信息
            return {
                "content": content,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "pdf",
                    "page_count": len(images)
                }
            }
            
        except Exception as e:
            logging.error(f"PDF处理失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "file_name": os.path.basename(file_path)
            } 
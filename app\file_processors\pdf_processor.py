import logging
import os
import json
from typing import Dict, Any, List
from pdf2image import convert_from_path
from .base import BaseFileProcessor
from ..ai_models.base import BaseAIModel
from fastapi import Depends
from app.dependencies import get_ocr_initializer
from app.ocr.ocr_initializer import OCRInitializer
from ..ai_models.prompts import SPLIT_PROMPT,SPLIT_PROMPT_I
from .util import Util
class PDFProcessor(BaseFileProcessor):
    """PDF文件处理器"""
    
    def __init__(self, ai_model: BaseAIModel, ocr_initializer: OCRInitializer):
        super().__init__(ai_model)
        self.ai_model = ai_model
        if not isinstance(ocr_initializer, OCRInitializer):
            raise ValueError("ocr_initializer必须是OCRInitializer实例")
        try:
            from ..ocr import OCRProcessor
            self.ocr_processor = OCRProcessor(ocr_initializer)
            self.has_ocr = True
            logging.info("PDF处理器初始化成功")
        except ImportError as e:
            logging.error(f"OCR功能初始化失败: {str(e)}")
            self.has_ocr = False
        except Exception as e:
            logging.error(f"PDF处理器初始化失败: {str(e)}")
            self.has_ocr = False
    
    async def process(self, file_path: str,flag,ie_flag="E") -> Dict[str, Any]:
        """处理PDF文件"""
        try:
            logging.info(f"开始处理PDF文件: {file_path}")
            
            if not self.has_ocr:
                return {
                    "error": "OCR功能不可用，无法处理PDF",
                    "file_name": os.path.basename(file_path)
                }
            
            
            # 转换PDF为图片
            images = convert_from_path(file_path)
            if not images:
                return {
                    "error": "PDF转换失败或为空文件",
                    "file_name": os.path.basename(file_path)
                }
            
            # 处理每一页
            all_text = []
            max_pages = 50  # 设置最大处理页数为5
            for i, image in enumerate(images):
                if i >= max_pages:
                    break
                logging.info(f"处理第 {i+1}/{len(images)} 页")
                result = await self.ocr_processor.process_image(image)
                print("pdf文件读取结果是：",result)
                if result["status"] == "success":
                    text = result["text"]
                    all_text.append(text)
            
            if not all_text:
                return {
                    "error": "未能提取到文本内容",
                    "file_name": os.path.basename(file_path)
                }
            
            # 合并所有页面的文本
            ocr_text = "\n\n".join(all_text)
            # ocr_text = ocr_text.replace("\t", "\\\\t")
            # ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            ocr_text = "文件名：" + os.path.basename(file_path) + "\n文件内容：" + ocr_text
            if flag:
                #调用大模型对content进行分割
                if ie_flag == "I":
                    split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                else:
                    split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                split_message = [
                    {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                    {"role": "user", "content": split_prompt}
                    ]
                content = await self.ai_model.chat(split_message)
                result = content.replace("```json", "").replace("```", "")
                formatted = json.loads(result)
            else:
                formatted = {
                    "单据类型": "舱单",
                    "单据内容": ocr_text
                }

            # 返回提取的内容和文件信息
            return {
                "contents":formatted,
                "ocr_text":ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "pdf",
                    "file_path": file_path
                }
            }
            
        except Exception as e:
            logging.error(f"PDF处理失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "file_name": os.path.basename(file_path)
            } 
        


SHIPPING = {
    "0": "非保税区",
    "1": "监管仓库",
    "2": "水路运输",
    "3": "铁路运输",
    "4": "公路运输",
    "5": "航空运输",
    "6": "邮件运输",
    "7": "保税区",
    "8": "保税仓库",
    "9": "其他方式运输",
    "G": "固定设施运输",
    "H": "边境特殊海关作业区",
    "L": "旅客携带",
    "P": "洋浦保税港区",
    "S": "特殊综合保税区",
    "T": "综合实验区",
    "W": "物流中心",
    "X": "物流园区",
    "Y": "保税港区",
    "Z": "出口加工区"
}
# 运输方式处理
# 输入：str 运输方式代码或运输方式名称
# 输出：代码和名称
def shipping_handler(str):
    if str in ["海运","水路"]:
        str = "2"
    elif str in ["空运","航空"]:
        str = "5"
    elif str in ["陆运","公路"]:
        str = "4"
    elif str in ["铁路"]:
        str = "3"
    elif str in ["快递","邮政"]:
        str = "6"

    for key, value in SHIPPING.items():
        if str == key or str == value:
            return key, value
    return "", str
from db.query import DBQuery

def main():
    try:
        # 1. 查询示例
        users = DBQuery.fetch_all("SELECT * FROM users WHERE age > %s", (18,))
        print(f"查询到 {len(users)} 条用户记录")
        
        # 2. 插入示例
        user_id = DBQuery.insert(
            "INSERT INTO users (name, age) VALUES (%s, %s)",
            ("张三", 25)
        )
        print(f"插入成功，影响行数: {user_id}")
        
        # 3. 事务示例
        try:
            # 开始事务(通过设置commit=True)
            DBQuery.update(
                "UPDATE accounts SET balance = balance - %s WHERE user_id = %s",
                (100, 1),
                commit=False
            )
            
            DBQuery.update(
                "UPDATE accounts SET balance = balance + %s WHERE user_id = %s",
                (100, 2),
                commit=True  # 最后一步提交事务
            )
            print("转账事务执行成功")
        except Exception as e:
            print(f"转账失败: {e}")
        
        # 4. 批量插入示例
        data = [("李四", 30), ("王五", 28)]
        count = DBQuery.insert_many(
            "INSERT INTO users (name, age) VALUES (%s, %s)",
            data
        )
        print(f"批量插入成功，影响行数: {count}")
        
    except Exception as e:
        print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
# 成交方式

Trans_Mode = {
    "CIF":"CIF",
    "DAP":"CIF",
    "DPU":"CIF",
    "DDP":"CIF",
    "CFR":"C&F",
    "CPT":"C&F",
    "EXW":"EXW",
    "CIP":"CIF",
    "FCA":"FOB",
    "FOB":"FOB",
    "FAS":"FOB",
}
Trans_Mode_Code = {
    "CIF":"1",
    "C&F":"2",
    "EXW":"7",
    "FOB":"3"
}

def trans_mode_handler(input_text):
    transMode = input_text
    transModeCode = ""
    # 先获取保单成交方式
    for code,name in Trans_Mode.items():
        if code == input_text or name == input_text:
            transMode = name
    #再获取代码
    for code,name in Trans_Mode_Code.items():
        if code == transMode:
            transModeCode = name
    return transModeCode,transMode
import logging
import json
import re
from typing import List, Dict, Any
import asyncio
from typing import Dict, Any, Optional
from .ai_models.base import BaseAIModel
from .ai_models.prompts import CLASSIFY_PROMPT, format_by_type,CLASSIFY_PROMPT_I,format_by_type_I
from .ai_models.prompts import VERIFY_PROMPT,MERGE_PROMPT,verify_json_prompt,verify_list_json_prompt
from .ai_models.prompts import split_product_fields_prompt
from .ai_models.prompts import format_by_manifest
from .ai_models.special_prompts import format_by_tradeName
from .config import ENABLE_DOCUMENT_CHECK
from collections import defaultdict
class ContentProcessor:
    """文档内容处理器"""
    
    def __init__(self, ai_model: BaseAIModel):
        self.ai_model = ai_model

    # 增加处理大模型返回的json里面含有特殊字符的逻辑
    def clean_and_fix_json(self,text):
        # 1. 基础清理
        text = text.strip().lstrip('\ufeff')  # 移除BOM
        
        # 2. 替换中文引号
        text = text.replace('“', '"').replace('”', '"')
        
        # 3. 处理控制字符（分为结构位置和字符串值两种情况）
        
        # 首先处理JSON结构中的非法控制字符（键/值外的位置）
        # 这些位置不允许有任何控制字符（包括\t）
        def clean_structural_chars(match):
            # 匹配不在字符串内的部分
            return re.sub(r'[\x00-\x1f\x7f]', '', match.group(0))
        
        # 使用正则表达式区分字符串内和字符串外的部分
        text = re.sub(r'"[^"\\]*(?:\\.[^"\\]*)*"|([^"\\]+)', 
                    lambda m: m.group(0) if m.group(0).startswith('"') else clean_structural_chars(m),
                    text)
        
        # 4. 处理字符串值中的控制字符
        # 只允许特定的转义控制字符
        def clean_string_value(match):
            s = match.group(1)
            # 将未转义的控制字符转为空格
            s = re.sub(r'[\x00-\x1f\x7f]', ' ', s)
            return f'"{s}"'
        
        text = re.sub(r'"([^"\\]*(?:\\.[^"\\]*)*)"', clean_string_value, text)
        
        # 5. 尝试解析
        try:
            return json.loads(text)
        except json.JSONDecodeError as e:
            print(f"解析失败: {e}")
            return None

    async def format_segment(self, segment: str, _doc_type: str) -> Dict[str, Any]:
        """格式化单个段落"""
        format_prompt = format_by_type(segment, _doc_type)
        format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
        format_message = [
            {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的 JSON格式字段，不要添加其他文字和字段，不要改变字段的位置，不得假设或创造不存在的信息。必须完整的输出全部内容，不能省略任何内容，不能出现类似其余信息按照相同结构依次列出。"},
            {"role": "user", "content": format_prompt}
        ]
        
        try:
            result = await self.ai_model.chat(format_message)
            
            if not result:
                return None
                
            result = result.replace("```json", "").replace("```", "")
            formatted = self.clean_and_fix_json(result)
            
            formatted["单据类型"] = _doc_type
            return formatted
        except Exception as e:
            print(f"格式化段落时出错: {str(e)}")
            return None
        
    async def format_segment_I(self, segment: str, _doc_type: str) -> Dict[str, Any]:
        """格式化单个段落"""
        format_prompt = format_by_type_I(segment, _doc_type)
        format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
        format_message = [
            {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的 JSON格式字段，不要添加其他文字和字段，不要改变字段的位置，不得假设或创造不存在的信息。必须完整的输出全部内容，不能省略任何内容，不能出现类似其余信息按照相同结构依次列出。"},
            {"role": "user", "content": format_prompt}
        ]
        
        try:
            result = await self.ai_model.chat(format_message)
            
            if not result:
                return None
                
            result = result.replace("```json", "").replace("```", "")
            formatted = self.clean_and_fix_json(result)
            
            formatted["单据类型"] = _doc_type
            return formatted
        except Exception as e:
            print(f"格式化段落时出错: {str(e)}")
            return None

    async def process_segments(self, segments: List[str], _doc_type: str, max_concurrent: int = 4) -> List[Dict[str, Any]]:
        """并发处理所有段落"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_task(segment: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.format_segment(segment, _doc_type)
        
        tasks = [limited_task(seg) for seg in segments]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤掉异常和None结果
        return [res for res in results if not isinstance(res, Exception) and res is not None]
    # 0603 增加处理舱单接口
    async def process_content_manifest(self, content: str, doc_type: str) -> Dict[str, Any]:
        # formatted = self.format_segment(content, _doc_type)        
        format_prompt = format_by_manifest(content)
        format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
        format_message = [
            {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的 JSON格式字段，不要添加其他文字和字段，不要改变字段的位置，不得假设或创造不存在的信息。必须完整的输出全部内容，不能省略任何内容，不能出现类似其余信息按照相同结构依次列出。"},
            {"role": "user", "content": format_prompt}
            ]
        result = await self.ai_model.chat(format_message)
        if not result:
            return {"error": "内容格式化失败"}
        
        result = result.replace("```json", "").replace("```", "")
        formatted = self.clean_and_fix_json(result)
        formatted["单据类型"] = doc_type

        return formatted
    
    async def process_content(self, content: str, doc_type: str,ie_flag) -> Dict[str, Any]:
        """处理文档内容
        
        Args:
            content: 提取的文本内容
            file_info: 文件信息，包含文件名、类型等
            
        Returns:
            Dict[str, Any]: 处理结果
            
        处理过程：
            1、使用大模型对文档进行分类
            2、根据分类的结果进行格式化
            3、对格式化后的内容使用大模型进行业务逻辑的校验
        """
        if ie_flag == "I":
            doc_type = "".join(doc_type.split()).lower()
            print("文件类型：",doc_type)
            try:
                if doc_type in ["进口发票", "发票", "IV", "INVOICE","INV","PI","CI","COMMERCIAL INVOICE","inv","iv","invoice"]:
                    _doc_type = "进口发票"
                elif doc_type in ["进口箱单", "箱单", "PACKING LIST", "PACKING","PL","pl","packing list","packing","packinglist"]:
                    _doc_type = "进口箱单"
                elif doc_type in ["进口证书", "证书", "CERTIFICATE", "CERT","cert","certificate"]:
                    _doc_type = "进口证书"
                elif doc_type in ["入货通知", "入货", "SHIPPING NOTICE"]:
                    _doc_type = "入货通知"
                elif doc_type in ["舱单样本", "舱单", "MANIFEST"]:
                    _doc_type = "舱单样本"
                elif doc_type in ["报关单", "报关", "CUSTOMS DECLARATION"]:
                    _doc_type = "报关单"
                elif doc_type in ["进口合同", "合同", "CONTRACT","CL","SALES CONTRACT","contract","cl","salescontract"]:
                    _doc_type = "进口合同"
                elif doc_type in ["提运单", "提单", "BILL OF LADING", "B/L"]:
                    _doc_type = "提运单"
                elif doc_type in ["分箱明细", "分箱", "SPLIT DETAILS"]:
                    _doc_type = "分箱明细"
                elif doc_type in ["申报要素", "申报", "DECLARATION ELEMENTS","要素","报关要素"]:
                    _doc_type = "申报要素"
                elif doc_type in ["历史申报记录","进口申报记录","申报记录"]:
                    _doc_type = "历史申报记录"
                elif doc_type in ["电子底账","检验检疫"]:
                    _doc_type = "检验检疫"
                else:
                    _doc_type = "其他"
                    # 1、根据文件内容进行文档分类
                    classify_prompt = CLASSIFY_PROMPT_I.format(text=content)
                    classify_message = [
                        {"role": "system", "content": "你是一个严谨的文档类型分类助手，只输出用户要求的内容，不要添加其他文字"},
                        {"role": "user", "content": classify_prompt}
                        ]

                    category = await self.ai_model.chat(classify_message)
                    # await self.ai_model.close()
                    # 对于第一步分类，返回纯文本结果
                    # 清理结果文本，提取关键文档类型
                    valid_types = ["进口发票", "进口箱单", "进口证书", "入货通知", "舱单样本", "报关单","进口合同", "提运单","分箱明细","申报要素","检验检疫","历史申报记录","其他"]
                    for doc_type in valid_types:
                        if doc_type in category:
                            _doc_type = doc_type

                # 2、根据文档类型进行数据格式化
                #增加文件内容过长，需要分段处理
                if len(content) > 8000:
                    # 将内容分成多个段落
                    print("份段落的type:",_doc_type)
                    segments = self.adaptive_segment_from_content(content, 8000)
                    tasks = [self.format_segment_I(segment,_doc_type) for segment in segments]
                    formatted_segments = await asyncio.gather(*tasks)

                    # 多线程处理每个段落
                    # formatted_segments = await self.process_segments(segments, _doc_type)

                    # 合并格式化后的结果
                    formatted = await self.merge_jsons(formatted_segments,_doc_type)

                else:
                    # formatted = self.format_segment(content, _doc_type)       
                    format_prompt = format_by_type_I(content, _doc_type)
                    format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
                    format_message = [
                        {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的 JSON格式字段，不要添加其他文字和字段，不要改变字段的位置，不得假设或创造不存在的信息。必须完整的输出全部内容，不能省略任何内容，不能出现类似其余信息按照相同结构依次列出。"},
                        {"role": "user", "content": format_prompt}
                        ]
                    result = await self.ai_model.chat(format_message)
                    # await self.ai_model.close()
                    # print("格式化前的内容：",format_prompt)
                    # print("格式化后的内容：",result)
                    if not result:
                        return {"error": "内容格式化失败"}
                    
                    result = result.replace("```json", "").replace("```", "")
                    formatted = self.clean_and_fix_json(result)
                    formatted["单据类型"] = _doc_type

                    # 如果启用了文档检查，则对格式化后的内容使用大模型进行业务逻辑的校验
                    if ENABLE_DOCUMENT_CHECK:
                        #3、对格式化后的内容使用大模型进行业务逻辑的校验
                        verify_prompt = VERIFY_PROMPT.format(text=formatted)
                        verify_prompt = " ".join(line.strip() for line in verify_prompt.splitlines() if line.strip())
                        verify_message = [
                            {"role": "system", "content": "你是一个严谨的校验助手，只输出用户要求的JSON，不要添加其他文字"},
                            {"role": "user", "content": verify_prompt}
                            ]
                        verify_result = await self.ai_model.chat(verify_message)
                        # await self.ai_model.close()
                        verify_result = json.loads(verify_result)
                        formatted.update(verify_result)
                return formatted
                    
            except Exception as e:
                import traceback
                traceback.print_exc()
                logging.error(f"内容处理失败: {str(e)}")
                return {"error": str(e)} 
        else:
            doc_type = "".join(doc_type.split())
            print("文件类型：",doc_type)
            try:
                if doc_type in ["出口发票", "发票", "IV", "INVOICE","INV","PI","CI","COMMERCIAL INVOICE"]:
                    _doc_type = "出口发票"
                elif doc_type in ["出口箱单", "箱单", "PACKING LIST", "PACKING","PL"]:
                    _doc_type = "出口箱单"
                elif doc_type in ["出口证书", "证书", "CERTIFICATE", "CERT"]:
                    _doc_type = "出口证书"
                elif doc_type in ["入货通知", "入货", "SHIPPING NOTICE"]:
                    _doc_type = "入货通知"
                elif doc_type in ["舱单样本", "舱单", "MANIFEST"]:
                    _doc_type = "舱单样本"
                elif doc_type in ["报关单", "报关", "CUSTOMS DECLARATION"]:
                    _doc_type = "报关单"
                elif doc_type in ["出口合同", "合同", "CONTRACT","CL","SALES CONTRACT"]:
                    _doc_type = "出口合同"
                elif doc_type in ["提运单", "提单", "BILL OF LADING", "B/L"]:
                    _doc_type = "提运单"
                elif doc_type in ["分箱明细", "分箱", "SPLIT DETAILS"]:
                    _doc_type = "分箱明细"
                elif doc_type in ["申报要素", "申报", "DECLARATION ELEMENTS","要素","报关要素"]:
                    _doc_type = "申报要素"
                elif doc_type in ["电子底账","检验检疫"]:
                    _doc_type = "检验检疫"
                elif doc_type in ["随附单证信息","随附单证"]:
                    _doc_type = "随附单证信息"
                else:
                    _doc_type = "其他"
                # if doc_type in ["出口发票", "出口箱单", "出口证书", "入货通知", "舱单样本", "报关单","出口合同", "提运单","分箱明细","申报要素"]:
                #     _doc_type = doc_type
                # else:
                    # _doc_type = "其他"
                    # 1、根据文件内容进行文档分类
                    classify_prompt = CLASSIFY_PROMPT.format(text=content)
                    classify_message = [
                        {"role": "system", "content": "你是一个严谨的文档类型分类助手，只输出用户要求的内容，不要添加其他文字"},
                        {"role": "user", "content": classify_prompt}
                        ]

                    category = await self.ai_model.chat(classify_message)
                    # await self.ai_model.close()
                    # 对于第一步分类，返回纯文本结果
                    # 清理结果文本，提取关键文档类型
                    valid_types = ["出口发票", "出口箱单", "出口证书", "入货通知", "舱单样本", "报关单","出口合同", "提运单","分箱明细","申报要素","检验检疫","其他"]
                    for doc_type in valid_types:
                        if doc_type in category:
                            _doc_type = doc_type

                # 2、根据文档类型进行数据格式化
                #增加文件内容过长，需要分段处理
                if len(content) > 7000:
                    # 将内容分成多个段落
                    segments = self.adaptive_segment_from_content(content, 7000)
                    tasks = [self.format_segment(segment,_doc_type) for segment in segments]
                    formatted_segments = await asyncio.gather(*tasks)

                    # 多线程处理每个段落
                    # formatted_segments = await self.process_segments(segments, _doc_type)

                    # 合并格式化后的结果
                    formatted = await self.merge_jsons(formatted_segments,_doc_type)

                else:
                    # formatted = self.format_segment(content, _doc_type)        
                    format_prompt = format_by_type(content, _doc_type)
                    format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
                    format_message = [
                        {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的 JSON格式字段，不要添加其他文字和字段，不要改变字段的位置，不得假设或创造不存在的信息。必须完整的输出全部内容，不能省略任何内容，不能出现类似其余信息按照相同结构依次列出。"},
                        {"role": "user", "content": format_prompt}
                        ]
                    result = await self.ai_model.chat(format_message)
                    # await self.ai_model.close()
                    # print("格式化前的内容：",format_prompt)
                    # print("格式化后的内容：",result)
                    if not result:
                        return {"error": "内容格式化失败"}
                    print("格式化内容---------",result)
                    result = result.replace("```json", "").replace("```", "")
                    formatted = self.clean_and_fix_json(result)
                    print("json转换后的结果--------",formatted)
                    if isinstance(formatted,list) and formatted:  
                        formatted = formatted[0]
                    formatted["单据类型"] = _doc_type

                    # 如果启用了文档检查，则对格式化后的内容使用大模型进行业务逻辑的校验
                    if ENABLE_DOCUMENT_CHECK:
                        #3、对格式化后的内容使用大模型进行业务逻辑的校验
                        verify_prompt = VERIFY_PROMPT.format(text=formatted)
                        verify_prompt = " ".join(line.strip() for line in verify_prompt.splitlines() if line.strip())
                        verify_message = [
                            {"role": "system", "content": "你是一个严谨的校验助手，只输出用户要求的JSON，不要添加其他文字"},
                            {"role": "user", "content": verify_prompt}
                            ]
                        verify_result = await self.ai_model.chat(verify_message)
                        # await self.ai_model.close()
                        verify_result = json.loads(verify_result)
                        formatted.update(verify_result)
                return formatted
                    
            except Exception as e:
                import traceback
                traceback.print_exc()
                logging.error(f"内容处理失败: {str(e)}")
                return {"error": str(e)} 

    def guess_source_type_from_content(self, content):
        lines = content.strip().splitlines()
        if not lines:
            return "text"

        # 统计表格特征（每行中的分隔符数量）
        tab_counts = [line.count("\t") for line in lines]
        avg_line_len = sum(len(line) for line in lines) / len(lines)
        structured_line_count = sum(1 for line in lines if "|" in line or "," in line or ":" in line or "\t" in line)

        # 增强表格判断逻辑
        # 如果大部分行都有较多的 \t 分隔符，很可能是表格
        if max(tab_counts, default=0) > 2 and sum(tab_counts) / len(lines) > 1:
            return "table"

        # 原始表格判断逻辑
        if avg_line_len < 60 and structured_line_count / len(lines) > 0.5:
            return "table"
        
        # 图片OCR判断
        if len(lines) < 5 and len(content) > 1000:
            return "ocr_like"

        return "text"
    def split_excel_rows(self,rows, max_chars=2500):
        segments = []
        current = []
        total_len = 0

        for row in rows:
            row_text = " | ".join(str(cell) for cell in row)  # 转成文本行
            row_len = len(row_text)
            if total_len + row_len > max_chars:
                segments.append("\n".join(current))
                current = [row_text]
                total_len = row_len
            else:
                current.append(row_text)
                total_len += row_len

        if current:
            segments.append("\n".join(current))

        return segments

    def semantic_split(self,content, max_length=2500):
        # 替换连续多个空白符为一个换行符，处理 OCR、Word、PDF 的情况
        normalized = re.sub(r'[\r\n]+', '\n', content)
        
        # 以语义段落或逻辑分割点为参考切分点
        split_points = re.split(r'(?<=[。！？])\s*|\n{2,}|\n(?=\d+[\）.)、])', normalized)

        segments = []
        buffer = ""

        for part in split_points:
            part = part.strip()
            if not part:
                continue

            if len(buffer) + len(part) <= max_length:
                buffer += part + "\n"
            else:
                if buffer:
                    segments.append(buffer.strip())
                buffer = part + "\n"

        if buffer:
            segments.append(buffer.strip())

        return segments

    def adaptive_segment_from_content(self,content, max_length=2500):
        import re
        source_type = self.guess_source_type_from_content(content)
        print("文本类型：",source_type)
        if source_type == "table":
            rows = [line.split() for line in content.strip().splitlines()]
            return self.split_excel_rows(rows, max_chars=max_length)
        
        elif source_type == "ocr_like":
            # 先按句号/标点切句
            sentences = re.split(r'(?<=[。；？！])', content)
            chunks = []
            current_chunk = ""

            for sentence in sentences:
                if len(current_chunk) + len(sentence) <= max_length:
                    current_chunk += sentence
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = sentence
            if current_chunk:
                chunks.append(current_chunk.strip())

            return chunks
        
        else:
            return self.semantic_split(content, max_length)


    async def merge_jsons(self, json_list,_doc_type):
        """合并多个JSON，商品信息去重，并移除空值"""
        def deep_merge(target, source):
            _tag = True
            total_fileds = 2
            if _doc_type in ["报关单","出口箱单","出口发票"]:
                total_fileds = 5
            if len(source) > total_fileds:
                _tag = False
            """递归合并两个字典，优先保留非空值"""
            for key, value in source.items():
                if key in target:
                    # 如果都是字典，递归合并
                    if isinstance(target[key], dict) and isinstance(value, dict):
                        deep_merge(target[key], value)
                    # 如果是商品信息列表，合并去重
                    elif isinstance(target[key], list) and isinstance(value, list) and key == "商品信息":
                        target[key] = merge_products(target[key], value, _tag)
                    elif isinstance(target[key], list) and isinstance(value, list) and key == "集装箱信息":
                        target[key] = merge_containers(target[key], value)
                    elif isinstance(target[key], list) and isinstance(value, list) and key == "分箱明细":
                        target[key] = merge_split_products(target[key], value)
                    elif isinstance(target[key], list) and isinstance(value, list) and key == "申报要素":
                        target[key] = merge_split_products(target[key], value)
                    elif isinstance(target[key], list) and isinstance(value, list) and key == "历史申报记录":
                        target[key] = merge_history_products(target[key], value)
                    # 其他列表直接覆盖（或自定义规则）
                    elif isinstance(target[key], list) and isinstance(value, list):
                        target[key] = value
                    # 非列表字段：优先保留非空值
                    else:
                        if target[key] in (None, "", [], {}):
                            target[key] = value
                else:
                    # 如果目标中没有该键，直接添加
                    target[key] = value
            return target

        def merge_split_products(split_products1, split_products2):
            all_split_products = split_products1 + split_products2
            return all_split_products
        def merge_history_products(split_products1, split_products2):
            all_split_products = split_products1 + split_products2
            return all_split_products   
        # 合并集装箱信息
        def merge_containers(containers1, containers2):
            container_map = defaultdict(dict)
            all_containers = containers1 + containers2
            # return all_containers
            for container in all_containers:
                if not container:
                    continue
                model = container.get("集装箱号", "")
                if not model:  # 如果集装箱号为空，跳过该条目
                    continue
                for key, value in container.items():
                    if key not in container_map[model]:
                        container_map[model][key] = value
            # 转换为最终列表
            merged_containers = list(container_map.values())
            return merged_containers
                
        def merge_products(products1, products2, _tag):
            
            # 找出所有有效的品牌类型和出口享惠情况
            def collect_field_values(products, field_name):
                values = set()
                for product in products:
                    if product and field_name in product and product[field_name] and product[field_name] not in (None, "", [], {},"0"):
                        values.add(product[field_name])
                return list(values) if values else None
            
            
            # 预处理函数：补充缺失的关键字段
            def preprocess_products(products, brand_types, export_statuses):
                processed = []
                for product in products:
                    if not product:
                        continue
                        
                    # 克隆原始产品信息
                    processed_product = product.copy()
                    
                    # 补充品牌类型
                    if '品牌类型' not in processed_product or not processed_product['品牌类型']:
                        if brand_types:
                            processed_product['品牌类型'] = brand_types[0]  # 取第一个可用值
                    
                    # 补充出口享惠情况
                    if '出口享惠情况' not in processed_product or not processed_product['出口享惠情况']:
                        if export_statuses:
                            processed_product['出口享惠情况'] = export_statuses[0]  # 取第一个可用值
                            
                    processed.append(processed_product)
                return processed
            # 合并逻辑：保留所有字段，优先非空值
            all_products = products1 + products2
            brand_types = collect_field_values(products1, '品牌类型')
            export_statuses = collect_field_values(products1, '出口享惠情况')
            
            # 预处理两个产品列表
            processed1 = preprocess_products(products1, brand_types, export_statuses)
            processed2 = preprocess_products(products2, brand_types, export_statuses)

            #0426修改，目前进行了文档内容拆分，所以一个文档中一定是同一个单据，所以商品信息直接合并
            return processed1+processed2

        def remove_empty(data):
            """递归移除空值字段"""
            if isinstance(data, dict):
                return {
                    k: remove_empty(v)
                    for k, v in data.items()
                    if v not in (None, "", [], {}) and remove_empty(v) not in (None, "", [], {})
                }
            elif isinstance(data, list):
                return [remove_empty(v) for v in data if v not in (None, "", [], {})]
            else:
                return data
        def clean_json_flat(obj):
            """清理最外层的 None 和空字符串（不递归处理嵌套）"""
            if isinstance(obj, dict):
                return {k: v for k, v in obj.items() if v not in (None, "")}
            return obj
        
        # 找出字段最多的JSON
        def get_largest_json(jsons):
            max_fields = 0
            largest_json = {}
            for j in jsons:
                # 统计最顶层的字段数量
                field_count = len(j.keys())
                if field_count > max_fields:
                    max_fields = field_count
                    largest_json = j
            return largest_json
        # 找出字段最多的JSON
        largest_json = get_largest_json(json_list)
        cleaned_list = [clean_json_flat(j) for j in json_list]
        # 找到最大JSON在清理后列表中的对应项
        base_json = next(j for j in cleaned_list 
                    if clean_json_flat(largest_json) == j)
        merged = base_json.copy()

        for j in cleaned_list:
            # 不再与空字典合并，只合并其他JSON
            if j is not base_json and j != base_json:
                merged = deep_merge(merged, j)
        return remove_empty(merged)

    async def merge_contents(self, contents):
        """合并多个文档的内容
        
        Args:
            contents: 多个文档的内容
            
        Returns:合并的结果
        """
        format_prompt = MERGE_PROMPT.format(json.dumps(contents, indent=2, ensure_ascii=False))
        format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
        format_message = [
            {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的JSON，不要添加其他文字"},
            {"role": "user", "content": format_prompt}
            ]
        import time
        merge_start = time.perf_counter()
        result = await self.ai_model.chat(format_message)
        # await self.ai_model.close()
        merge_end = time.perf_counter()
        if not result:
            return {"error": "内容格式化失败"}
        
        import re
        json_match = re.search(r"```json.*?```", result, re.DOTALL)
        if json_match:
            json_str = json_match.group(0).strip("```json").strip("```").strip()
            try:
                formatted = json.loads(json_str)
            except json.JSONDecodeError as e:
                print(json.dumps(formatted, indent=4, ensure_ascii=False))
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
        else:
            formatted = json.loads(result)
        
        return formatted
    
    async def split_product_fields(self, text: str, split_rules: str,sbys_required:str) -> dict:
        text = text.replace("规格型号：","")
        """商品字段拆分专用方法"""
        split_rules = [rule.strip() for rule in split_rules.split(";") if rule.strip()]
        
        # 动态生成字段模板
        # fields_template = ",\n".join([f'"{rule}": ""' for rule in split_rules])
        # print(fields_template)
        # 确保 sbys_required 和 split_rules 长度一致
        if len(sbys_required) != len(split_rules):
            raise ValueError("sbys_required 和 split_rules 长度不匹配")

        # 生成字段模板，并根据 sbys_required 设置是否必填
        fields_template = ",\n".join(
            [
                f'"{rule}": "非空"' if required == "1" else f'"{rule}": ""'
                for rule, required in zip(split_rules, sbys_required)
            ]
        )
        # print(fields_template)
        prompt = split_product_fields_prompt(text,fields_template)
        messages = [
            {"role": "system", "content": "你是一个海关商品归类专家，只返回JSON"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            # 使用豆包模型处理
            from app.ai_models import get_ai_model
            ai_model = get_ai_model("doubao")
            response = await ai_model.chat(messages)
            # await self.ai_model.close()
            # 清理响应内容
            json_str = response.replace("```json", "").replace("```", "").replace('非空', '').strip()
            result = json.loads(json_str)
            
            # 构建带序号的拆分结果
            split_result = {}
            result_values = []
            
            for idx, rule in enumerate(split_rules):
                key = f"{idx}:{rule}"
                value = result.get(rule, "")
                split_result[key] = value
                result_values.append(str(value) if value else "")
            print(result_values)
            return {
                "result": "|".join(result_values).rstrip("|"),
                "split_result": result
            }
            
        except Exception as e:
            return {"error": f"处理失败: {str(e)}"}
    def clean_json_string(self,json_str):
        """
        移除控制字符和特殊字符，但保留中英文标点符号
        """
        # 保留的标点符号（中文和英文）
        keep_punctuation = r'，。、；：！？「」『』（）《》【】‘’“”.,!?;:\'\"()<>\[\]{}'
        
        # 1. 移除所有控制字符（ASCII 0-31）
        cleaned = re.sub(r'[\x00-\x1F\x7F]', '', json_str)
        
        # 2. 移除非字母、数字、中文、空格和保留标点的字符
        cleaned = re.sub(
            fr'[^\w\s\u4e00-\u9fff{keep_punctuation}]', 
            '', 
            cleaned
        )
        
        # 3. 将连续多个空格替换为单个空格（保留换行符）
        cleaned = re.sub(r'[ \t]+', ' ', cleaned)
        
        return cleaned
    async def verify_json(self,content):
        """校验JSON数据"""
        prompt = verify_json_prompt(content)
        messages = [
            {"role": "system", "content": "你是一个资深的外贸专家，对外贸单据数据进行多单据字段比对和业务逻辑一致性校验，仅对指定规则字段进行校验，严格避免校验未提供的字段或无关字段"},
            {"role": "user", "content": prompt}
        ]
        try:
            # 使用豆包模型处理
            from app.ai_models import get_ai_model
            ai_model = get_ai_model("doubao")
            response = await ai_model.chat(messages)
            print("单个JSON的校验结果:",response)
            # await self.ai_model.close()
            # 清理响应内容
            json_str = response.replace("```json", "").replace("```", "").replace("'","").strip()
            json_str = self.clean_json_string(json_str)
            result = json.loads(json_str)
            
            return result
            
        except Exception as e:
            return {"error": f"处理失败: {str(e)}"}
    
    async def verify_list_json(self,json_list):
        """校验多个JSON数据"""
        prompt = verify_list_json_prompt(json_list)
        messages = [
            {"role": "system", "content": "你是一个资深的外贸专家，对外贸单据数据进行多单据字段比对和业务逻辑一致性校验，仅对指定规则字段进行校验，严格避免校验未提供的字段或无关字段"},
            {"role": "user", "content": prompt}
        ]
        try:
            # 使用豆包模型处理
            from app.ai_models import get_ai_model
            ai_model = get_ai_model("doubao")
            response = await ai_model.chat(messages)
            print("多个JSON之间的校验结果：",response)
            # await self.ai_model.close()
            # 清理响应内容
            json_str = response.replace("```json", "").replace("```", "").replace("'","").strip()
            json_str = self.clean_json_string(json_str)
            result = json.loads(json_str)
            
            return result
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return {"error": f"处理失败: {str(e)}"}
        
    async def special_field_handler(self,sys_rules,merged):
        prompt = format_by_tradeName(sys_rules,merged)
        print("模型修改前的结果：",merged)
        if prompt:
            messages = [
                {"role": "system", "content": "你是一个资深的数据处理专家，按照用户输入要求对JSON的内容进行修改，只按照修改要求的字段进行修改，其他字段不要有任何改动"},
                {"role": "user", "content": prompt}
            ]
            # 使用豆包模型处理
            from app.ai_models import get_ai_model
            ai_model = get_ai_model("doubao")
            response = await ai_model.chat(messages)
            print("模型修改后的结果：",response)
            # await self.ai_model.close()
            # 清理响应内容
            json_str = response.replace("```json", "").replace("```", "").replace("'","\"").strip()
            # new_json_str = json.dumps(json_str) 
            result = json.loads(json_str)
            print("JSON转换的内容：",result)
            return result
        else:
            return merged
        


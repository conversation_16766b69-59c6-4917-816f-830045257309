import logging
import json
from typing import Dict, Any, Optional
from .ai_models.base import BaseAIModel
from .ai_models.prompts import CLASSIFY_PROMPT, format_by_type,VERIFY_PROMPT,MERGE_PROMPT
from .config import ENABLE_DOCUMENT_CHECK
from collections import defaultdict
class ContentProcessor:
    """文档内容处理器"""
    
    def __init__(self, ai_model: BaseAIModel):
        self.ai_model = ai_model
    
    async def process_content(self, content: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理文档内容
        
        Args:
            content: 提取的文本内容
            file_info: 文件信息，包含文件名、类型等
            
        Returns:
            Dict[str, Any]: 处理结果
            
        处理过程：
            1、使用大模型对文档进行分类
            2、根据分类的结果进行格式化
            3、对格式化后的内容使用大模型进行业务逻辑的校验
        """
        try:
            print("文件名称是：", file_info.get("file_name"))
            if "发票" in file_info.get("file_name"):
                _doc_type = "出口发票"
            elif "箱单" in file_info.get("file_name"):
                _doc_type = "出口箱单"
            elif "证书" in file_info.get("file_name"):
                _doc_type = "出口证书"
            elif "入货" in file_info.get("file_name"):
                _doc_type = "入货通知"
            elif "舱单" in file_info.get("file_name"):
                _doc_type = "舱单样本"
            elif "合同" in file_info.get("file_name"):
                _doc_type = "出口合同"
            elif "报关单" in file_info.get("file_name"):
                _doc_type = "报关单"
            else:
                _doc_type = "其他"
                # 1、根据文件内容进行文档分类
                classify_prompt = CLASSIFY_PROMPT.format(text=content)
                classify_message = [
                    {"role": "system", "content": "你是一个严谨的文档类型分类助手，只输出用户要求的内容，不要添加其他文字"},
                    {"role": "user", "content": classify_prompt}
                    ]

                category = await self.ai_model.chat(classify_message)
                print("分类结果是：", category)
                # 对于第一步分类，返回纯文本结果
                # 清理结果文本，提取关键文档类型
                valid_types = ["出口发票", "出口箱单", "出口证书", "入货通知", "舱单样本", "报关单","出口合同", "其他"]
                for doc_type in valid_types:
                    if doc_type in category:
                        _doc_type = doc_type
            print(f"文档类型: {_doc_type}")

            # 2、根据文档类型进行数据格式化
            format_prompt = format_by_type(content, _doc_type)
            format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
            format_message = [
                {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的 JSON，不要添加其他文字"},
                {"role": "user", "content": format_prompt}
                ]
            result = await self.ai_model.chat(format_message)
            print("格式化结果是：", result)
            if not result:
                return {"error": "内容格式化失败"}
            
            result = result.replace("```json", "").replace("```", "")
            formatted = json.loads(result)
            print("格式化结果是：", formatted)
            formatted.update(file_info)
            print("合并文件信息后结果是：", formatted)
            # 如果启用了文档检查，则对格式化后的内容使用大模型进行业务逻辑的校验
            if ENABLE_DOCUMENT_CHECK:
                #3、对格式化后的内容使用大模型进行业务逻辑的校验
                verify_prompt = VERIFY_PROMPT.format(text=result)
                verify_prompt = " ".join(line.strip() for line in verify_prompt.splitlines() if line.strip())
                verify_message = [
                    {"role": "system", "content": "你是一个严谨的校验助手，只输出用户要求的JSON，不要添加其他文字"},
                    {"role": "user", "content": verify_prompt}
                    ]
                verify_result = await self.ai_model.chat(verify_message)
                print("校验结果是：", verify_result)
                verify_result = json.loads(verify_result)
                formatted.update(verify_result)
            return formatted
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"内容处理失败: {str(e)}")
            return {"error": str(e)} 

    async def merge_contents(self, contents):
        """合并多个文档的内容
        
        Args:
            contents: 多个文档的内容
            
        Returns:合并的结果
        """
        format_prompt = MERGE_PROMPT.format(json.dumps(contents, indent=2, ensure_ascii=False))
        format_prompt = " ".join(line.strip() for line in format_prompt.splitlines() if line.strip())
        format_message = [
            {"role": "system", "content": "你是一个严谨的格式化助手，只输出用户要求的JSON，不要添加其他文字"},
            {"role": "user", "content": format_prompt}
            ]
        import time
        merge_start = time.perf_counter()
        result = await self.ai_model.chat(format_message)
        merge_end = time.perf_counter()
        print(f"调用大模型合并耗时: {merge_end - merge_start:.4f} 秒")
        
        print("格式化结果是：", result)
        if not result:
            return {"error": "内容格式化失败"}
        
        # result = result.replace("```json", "").replace("```", "")
        # # 将结果转换为json对象
        # formatted = json.loads(result)
        # print("格式化结果是：", formatted)
        import re
        json_match = re.search(r"```json.*?```", result, re.DOTALL)
        if json_match:
            json_str = json_match.group(0).strip("```json").strip("```").strip()
            print("json_str是：", json_str)
            try:
                formatted = json.loads(json_str)
                print("提取的JSON数据:")
                print(json.dumps(formatted, indent=4, ensure_ascii=False))
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
        else:
            formatted = json.loads(result)
        
        return formatted
    
    async def split_product_fields(self, text: str) -> dict:
        """商品字段拆分专用方法"""
        prompt = f"""
        # 任务：从商品描述提取结构化字段
        ## 输入文本：
        {text}
        
        ## 输出要求：
        {{
            "品牌类型": "",
            "出口享惠情况": "", 
            "用途": "",
            "材质": "",
            "品牌": "",
            "型号": "",
            "GTIN": "",
            "CAS": "",
            "其他": ""
        }}
        """
        
        messages = [
            {"role": "system", "content": "你是一个海关商品归类专家，只返回JSON"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            # 使用豆包模型处理
            from app.ai_models import get_ai_model
            ai_model = get_ai_model("doubao")
            response = await ai_model.chat(messages)
            
            # 清理响应内容
            json_str = response.replace("```json", "").replace("```", "").strip()
            return json.loads(json_str)
        except Exception as e:
            return {"error": f"处理失败: {str(e)}"}
    
    

import jieba
# 船代
SHIPPING_AGENT_LIST = [
    "外代",
"日照中联运通",
"中远代录",
"中远BGH",
"青港",
"中创",
"中港联(中和顺德)",
"东方海外",
"日照东方海外",
"日照外运",
"外运",
"联代",
"日照青港",
"中联运通",
"中集世联达（振华）",
"海丰",
"振华",
"远大内支线",
"日照中创",
"中远自录",
"华港",
"日照外代"
]
def fast_match_agent(name):
    for shipping_agent in SHIPPING_AGENT_LIST:
        if name == shipping_agent:
            return shipping_agent
    return None

def shipping_agent_handler(input_shipping_agent):
    # Step 1: 原始匹配
    fast_match = fast_match_agent(input_shipping_agent)
    if fast_match:
        return fast_match
    # Step 2: 拆词匹配
    words = [word for word in jieba.cut(input_shipping_agent) if len(word) > 1]
    if len(words) < 2:
        candidates = [words[-1]]  # 只有一个词时，只查最后一个
        return input_shipping_agent
    else:
        candidates = words[-2:]   # 正常情况查倒数两个
        match = fast_match_agent(candidates[1])
        if match:
            return match
        match = fast_match_agent([candidates[0]])
        if match:
            return match
        
    return input_shipping_agent

print(shipping_agent_handler("青岛港国际物流有限公司"))
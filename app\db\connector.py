import mysql.connector
from mysql.connector import pooling, Error
from typing import Optional, Dict, Any, Tuple
from app.config import to_dict

class MySQLConnector:
    """MySQL数据库连接管理器"""
    
    _pool: Optional[pooling.MySQLConnectionPool] = None
    
    @classmethod
    def initialize(cls):
        """初始化连接池"""
        if cls._pool is None:
            try:
                cls._pool = pooling.MySQLConnectionPool(
                    **to_dict()
                )
            except Error as e:
                raise ConnectionError(f"MySQL连接池初始化失败: {e}")
    
    @classmethod
    def get_connection(cls):
        """从连接池获取连接"""
        if cls._pool is None:
            cls.initialize()
        try:
            return cls._pool.get_connection()
        except Error as e:
            raise ConnectionError(f"获取数据库连接失败: {e}")
    
    @classmethod
    def close_all(cls):
        """关闭所有连接"""
        if cls._pool:
            cls._pool.close()
            cls._pool = None
            print("MySQL连接池已关闭")
import logging
from typing import Dict, Any, Optional
import os

class BaseFileProcessor:
    """文件处理器基类"""
    
    def __init__(self, ai_handler=None):
        self.ai_handler = ai_handler
    
    async def process(self, file_path: str) -> Dict[str, Any]:
        """处理文件的基本方法 (子类应该重写此方法)"""
        try:
            # 提取文本内容
            content = await self._extract_text(file_path)
            
            # AI处理
            result = await self.ai_model.process_content(content)
            
            # 确保返回JSON格式
            if not isinstance(result, dict):
                result = {"content": str(result)}
            
            # 添加文件元数据
            result["file_name"] = os.path.basename(file_path)
            
            return result
        except Exception as e:
            logging.error(f"文件处理失败: {str(e)}", exc_info=True)
            return {"error": str(e), "file_name": os.path.basename(file_path)}
    
    def _detect_file_type(self, file_path: str) -> Optional[str]:
        """检测文件类型"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logging.error(f"文件不存在: {file_path}")
                return None
            
            # 获取文件扩展名
            extension = os.path.splitext(file_path)[1].lower()

            # 尝试通过magic库判断文件类型
            try:
                import magic
                mime = magic.from_file(file_path, mime=True)
                
                # 基于MIME和扩展名判断文件类型
                if mime == "application/pdf" or extension == ".pdf":
                    return "pdf"
                elif mime.startswith("image/"):
                    return mime.split('/')[-1]
                elif "officedocument.wordprocessingml" in mime or extension == ".docx":
                    return "docx"
                elif "officedocument.spreadsheetml" in mime or extension in [".xlsx", ".xls"]:
                    return "xlsx" if extension == ".xlsx" else "xls"
                elif mime == "application/zip" or extension == ".zip":
                    return "zip"
                else:
                    logging.info(f"未识别的MIME类型: {mime}, 使用扩展名")
                    return extension[1:] if extension else "unknown"
                
            except ImportError:
                logging.warning("python-magic库未安装，使用扩展名判断文件类型")
                # 基于扩展名判断
                if extension == '.pdf':
                    return 'pdf'
                elif extension in ['.jpg', '.jpeg', '.png']:
                    return extension[1:]  # 去掉点
                elif extension == '.docx':
                    return 'docx'
                elif extension in ['.xlsx', '.xls']:
                    return 'xlsx' if extension == '.xlsx' else 'xls'
                elif extension == '.zip':
                    return 'zip'
                else:
                    return 'unknown'
                
        except Exception as e:
            logging.error(f"检测文件类型失败: {str(e)}", exc_info=True)
            return None 
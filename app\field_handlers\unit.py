# 计量单位
UNIT_MAP = {
    "001": "台",
    "002": "座",
    "003": "辆",
    "004": "艘",
    "005": "架",
    "006": "套",
    "007": "个",
    "008": "只",
    "009": "头",
    "010": "张",
    "011": "件",
    "012": "支",
    "013": "枝",
    "014": "根",
    "015": "条",
    "016": "把",
    "017": "块",
    "018": "卷",
    "019": "副",
    "020": "片",
    "021": "组",
    "022": "份",
    "023": "幅",
    "025": "双",
    "026": "对",
    "027": "棵",
    "028": "株",
    "029": "井",
    "030": "米",
    "031": "盘",
    "032": "平方米",
    "033": "立方米",
    "034": "筒",
    "035": "千克",
    "035": "公斤",
    "035": "kg",
    "035": "KG",
    "035": "Kg",
    "035": "kG",
    "036": "克",
    "037": "盆",
    "038": "万个",
    "039": "具",
    "040": "百副",
    "041": "百支",
    "042": "百把",
    "043": "百个",
    "044": "百片",
    "045": "刀",
    "046": "疋",
    "047": "公担",
    "048": "扇",
    "049": "百枝",
    "050": "千只",
    "051": "千块",
    "052": "千盒",
    "053": "千枝",
    "054": "千个",
    "055": "亿支",
    "056": "亿个",
    "057": "万套",
    "058": "千张",
    "059": "万张",
    "060": "千伏安",
    "061": "千瓦",
    "062": "千瓦时",
    "063": "千升",
    "067": "英尺",
    "070": "吨",
    "071": "长吨",
    "072": "短吨",
    "073": "司马担",
    "074": "司马斤",
    "075": "斤",
    "076": "磅",
    "077": "担",
    "078": "英担",
    "079": "短担",
    "080": "两",
    "081": "市担",
    "083": "盎司",
    "084": "克拉",
    "085": "市尺",
    "086": "码",
    "088": "英寸",
    "089": "寸",
    "095": "升",
    "096": "毫升",
    "097": "英加仑",
    "098": "美加仑",
    "099": "立方英尺",
    "101": "立方尺",
    "110": "平方码",
    "111": "平方英尺",
    "112": "平方尺",
    "115": "英制马力",
    "116": "公制马力",
    "118": "令",
    "120": "箱",
    "121": "批",
    "122": "罐",
    "123": "桶",
    "124": "扎",
    "125": "包",
    "126": "箩",
    "127": "打",
    "128": "筐",
    "129": "罗",
    "130": "匹",
    "131": "册",
    "132": "本",
    "133": "发",
    "134": "枚",
    "135": "捆",
    "136": "袋",
    "139": "粒",
    "140": "盒",
    "141": "合",
    "142": "瓶",
    "143": "千支",
    "144": "万双",
    "145": "万粒",
    "146": "千粒",
    "147": "千米",
    "148": "千英尺",
    "149": "百万贝可",
    "163": "部",
    "164": "亿株",
    "170": "人份",
}
# weight units
WEIGHT_UNITS = {
    "035": "千克",
    "036": "克",
    "070": "吨",
    "071": "长吨",
    "072": "短吨",
    "073": "司马担",
    "074": "司马斤",
    "075": "斤",
    "076": "磅",
    "077": "担",
    "078": "英担",
    "079": "短担",
    "080": "两",
    "081": "市担",
    "083": "盎司",
    "084": "克拉"
}

# 判断是否是重量单位
def is_weight_unit(unit):
    return unit in WEIGHT_UNITS.keys() or unit in WEIGHT_UNITS.values()


def unit_handler(input_unit):
    for code,unit in UNIT_MAP.items():
        if input_unit == unit or input_unit == code:
            return code,unit
    return None,input_unit
import jieba,os
STATION_LIST = [
    "长荣场站",
    "东港场站",
    "大亚场站",
    "泛联场站",
    "港捷丰场站",
    "地中海(捷运)场站",
    "港联海场站",
    "港联华场站",
    "港联捷(怡之航)场站",
    "港联荣场站",
    "港联欣场站",
    "捷丰场站",
    "锦恒场站",
    "陆海场站",
    "珉钧场站",
    "胜狮场站",
    "世腾克运场站",
    "神州场站",
    "外运场站",
    "新霸达",
    "中创场站",
    "吉永场站",
    "即墨陆港",
    "大港",
    "神州董家口",
    "济铁黄岛物流园",
    "陆海日照",
    "港连运场站",
    "中创日照场站",
    "日照外运场站",
    "招商场站(保税)",
    "招商场站",
    "外运弘治场站",
    "CFS四期场站",
    "凯镖场站",
    "黄岛港站提单",
    "胶州港提单",
    "济南港提单"
]
# 加载自定义词典（绝对路径或相对路径）
# 获取当前脚本的绝对路径目录
current_dir = os.path.dirname(os.path.abspath(__file__))
dict_path = os.path.join(current_dir, "station_dict.txt")
jieba.load_userdict(dict_path)  
def preprocess(text):
    suffixes = ["场站","提单"]
    for suffix in suffixes:
        text = text.replace(suffix, "")
    return text

def fast_match_station(name):
    name_clean = preprocess(name)
    for station in STATION_LIST:
        station_clean = preprocess(station)
        if name == station or name == station_clean or name_clean == station or name_clean == station_clean:
            return station
    return None

def station_handler(input_station):
    match = fast_match_station(input_station)
    if match:
        return match
    # 拆词
    words = [word for word in jieba.cut(input_station) if len(word) > 1]
    print(words)
    if len(words) < 2:
        candidates = [words[-1]]  # 只有一个词时，只查最后一个
        return input_station
    else:
        candidates = words[-2:]   # 正常情况查倒数两个
        match = fast_match_station(candidates[0])
        if match:
            return match
        match = fast_match_station([candidates[1]])
        if match:
            return match
    return input_station



print(station_handler("中创远达"))

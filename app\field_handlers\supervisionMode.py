# 监管方式
SUPERVISION_MODE = {
    "0110":"一般贸易",
"0130":"易货贸易",
"0200":"料件销毁",
"0214":"来料加工",
"0245":"来料料件内销",
"0255":"来料深加工",
"0258":"来料余料结转",
"0265":"来料料件复出",
"0300":"来料料件退换",
"0314":"加工专用油",
"0320":"不作价设备",
"0345":"来料成品减免",
"0400":"边角料销毁",
"0420":"加工贸易设备",
"0444":"保区进料成品",
"0445":"保区来料成品",
"0446":"加工设备内销",
"0456":"加工设备结转",
"0466":"加工设备退运",
"0500":"减免设备结转",
"0513":"补偿贸易",
"0544":"保区进料料件",
"0545":"保区来料料件",
"0615":"进料对口",
"0642":"进料以产顶进",
"0644":"进料料件内销",
"0654":"进料深加工",
"0657":"进料余料结转",
"0664":"进料料件复出",
"0700":"进料料件退换",
"0715":"进料非对口",
"0744":"进料成品减免",
"0815":"低值辅料",
"0844":"进料边角料内销",
"0845":"来料边角料内销",
"0864":"进料边角料复出",
"0865":"来料边角料复出",
"1039":"市场采购",
"1139":"国轮油物料",
"1200":"保税间货物",
"1210":"保税电商",
"1215":"保税工厂",
"1233":"保税仓库货物",
"1234":"保税区仓储转口",
"1239":"保税电商A",
"1300":"修理物品",
"1371":"保税维修",
"1427":"出料加工",
"1500":"租赁不满1年",
"1523":"租赁贸易",
"1616":"寄售代销",
"1741":"免税品",
"1831":"外汇商品",
"2025":"合资合作设备",
"2210":"对外投资",
"2225":"外资设备物品",
"2439":"常驻机构公用",
"2600":"暂时进出货物",
"2700":"展览品",
"2939":"陈列样品",
"3010":"货样广告品",
"3100":"无代价抵偿",
"3339":"其他进出口免费",
"3410":"承包工程进口",
"3422":"对外承包出口",
"3511":"援助物资",
"3611":"无偿军援",
"3612":"捐赠物资",
"3910":"军事装备",
"4019":"边境小额",
"4039":"对台小额",
"4139":"对台小额商品交易市场",
"4200":"驻外机构运回",
"4239":"驻外机构购进",
"4400":"来料成品退换",
"4500":"直接退运",
"4539":"进口溢误卸",
"4561":"退运货物",
"4600":"进料成品退换",
"5000":"料件进出区",
"5010":"特殊区域研发货物",
"5014":"区内来料加工",
"5015":"区内进料加工货物",
"5033":"区内仓储货物",
"5034":"区内物流货物",
"5072":"区内保税展品",
"5073":"区内国际中转",
"5100":"成品进出区",
"5300":"设备进出区",
"5335":"境外设备进区",
"5361":"区内设备退运",
"6033":"物流中心进出境货物",
"6072":"中心保税展品",
"6073":"中心国际中转",
"9500":"特许权使用费后续征税",
"9600":"内贸货物跨境运输",
"9610":"电子商务",
"9639":"海关处理货物",
"9700":"后续补税",
"9710":"跨境电商B2B直接出口",
"9739":"其他贸易",
"9800":"租赁征税",
"9810":"跨境电商出口海外仓",
"9839":"留赠转卖物品",
"9900":"其他",
}

def supervision_mode_handler(input_supervision_mode):
    if "一般" in input_supervision_mode:
        return "0110","一般贸易"
    for code,mode in SUPERVISION_MODE.items():
        if input_supervision_mode == mode or input_supervision_mode == code:
            return code,mode
    return None,input_supervision_mode

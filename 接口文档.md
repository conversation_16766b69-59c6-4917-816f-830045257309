### 接口文档：上传并处理多个文件

#### 接口地址

`POST http:10.10.70.110:8000/upload`

#### 功能描述

该接口用于上传多个文件，支持压缩文件，并对文件内容进行OCR识别、格式化处理以及业务逻辑校验，最终返回合并后的结果。

#### 请求参数

##### 表单参数


| 参数名         | 类型 | 必填 | 默认值   | 描述                     |
| -------------- | ---- | ---- | -------- | ------------------------ |
| model_provider | str  | 否   | `doubao` | 指定使用的模型提供商名称 |
| files          | List | 是   |          | 上传的文件               |

#### 响应数据

##### 成功响应

HTTP状态码：`200 OK`

响应体：

```
{
  "detail_ocr": [
    {
      "file_name": "example.pdf",
      "content": "OCR识别的文本内容"
    }
  ],
  "detail_format": [
    {
      "file_name": "example.pdf",
      "content": "格式化后的文本内容"
    }
  ],
  "result": "合并后的去重结果"
}
```


| 字段名          | 类型       | 描述                                                               |
| --------------- | ---------- | ------------------------------------------------------------------ |
| `detail_ocr`    | List[Dict] | OCR识别的详细结果，每个文件对应一个字典，包含文件名和OCR内容       |
| `detail_format` | List[Dict] | 格式化处理的详细结果，每个文件对应一个字典，包含文件名和格式化内容 |
| `result`        | List[Dict] | 合并后的去重结果                                                   |

#### 示例

```
const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
```

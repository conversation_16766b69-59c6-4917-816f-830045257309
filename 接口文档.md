# 1、上传并处理多个文件

#### 接口地址

`POST http:10.10.70.110:8000/upload`

#### 功能描述

该接口用于上传多个文件，支持压缩文件，并对文件内容进行OCR识别、格式化处理以及业务逻辑校验，最终返回合并后的结果。

#### 请求参数

##### 表单参数


| 参数名         | 类型 | 必填 | 默认值   | 描述                     |
| -------------- | ---- | ---- | -------- | ------------------------ |
| model_provider | str  | 否   | `doubao` | 指定使用的模型提供商名称 |
| files          | List | 是   |          | 上传的文件               |
| ie_flag          | str | 否   |   `E`      | I:进口，E:出口              |
| enable_processing  | bool | 否   |    False      | 是否需要进行英文字段处理，默认不处理               |
| opt_unit_name          | str | 否   |          | 境内收发货人名称               |
| sys_rules          | str | 否   |          | 特殊规则信息               |

#### 响应数据

##### 成功响应

HTTP状态码：`200 OK`

响应体：

```
{
  "detail_ocr": [
    {
      "file_name": "example.pdf",
      "content": "OCR识别的文本内容"
    }
  ],
  "detail_format": [
    {
      "file_name": "example.pdf",
      "content": "格式化后的文本内容"
    }
  ],
  "result": "合并后的去重结果",
  "result_logic":"处理后的最终结果"
}
```


| 字段名          | 类型       | 描述                                                               |
| --------------- | ---------- | ------------------------------------------------------------------ |
| `detail_ocr`    | List[Dict] | OCR识别的详细结果，每个文件对应一个字典，包含文件名和OCR内容       |
| `detail_format` | List[Dict] | 格式化处理的详细结果，每个文件对应一个字典，包含文件名和格式化内容 |
| `result`        | List[Dict] | 合并后的去重结果                                                   |
| `result_logic`        | List[Dict] | 按照单一窗口规则处理之后的结果                                                   |

#### 示例

```
const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
```

# 2、申报要素智能填写

#### 接口地址

`POST http:10.10.70.110:8000/api/smart-fill`

#### 功能描述

该接口申报要素智能识别填写生成

#### 请求参数

##### 表单参数


| 参数名      | 类型 | 必填 | 示例                                                                                                                                                        | 描述             |
| ----------- | ---- | ---- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------- |
| text        | str  | 是   | 盛放调料的盒子，PC+\nABS+PP 无型号 无牌 不享惠                                                                                                              | 申报要素描述     |
| split_rules | str  | 是   | 0:品牌类型;1:出口享惠情况;2:用途;3:加工工艺;4:是否配定剂量;5:是否零售包装;6:成分;7:来源;8:品牌（中文或外文名称）;9:型号;10:包装规格;11:GTIN;12:CAS;13:其他; | 申报要素拆分规则 |
| sbysRequired        | str  | 是   | 1｜1｜1｜0｜0 | 申报要素是否必填     |

#### 响应数据

##### 成功响应

HTTP状态码：`200 OK`

响应体：

```
{
    "result": "无牌|不享惠|盛放调料||||PC+ABS+PP|||无型号||||",
    "split_result": {
        "0:品牌类型": "无牌",
        "1:出口享惠情况": "不享惠",
        "2:用途": "盛放调料",
        "3:加工工艺": "",
        "4:是否配定剂量": "",
        "5:是否零售包装": "",
        "6:成分": "PC+ABS+PP",
        "7:来源": "",
        "8:品牌（中文或外文名称）": "",
        "9:型号": "无型号",
        "10:包装规格": "",
        "11:GTIN": "",
        "12:CAS": "",
        "13:其他": ""
    }
}
```


| 字段名         | 类型 | 描述         |
| -------------- | ---- | ------------ |
| `result`       | str  | 内容合并结果 |
| `split_result` | Dict | 拆分后的结果 |

#### 示例

# 3、 文档逻辑校验

#### 接口地址

`POST http:10.10.70.110:8000/api/verify`

#### 功能描述

该接口是对文档内容进行逻辑校验对

#### 请求参数

##### 表单参数


| 参数名      | 类型 | 必填 | 示例                                                                                                                                                        | 描述             |
| ----------- | ---- | ---- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------- |
| detail_format_list        | List  | 是   |                                                                                                               | 格式化的识别结果     |


#### 响应数据

##### 成功响应

HTTP状态码：`200 OK`

响应体：

```
[
	{
		"error": "",
		"file_name": "BGD ETA TS2506ETA003  B.xlsx"
	},
	{
		"error": "",
		"file_name": "INV TS2506ETA003 BG  B.xlsx"
	},
	{
		"error": "商品信息中仅一项商品，但总数量、净重、毛重均为多个数值相加形式，与商品信息项数不匹配，无法进行准确校验",
		"file_name": "PL TS2506ETA003 BG  B.xlsx"
	},
	{
		"error": "1. 境内发货人信息不一致，BGD ETA TS2506ETA003  B.xlsx中为“苏州方博贸易有限公司3215960NJ5”，INV TS2506ETA003 BG  B.xlsx和PL TS2506ETA003 BG  B.xlsx中为“SUZHOUTOLSENTOOLSCO.,LTD NO.198,HUASHANROAD,JINFENGTOWN,ZHANGJIAGANGCITY,JIANGSUPROVINCE,P.R.OFCHINA”。\n2. 商品信息中，BGD ETA TS2506ETA003  B.xlsx包含多种商品，而INV TS2506ETA003 BG  B.xlsx仅列出一种商品“9PCSBI-METALHOLESAWSET”，两者商品项数不一致。\n3. 商品信息中，BGD ETA TS2506ETA003  B.xlsx包含多种商品，而PL TS2506ETA003 BG  B.xlsx仅列出一种商品“9PCSBI-METALHOLESAWSET”，两者商品项数不一致。\n4. BGD ETA TS2506ETA003  B.xlsx中未提供各项商品的净重、毛重，无法与PL TS2506ETA003 BG  B.xlsx中的净重“231+57+237+197+212+2+23+185+83+18+38+10+44+12+1285+31+76+416”、毛重“209+51+217+191+203+1+22+172+78+16+36+8+43+11+1144+29+71+394”进行单项匹配及校验毛重>净重的规则。\n5. INV TS2506ETA003 BG  B.xlsx中商品“9PCSBI-METALHOLESAWSET”单价为“USD12.919999999999998”与BGD ETA TS2506ETA003  B.xlsx中“9件套孔锯”单价“12.92”存在细微差异。",
		"file_name": "校验结果"
	}
]
```


| 字段名         | 类型 | 描述         |
| -------------- | ---- | ------------ |
| `file_name`       | str  | 文件名 |
| `error` | str | 错误信息 |

#### 示例

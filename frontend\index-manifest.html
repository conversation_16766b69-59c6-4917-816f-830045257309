<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>外贸单据处理系统</title>
    <link href="/static/js/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/js/all.min.css">
    <script src="/static/js/manifest.js" defer></script>
    <script src="/static/js/crypto-js.min.js" defer></script>
    <style>
        :root {
            --primary-color: #4a6baf;
            --secondary-color: #5a6268;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            background-color: #f5f8fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .page-header {
            padding: 1rem 0;
            background: linear-gradient(135deg, var(--primary-color), #2c3e50);
            color: white;
            margin-bottom: 1rem;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .card:hover {
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }
        
        .results-container {
            min-height: 500px;
        }
        
        #progressList {
            max-height: 250px;
            overflow-y: auto;
        }
        
        .sticky-top {
            top: 1rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #3a5999;
            border-color: #3a5999;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(74, 107, 175, 0.25);
        }
        
        .list-group-item {
            border-left: none;
            border-right: none;
        }
        
        /* 美化JSON显示 */
        .json-viewer {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .results-container {
                margin-top: 20px;
            }
            
            .page-header {
                padding: 1rem 0;
            }
        }
        
        /* 表格样式美化 */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table th {
            background-color: #eef2f7;
            color: #495057;
            font-weight: 600;
        }
        
        .table-bordered th,
        .table-bordered td {
            border: 1px solid #e9ecef;
        }
        
        /* 进度条样式 */
        .progress {
            height: 8px;
            border-radius: 4px;
        }
        
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
<div class="page-header">
    <div class="container-fluid">
        <h2 class="text-center mb-0"><i class="fas fa-file-import me-2"></i>外贸单据处理系统</h2>
        <p class="text-center text-light mt-1 mb-0 small">智能识别、自动分类、结构化输出</p>
    </div>
</div>

<div class="container-fluid">    
    <div class="row">
        <!-- 左侧 - 上传区域 -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-upload me-2"></i>文件上传
                </div>
                <div class="card-body">
                    <form id="upload-form" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label for="files" class="form-label fw-bold">
                                <i class="fas fa-file-alt me-1"></i>选择文件:
                            </label>
                            <input type="file" id="files" name="files" multiple class="form-control" required>
                            <small class="text-muted">支持PDF、Word、Excel、图片等格式</small>
                        </div>
                        
                        <div class="form-group mb-4">
                            <label for="model-provider" class="form-label fw-bold">
                                <i class="fas fa-robot me-1"></i>选择AI模型:
                            </label>
                            <select class="form-select" id="model-provider" name="model_provider">
                                <!-- 模型选项将由JavaScript动态加载 -->
                            </select>
                            <small class="text-muted">选择合适的AI模型处理您的文档</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 py-2">
                            <i class="fas fa-cog me-1"></i>上传并处理
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- 处理状态展示 -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <i class="fas fa-tasks me-2"></i>处理进度
                </div>
                <ul id="progressList" class="list-group list-group-flush"></ul>
            </div>
        </div>
        
        <!-- 右侧 - 结果展示区域 -->
        <div class="col-md-8 results-container">
            <div class="card sticky-top">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-clipboard-list me-2"></i>处理结果
                </div>
                <div class="card-body">
                    <div id="results" class="mt-4">
                        <div id="jsonOutput" class="bg-light p-3 rounded">
                            <!-- 表格结果展示区域 -->
                            <div id="resultTable"></div>
                            <form id="resultForm" class="row g-3">
                                <!-- 基础信息 -->
                                <h5 class="col-12 mt-3 mb-2">基础信息</h5>
                                <div class="col-md-4">
                                    <label class="form-label">境内发货人</label>
                                    <input type="text" class="form-control form-control-sm" name="境内发货人" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">境外收货人</label>
                                    <input type="text" class="form-control form-control-sm" name="境外收货人" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">发票号</label>
                                    <input type="text" class="form-control" name="发票号" readonly>
                                </div>
                                
                                <!-- 运输信息 -->
                                <h5 class="col-12 mt-3 mb-2">运输信息</h5>
                                <div class="col-md-3">
                                    <label class="form-label">运抵国</label>
                                    <input type="text" class="form-control" name="运抵国" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">成交方式</label>
                                    <input type="text" class="form-control" name="成交方式" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">贸易国</label>
                                    <input type="text" class="form-control" name="贸易国" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">指运港</label>
                                    <input type="text" class="form-control" name="指运港" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">运输方式</label>
                                    <input type="text" class="form-control" name="运输方式" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">运输工具及航次</label>
                                    <input type="text" class="form-control" name="运输工具及航次" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">离境口岸</label>
                                    <input type="text" class="form-control" name="离境口岸" readonly>
                                </div>
                                
                                <!-- 重量和包装信息 -->
                                <h5 class="col-12 mt-3 mb-2">重量和包装信息</h5>
                                <div class="col-md-3">
                                    <label class="form-label">总数量</label>
                                    <input type="text" class="form-control" name="总数量" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">净重</label>
                                    <input type="text" class="form-control" name="净重" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">毛重</label>
                                    <input type="text" class="form-control" name="毛重" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">包装种类</label>
                                    <input type="text" class="form-control" name="包装种类" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">件数</label>
                                    <input type="text" class="form-control" name="件数" readonly>
                                </div>
                                
                                <!-- 费用信息 -->
                                <h5 class="col-12 mt-3 mb-2">费用信息</h5>
                                <div class="col-md-4">
                                    <label class="form-label">运费</label>
                                    <input type="text" class="form-control" name="运费" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">保费</label>
                                    <input type="text" class="form-control" name="保费" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">杂费</label>
                                    <input type="text" class="form-control" name="杂费" readonly>
                                </div>
                                
                                <!-- 单证信息 -->
                                <h5 class="col-12 mt-3 mb-2">单证信息</h5>
                                <div class="col-md-4">
                                    <label class="form-label">提运单号</label>
                                    <input type="text" class="form-control" name="提运单号" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">船名/航次</label>
                                    <input type="text" class="form-control" name="船名/航次" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">场站</label>
                                    <input type="text" class="form-control" name="场站" readonly>
                                </div>
                                
                                <!-- 申报信息 -->
                                <h5 class="col-12 mt-3 mb-2">申报信息</h5>
                                <div class="col-md-3">
                                    <label class="form-label">申报地海关</label>
                                    <input type="text" class="form-control" name="申报地海关" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">出境关别</label>
                                    <input type="text" class="form-control" name="出境关别" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">出口日期</label>
                                    <input type="text" class="form-control" name="出口日期" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">申报日期</label>
                                    <input type="text" class="form-control" name="申报日期" readonly>
                                </div>
                                
                                <!-- 监管信息 -->
                                <h5 class="col-12 mt-3 mb-2">监管信息</h5>
                                <div class="col-md-3">
                                    <label class="form-label">监管方式</label>
                                    <input type="text" class="form-control" name="监管方式" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">征免性质</label>
                                    <input type="text" class="form-control" name="征免性质" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">许可证号</label>
                                    <input type="text" class="form-control" name="许可证号" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">合同号</label>
                                    <input type="text" class="form-control" name="合同号" readonly>
                                </div>
                                
                                <!-- 其他信息 -->
                                <h5 class="col-12 mt-3 mb-2">其他信息</h5>
                                <div class="col-md-3">
                                    <label class="form-label">协定享惠</label>
                                    <input type="text" class="form-control" name="协定享惠" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">备案号</label>
                                    <input type="text" class="form-control" name="备案号" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">随附单证代码E</label>
                                    <input type="text" class="form-control" name="随附单证代码E" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">产品资质203</label>
                                    <input type="text" class="form-control" name="产品资质203" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">随附单据文件类别</label>
                                    <input type="text" class="form-control" name="随附单据文件类别" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">商品编码</label>
                                    <input type="text" class="form-control" name="商品编码" readonly>
                                </div>
                                <div class="col-12">
                                    <label class="form-label">有效截止日期</label>
                                    <input type="text" class="form-control" name="有效截止日期" readonly>
                                </div>
                                
                                <!-- 商品信息表格 -->
                                <h5 class="col-12 mt-3 mb-2">商品信息</h5>
                                <div class="col-12">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped" id="goodsTable">
                                            <thead>
                                                <tr>
                                                    <th>商品名称</th>
                                                    <th>型号</th>
                                                    <th>数量</th>
                                                    <th>单价</th>
                                                    <th>总价</th>
                                                    <th>商品编号</th>
                                                    <th>原产国</th>
                                                    <th>最终目的国</th>
                                                    <th>境内货源地</th>
                                                    <th>征免</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <!-- 错误信息 -->
                                <div class="col-12">
                                    <div class="alert alert-danger" id="errorInfo" style="display: none;"></div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer class="mt-5 pt-4 pb-4 text-center text-muted">
        <small>&copy; 2025 外贸单据处理系统 - 基于智能模型处理单据的解决方案</small>
    </footer>
</div>

<script src="/static/js/axios.min.js"></script>
<script>
const API_BASE = 'http://localhost:8000';

// 初始化上传处理
(async function() {
    const form = document.getElementById('upload-form');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const fileInput = document.getElementById('files');
        
        // 确保modelProvider有值，默认为'ollama'
        let modelProvider = document.getElementById('model-provider').value;
        if (!modelProvider) {
            console.warn("模型未选择，使用默认模型Ollama");
            modelProvider = 'ollama';
        }
        
        console.log("使用模型:", modelProvider);
        
        if (fileInput.files.length === 0) {
            showAlert('请选择至少一个文件', 'warning');
            return;
        }
        
        const formData = new FormData();
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('files', fileInput.files[i]);
        }
        
        // 添加模型提供商参数
        formData.append('model_provider', modelProvider);
        
        try {
            // 清空进度区域
            clearProgressList();
            
            // 添加每个文件到进度列表
            for (let i = 0; i < fileInput.files.length; i++) {
                addProgressItem(fileInput.files[i].name, '等待处理');
            }
            
            // 显示处理中状态
            const processingLi = document.createElement('li');
            processingLi.className = 'list-group-item list-group-item-info fade-in';
            processingLi.id = 'processing-status';
            processingLi.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>正在上传处理，请稍候...`;
            document.getElementById('progressList').appendChild(processingLi);
            
            const API_KEY = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
            // 获取当前时间戳
            const timestamp = Date.now().toString();
            // 计算签名
            const stringSignTemp = API_KEY + timestamp;
            const hash = CryptoJS.MD5(stringSignTemp);
            const sign = hash.toString(CryptoJS.enc.Hex).toUpperCase();

            // 将时间戳和签名添加到请求头中
            const headers = new Headers();
            headers.append("timestamp", timestamp);
            headers.append("sign", sign);
            const response = await fetch('/upload-manifest', {
                method: 'POST',
                body: formData,
                headers: headers,
            });
            
            // 请求完成后只移除处理中的状态消息
            const processingStatus = document.getElementById('processing-status');
            if (processingStatus) {
                processingStatus.remove();
            }
            
            if (!response.ok) {
                throw new Error(`上传失败: ${response.status}`);
            }
            
            const result = await response.json();
            displayResults(result);
        } catch (error) {
            console.error('上传失败:', error);
            // 移除处理中的状态消息
            const processingStatus = document.getElementById('processing-status');
            if (processingStatus) {
                processingStatus.remove();
            }
            updateProgress(`<i class="fas fa-times-circle me-2"></i>上传失败: ${error.message}`, 'danger');
            showAlert('上传失败: ' + error.message, 'danger');
        }
    });
})();

// 显示警告/提示信息
function showAlert(message, type = 'warning') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : 
                          type === 'danger' ? 'times-circle' : 
                          type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到表单后面
    const form = document.getElementById('upload-form');
    form.parentNode.insertBefore(alertDiv, form.nextSibling);
    
    // 5秒后自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// 添加处理进度项
function addProgressItem(filename, status) {
    const li = document.createElement('li');
    li.className = 'list-group-item d-flex justify-content-between align-items-center fade-in';
    
    // 获取文件图标
    const fileExt = filename.split('.').pop().toLowerCase();
    const fileIcon = getFileIcon(fileExt);
    
    li.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${fileIcon} me-2"></i>
            <span class="text-truncate" style="max-width: 200px;">${filename}</span>
        </div>
        <span class="badge ${getBadgeClass(status)} ms-2">${status}</span>
    `;
    document.getElementById('progressList').appendChild(li);
}

// 根据文件扩展名获取合适的图标
function getFileIcon(extension) {
    const iconMap = {
        'pdf': 'fas fa-file-pdf text-danger',
        'doc': 'fas fa-file-word text-primary',
        'docx': 'fas fa-file-word text-primary',
        'xls': 'fas fa-file-excel text-success',
        'xlsx': 'fas fa-file-excel text-success',
        'ppt': 'fas fa-file-powerpoint text-warning',
        'pptx': 'fas fa-file-powerpoint text-warning',
        'jpg': 'fas fa-file-image text-info',
        'jpeg': 'fas fa-file-image text-info',
        'png': 'fas fa-file-image text-info',
        'zip': 'fas fa-file-archive text-secondary',
        'rar': 'fas fa-file-archive text-secondary'
    };
    
    return iconMap[extension] || 'fas fa-file text-muted';
}

// 获取状态对应的徽章类
function getBadgeClass(status) {
    switch(status.toLowerCase()) {
        case '等待处理': return 'bg-secondary';
        case '处理中': return 'bg-primary';
        case '处理完成': return 'bg-success';
        case '处理失败': return 'bg-danger';
        default: return 'bg-info';
    }
}

// 更新处理状态
function updateProgress(text, type = 'success') {
    const li = document.createElement('li');
    li.className = `list-group-item list-group-item-${type} fade-in`;
    li.innerHTML = text;
    document.getElementById('progressList').appendChild(li);
    
    // 自动滚动到底部
    const progressList = document.getElementById('progressList');
    progressList.scrollTop = progressList.scrollHeight;
}

// 直接在页面中添加默认选项
(function() {
    const modelSelect = document.getElementById('model-provider');
    if (modelSelect) {
        // 清空现有选项
        modelSelect.innerHTML = '';
        
        // 添加选项 - 注意顺序，Ollama放在第一位并设为选中
        const options = [
            { value: 'ollama', text: 'Ollama - 本地模型', icon: 'fa-server', selected: true },
            { value: 'deepseek', text: 'DeepSeek - 中文模型', icon: 'fa-language', selected: false },
            { value: 'doubao', text: '豆包AI - 视觉模型', icon: 'fa-eye', selected: false }
        ];
        
        options.forEach(opt => {
            const option = document.createElement('option');
            option.value = opt.value;
            option.innerHTML = `<i class="fas ${opt.icon}"></i> ${opt.text}`;
            option.selected = opt.selected;
            modelSelect.appendChild(option);
        });
        
        // 强制设置选中Ollama
        modelSelect.value = 'ollama';
    }
})();

function displayResults(result) {
    try {
        // 确保结果是数组
        const resultsArray = Array.isArray(result) ? result : [result];
        console.log("resultsArray:",resultsArray)
        // 过滤出有效的结果
        const validResults = resultsArray.filter(item => item && !item.error);
        
        // 更新文件处理状态
        const fileItems = document.querySelectorAll('#progressList li:not(#processing-status)');
        fileItems.forEach(item => {
            const filename = item.querySelector('.text-truncate').textContent;
            const matchingResult = resultsArray.find(r => r.original_filename === filename);
            const badge = item.querySelector('.badge');
            if (matchingResult) {
                if (matchingResult.error) {
                    badge.className = 'badge bg-danger ms-2';
                    badge.textContent = '处理失败';
                } else {
                    badge.className = 'badge bg-success ms-2';
                    badge.textContent = '处理完成';
                }
            }
        });
        
        if (validResults.length === 0) {
            updateProgress(`<i class="fas fa-exclamation-triangle me-2"></i>处理完成，但没有有效结果`, "warning");
            document.getElementById('resultTable').innerHTML = 
                `<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>无有效结果</div>`;
            return;
        }
        
        // 如果是单个JSON结果
        if (validResults.length === 1 && typeof validResults[0] === 'object') {
            // 转换为表格格式
            renderResultTable(validResults);
            updateProgress(`<i class="fas fa-check-circle me-2"></i>处理完成，结果已显示`, "success");
        } 
        // 如果是多个结果的数组
        else if (validResults.length > 1) {
            // 呈现多结果表格
            renderResultTable(validResults);
            updateProgress(`<i class="fas fa-check-circle me-2"></i>处理完成，显示 ${validResults.length} 个结果`, "success");
        }
        // 如果是文本结果
        else {
            document.getElementById('resultTable').innerHTML = 
                `<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>${validResults[0]}</div>`;
            updateProgress(`<i class="fas fa-check-circle me-2"></i>处理完成，结果已显示`, "success");
        }

        // 添加查看原始JSON按钮
        const debugBtn = document.createElement('button');
        debugBtn.className = 'btn btn-outline-secondary mt-3';
        debugBtn.innerHTML = '<i class="fas fa-code me-2"></i>查看原始数据';
        debugBtn.onclick = function() {
            const existingDebug = document.getElementById('debug-output');
            
            if (existingDebug) {
                existingDebug.remove();
                this.innerHTML = '<i class="fas fa-code me-2"></i>查看原始数据';
                return;
            }
            
            const debugOutput = document.createElement('pre');
            debugOutput.className = 'json-viewer mt-3 p-3 bg-light border rounded fade-in';
            debugOutput.textContent = JSON.stringify(result, null, 2);
            debugOutput.id = 'debug-output';
            
            document.getElementById('resultTable').appendChild(debugOutput);
            this.innerHTML = '<i class="fas fa-times me-2"></i>隐藏原始数据';
        };
        
        document.getElementById('resultTable').appendChild(debugBtn);
    } catch (error) {
        console.error('显示结果时出错:', error);
        updateProgress(`<i class="fas fa-times-circle me-2"></i>显示结果失败: ${error.message}`, 'danger');
    }
}

// 增强JSON格式化显示
function formatJsonValue(value) {
    if (value === null || value === undefined) {
        return '<span class="text-muted">-</span>';
    }
    
    if (typeof value === 'object') {
        try {
            const json = JSON.stringify(value);
            if (json.length > 100) {
                return `<span class="text-truncate d-inline-block" style="max-width: 300px;" title="${escapeHtml(json)}">${escapeHtml(json.substring(0, 100))}...</span>
                        <button class="btn btn-sm btn-link p-0 ms-2 json-expand" data-json='${escapeHtml(json)}'>查看全部</button>`;
            }
            return escapeHtml(json);
        } catch (e) {
            return String(value);
        }
    }
    
    if (typeof value === 'string') {
        if (value.length > 100) {
            return `<span class="text-truncate d-inline-block" style="max-width: 300px;" title="${escapeHtml(value)}">${escapeHtml(value.substring(0, 100))}...</span>
                    <button class="btn btn-sm btn-link p-0 ms-2 text-expand" data-text='${escapeHtml(value)}'>查看全部</button>`;
        }
    }
    
    return escapeHtml(String(value));
}

// HTML转义
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 渲染结果表格
function renderResultTable(data) {
    try {
        const container = document.getElementById('resultTable');
        
        // 创建表格头 - 合并所有数据项中的键
        const allKeys = new Set();
        data.forEach(item => {
            if (item && typeof item === 'object') {
                Object.keys(item).forEach(key => allKeys.add(key));
            }
        });
        
        // 排除一些不需要显示的技术性字段
        const excludeFields = ['original_text', 'error', 'file_path'];
        const displayKeys = [...allKeys].filter(key => !excludeFields.includes(key));
        
        // 构建表格HTML
        let html = `
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        ${displayKeys.map(key => `<th>${key}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
        `;
        
        // 添加表格行
        data.forEach(item => {
            if (item && typeof item === 'object') {
                html += '<tr>';
                displayKeys.forEach(key => {
                    const value = item[key];
                    const formattedValue = formatJsonValue(value);
                    html += `<td>${formattedValue}</td>`;
                });
                html += '</tr>';
            }
        });
        
        html += '</tbody></table>';
        container.innerHTML = html;
        
        // 添加展开JSON/Text的事件处理
        setTimeout(() => {
            document.querySelectorAll('.json-expand').forEach(btn => {
                btn.addEventListener('click', function() {
                    const jsonData = this.getAttribute('data-json');
                    showModal('JSON数据详情', `<pre class="json-viewer">${escapeHtml(JSON.stringify(JSON.parse(jsonData), null, 2))}</pre>`);
                });
            });
            
            document.querySelectorAll('.text-expand').forEach(btn => {
                btn.addEventListener('click', function() {
                    const text = this.getAttribute('data-text');
                    showModal('文本内容详情', `<div class="p-3">${escapeHtml(text)}</div>`);
                });
            });
        }, 100);
    } catch (error) {
        console.error('渲染表格失败:', error);
        document.getElementById('resultTable').innerHTML = 
            `<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>表格渲染失败: ${error.message}</div>`;
    }
}

// 显示模态框
function showModal(title, content) {
    // 检查是否已存在模态框
    let modal = document.getElementById('dataModal');
    if (modal) {
        document.body.removeChild(modal);
    }
    
    // 创建模态框
    modal = document.createElement('div');
    modal.id = 'dataModal';
    modal.className = 'modal fade show';
    modal.style.display = 'block';
    modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" onclick="closeModal()"></button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">关闭</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 添加关闭事件
    window.closeModal = function() {
        document.getElementById('dataModal').remove();
    };
    
    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

// 添加清除进度列表的函数
function clearProgressList() {
    const progressList = document.getElementById('progressList');
    progressList.innerHTML = '';
}
</script>
</body>
</html> 
import logging
from typing import Dict, Any, List
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR
import subprocess
import torch
from transformers import DetrImageProcessor, TableTransformerForObjectDetection
from pdf2image import convert_from_path
import platform
import os

os.environ["TOKENIZERS_PARALLELISM"] = "false"  # 禁用tokenizer并行
class OCRProcessor:
    """OCR文本识别处理器"""
    
    def __init__(self, ocr_initializer):
        """
        接收已初始化的OCR资源
        ocr_initializer: OCRInitializer实例
        """
        # 从初始化器中获取必要资源
        self.poppler_path = ocr_initializer.poppler_path
        self.ocr = ocr_initializer.ocr
        self.table_model = ocr_initializer.table_model
        self.processor = ocr_initializer.processor
        
        # 验证资源是否已初始化
        if not all([hasattr(self, 'poppler_path'), 
                    hasattr(self, 'ocr'), 
                    hasattr(self, 'table_model')]):
            raise RuntimeError("OCR资源未正确初始化")


    def pdf_to_images(self, file_path):
        """将PDF转换为图片列表"""
        logging.info(f"📄 正在转换 PDF: {file_path}")
        try:
            # 验证PDF文件有效性
            with open(file_path, 'rb') as f:
                header = f.read(4)
                if header != b'%PDF':
                    raise ValueError("无效的PDF文件头")

            images = convert_from_path(file_path, dpi=300, poppler_path=self.poppler_path)
            if not images:
                raise ValueError("PDF转换失败，可能文件已损坏或为空")
                
            logging.info(f"✅ PDF 转换成功，共 {len(images)} 页")
            return images
        except ValueError as ve:
            logging.error(f"❌ PDF 验证失败: {str(ve)}")
            raise
        except Exception as e:
            logging.error(f"❌ PDF 转换失败: {str(e)}", exc_info=True)
            raise RuntimeError("PDF处理失败，请检查文件完整性") from e

    def recognize_text(self, image):
        """识别图片中的文本"""
        result = self.ocr.ocr(np.array(image), cls=True)
        text_data = []
        for line in result:
            if not line:
                continue
            for item in line:
                try:
                    if isinstance(item, list) and len(item) >= 2:
                        bbox = item[0]
                        text_info = item[1]
                        text = text_info[0] if isinstance(text_info, (list, tuple)) else text_info
                        text_data.append({"text": str(text), "bbox": bbox})
                except Exception as e:
                    logging.warning(f"解析OCR结果时出错: {str(e)}，忽略该项")
        return text_data

    def detect_tables(self, image):
        """检测图片中的表格"""
        inputs = self.processor(images=image, return_tensors="pt")
        with torch.no_grad():
            outputs = self.table_model(**inputs)
        target_sizes = torch.tensor([image.size[::-1]])
        results = self.processor.post_process_object_detection(outputs, target_sizes=target_sizes)[0]

        scores = results["scores"]
        if isinstance(scores, list) and len(scores) > 0 and isinstance(scores[0], list):
            scores = [s[0] for s in scores]

        return [{"bbox": box.tolist()} for score, label, box in zip(scores, results["labels"], results["boxes"]) if isinstance(score, float) and score > 0.5]

    def is_overlap(self, bbox1, bbox2, threshold=0.5):
        """检查两个边界框是否重叠"""
        x1, y1, x2, y2 = bbox1
        a1, b1, a2, b2 = bbox2
        inter_x1, inter_y1 = max(x1, a1), max(y1, b1)
        inter_x2, inter_y2 = min(x2, a2), min(y2, b2)
        if inter_x1 >= inter_x2 or inter_y1 >= inter_y2:
            return False
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        bbox1_area = (x2 - x1) * (y2 - y1)
        bbox2_area = (a2 - a1) * (b2 - b1)
        iou = inter_area / (bbox1_area + bbox2_area - inter_area)
        return iou > threshold

    def process_page(self, image, page_num):
        """处理单页图片"""
        try:
            logging.info(f"处理第 {page_num+1} 页...")
            # 确保图片是RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            text_data = self.recognize_text(image)
            
            # 确保图片格式正确后再进行表格检测
            image_for_table = image.copy()
            table_data = self.detect_tables(image_for_table)
            
            for table in table_data:
                table["table_data"] = [t["text"] for t in text_data if self.is_overlap(t["bbox"], table["bbox"])]
            
            page_text = [t["text"] for t in text_data]
            if table_data:
                page_text.append("[TABLE_START]")
                for idx, table in enumerate(table_data):
                    page_text.append(f"表格 {page_num+1}-{idx+1}:")
                    page_text.extend(table["table_data"])
                page_text.append("[TABLE_END]")
            
            # 如果没有识别到任何文本，返回空结果而不是None
            if not page_text:
                return {"page": page_num + 1, "text": [], "tables": []}
            
            return {"page": page_num + 1, "text": page_text, "tables": table_data}
        except Exception as e:
            logging.error(f"第 {page_num+1} 页处理失败: {str(e)}")
            return {"page": page_num + 1, "text": [], "tables": [], "error": str(e)}

    async def process_image(self, image) -> Dict[str, Any]:
        """处理单张图片
        Args:
            image: 图片路径或PIL Image对象
        Returns:
            Dict[str, Any]: OCR结果
        """
        try:
            # 如果传入的是路径，转换为PIL Image对象
            if isinstance(image, str):
                image = Image.open(image)
            
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 转换为numpy数组
            image_np = np.array(image)
            
            # 使用PaddleOCR进行识别
            result = self.ocr.ocr(image_np, cls=True)
            print("process_image处理过程中的result是：", result)
            
            # 处理PaddleOCR返回None的情况
            if result is None:
                logging.warning("PaddleOCR返回空结果，可能图片格式不支持")
                return {
                    "text": [],
                    "status": "success",
                    "warning": "图片格式不支持或无法解析"
                }
            
            # 检查OCR结果有效性
            if not result or not isinstance(result, list) or len(result) == 0:
                logging.warning(f"未检测到有效文本内容: {image.size if hasattr(image, 'size') else 'Unknown'}")
                return {
                    "text": [],
                    "status": "success",  # 视为成功但无内容
                    "warning": "未检测到可识别文本"
                }
            
            # 提取文本
            text_list = []
            if isinstance(result[0], list) and len(result[0]) > 0:
                for line in result[0]:
                    if line and len(line) >= 2:
                        text = line[1][0]  # PaddleOCR格式: [[[x1,y1],[x2,y2]...], (text, confidence)]
                        text_list.append(text)
            
            return {
                "text": text_list,
                "status": "success"
            }
            
        except Exception as e:
            logging.error(f"图片处理失败: {str(e)}", exc_info=True)
            return {
                "text": [],
                "status": "failed",
                "error": str(e)
            } 
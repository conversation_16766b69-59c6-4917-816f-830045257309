import logging
from typing import Dict, Any, List
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR
import subprocess
import torch
from transformers import DetrImageProcessor, TableTransformerForObjectDetection
from pdf2image import convert_from_path
import platform
import os
import cv2
import json

os.environ["TOKENIZERS_PARALLELISM"] = "false"  # 禁用tokenizer并行
class OCRProcessor:
    """OCR文本识别处理器"""
    
    def __init__(self, ocr_initializer):
        """
        接收已初始化的OCR资源
        ocr_initializer: OCRInitializer实例
        """
        # 从初始化器中获取必要资源
        self.poppler_path = ocr_initializer.poppler_path
        self.ocr = ocr_initializer.ocr
        
        # 验证资源是否已初始化
        if not all([hasattr(self, 'poppler_path'), 
                    hasattr(self, 'ocr')]):
            raise RuntimeError("OCR资源未正确初始化")


    def pdf_to_images(self, file_path):
        """将PDF转换为图片列表"""
        logging.info(f"📄 正在转换 PDF: {file_path}")
        try:
            # 验证PDF文件有效性
            with open(file_path, 'rb') as f:
                header = f.read(4)
                if header != b'%PDF':
                    raise ValueError("无效的PDF文件头")

            images = convert_from_path(file_path, dpi=300, poppler_path=self.poppler_path)
            if not images:
                raise ValueError("PDF转换失败，可能文件已损坏或为空")
                
            logging.info(f"✅ PDF 转换成功，共 {len(images)} 页")
            return images
        except ValueError as ve:
            logging.error(f"❌ PDF 验证失败: {str(ve)}")
            raise
        except Exception as e:
            logging.error(f"❌ PDF 转换失败: {str(e)}", exc_info=True)
            raise RuntimeError("PDF处理失败，请检查文件完整性") from e

    


    async def process_image(self, image) -> Dict[str, Any]:
        """处理单张图片
        Args:
            image: 图片路径或PIL Image对象
        Returns:
            Dict[str, Any]: OCR结果
        """
        try:
            if isinstance(image, str) and os.path.exists(image):
                image = cv2.imread(image)
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            elif isinstance(image, Image.Image):
                image = np.array(image)
            # 预处理：确保是3通道图像
            if len(image.shape) == 2:  # 如果是灰度图
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            # 预处理：增强垂直线条（仅当图像有效时）
            if isinstance(image, np.ndarray):
                try:
                    # 保存原始图像
                    # from datetime import datetime
                    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    # original_path = f"debug_original_{timestamp}.png"
                    # cv2.imwrite(original_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                    # logging.info(f"原始图像已保存到: {original_path}")


                    kernel = np.ones((3,1), np.uint8)
                    enhanced = cv2.erode(image, kernel, iterations=1)
                    image = cv2.addWeighted(image, 0.7, enhanced, 0.3, 0)

                    # 保存预处理后的图像
                    # processed_path = f"debug_processed_{timestamp}.png"
                    # cv2.imwrite(processed_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                    # logging.info(f"预处理后图像已保存到: {processed_path}")


                    h, w = image.shape[:2]
                    if min(h, w) < 1000:
                        image = cv2.resize(image, (w*2, h*2), interpolation=cv2.INTER_CUBIC)

                except cv2.error as e:
                    logging.warning(f"OpenCV预处理失败，继续原始图像: {str(e)}")
            
            # 使用PaddleOCR进行识别
            result = self.ocr.ocr(image,cls=False)
            print("OCR识别的结果：",result)
            # result = self.table_engine(image)
            print("tbale_OCR识别的结果：",result)
            # 处理PaddleOCR返回None的情况
            if result is None:
                logging.warning("PaddleOCR返回空结果，可能图片格式不支持")
                return {
                    "text": [],
                    "status": "success",
                    "warning": "图片格式不支持或无法解析"
                }
            
            # 检查OCR结果有效性
            if not result or not isinstance(result, list) or len(result) == 0:
                logging.warning(f"未检测到有效文本内容: {image.size if hasattr(image, 'size') else 'Unknown'}")
                return {
                    "text": [],
                    "status": "success",  # 视为成功但无内容
                    "warning": "未检测到可识别文本"
                }
            
            # 提取文本 ，历史处理
            # text_list = []
            # if isinstance(result[0], list) and len(result[0]) > 0:
            #     for line in result[0]:
            #         if line and len(line) >= 2:
            #             text = line[1][0]  # PaddleOCR格式: [[[x1,y1],[x2,y2]...], (text, confidence)]
            #             text_list.append(text)

            # print("ocr读取结果：",result)
            # print("ocr处理结果：",text_list)
            # 提取所有文本块及其坐标
            
            # 0603 新的处理方式
            formatted_text = self.format_ocr_result(result)
            #0722 使用大模型进行格式化处理
            # formatted_text = await self.format_ocr_result_to_html(result)
            # if not formatted_text:
            #     formatted_text = self.format_ocr_result(result)
            print("格式化后的OCR结果:\n", formatted_text)

            return {
                "text": formatted_text,
                "status": "success"
            }
            
        except Exception as e:
            logging.error(f"图片处理失败: {str(e)}", exc_info=True)
            return {
                "text": [],
                "status": "failed",
                "error": str(e)
            }
    def format_ocr_result(self, result):
        if not result or not isinstance(result, list) or len(result) == 0:
            return ""
        
        # 提取所有文本块及其坐标
        text_blocks = []
        for block in result[0]:
            if block and len(block) >= 2:
                points = block[0]
                text = block[1][0]
                y_center = sum(point[1] for point in points) / 4
                text_blocks.append({
                    'text': text,
                    'y_center': y_center,
                    'x_min': min(point[0] for point in points)
                })
        
        if not text_blocks:
            return ""
        
        # 按Y坐标分组行
        text_blocks.sort(key=lambda x: (x['y_center'], x['x_min']))
        
        LINE_THRESHOLD = 10  # 更紧凑的行分组阈值
        
        # 分组到不同行
        lines = []
        current_line = []
        prev_y = text_blocks[0]['y_center']
        
        for block in text_blocks:
            if abs(block['y_center'] - prev_y) <= LINE_THRESHOLD:
                current_line.append(block)
            else:
                if current_line:
                    lines.append(current_line)
                current_line = [block]
            prev_y = block['y_center']
        
        if current_line:
            lines.append(current_line)
        
         # 构建格式化文本
        formatted_lines = []
        for line in lines:
            # 按X坐标排序同一行中的文本块
            line.sort(key=lambda x: x['x_min'])
            # 合并同一行文本，用\t分隔
            line_text = "\t".join(block['text'] for block in line)
            formatted_lines.append(line_text)
        
        # 用\n连接不同行
        formatted_text = "\n".join(formatted_lines)
        
        return formatted_text


    def format_ocr_result_bak(self,result):
        if not result or not isinstance(result, list) or len(result) == 0:
            return ""
        
        # 提取所有文本块及其坐标
        text_blocks = []
        for block in result[0]:
            if block and len(block) >= 2:
                # 获取文本框坐标和文本内容
                points = block[0]
                text = block[1][0]
                # 计算文本框的中心Y坐标用于行分组
                y_center = sum(point[1] for point in points) / 4
                text_blocks.append({
                    'text': text,
                    'y_center': y_center,
                    'x_min': min(point[0] for point in points)
                })
        
        if not text_blocks:
            return ""
        
        # 按Y坐标分组行
        # 先按Y中心坐标排序
        text_blocks.sort(key=lambda x: (x['y_center'], x['x_min']))
        
        # 行分组阈值（根据实际文档调整）
        LINE_THRESHOLD = 20
        
        # 分组到不同行
        lines = []
        current_line = []
        prev_y = text_blocks[0]['y_center']
        
        for block in text_blocks:
            if abs(block['y_center'] - prev_y) <= LINE_THRESHOLD:
                current_line.append(block)
            else:
                if current_line:
                    lines.append(current_line)
                current_line = [block]
            prev_y = block['y_center']
        
        if current_line:
            lines.append(current_line)
        
        # 构建格式化文本
        formatted_lines = []
        for line in lines:
            # 按X坐标排序同一行中的文本块
            line.sort(key=lambda x: x['x_min'])
            # 合并同一行文本，用\t分隔
            line_text = "\t".join(block['text'] for block in line)
            formatted_lines.append(line_text)
        
        # 用\n连接不同行
        formatted_text = "\n".join(formatted_lines)
        
        return formatted_text
    
    async def format_ocr_result_to_html(self,result):
        """使用AI模型将OCR结果转换为HTML格式
        Args:
            result: OCR识别结果
        Returns:
            str: 格式化后的HTML内容
        """
        ocr_data = []
        for page in result:
            # 检查是否有识别结果
            if 'rec_texts' not in page:
                continue
                
            # 确保所有数组长度一致
            min_length = min(
                len(page['rec_texts']),
                len(page['rec_scores']),
                len(page['rec_polys']),
                len(page['rec_boxes'])
            )
            
            # 提取并结构化数据
            for i in range(min_length):
                # 处理多边形坐标（numpy数组转列表）
                poly = page['rec_polys'][i].tolist() if hasattr(page['rec_polys'][i], 'tolist') else page['rec_polys'][i]
                
                # 处理矩形框坐标（numpy数组转列表）
                box = page['rec_boxes'][i].tolist() if hasattr(page['rec_boxes'][i], 'tolist') else page['rec_boxes'][i]
                
                ocr_data.append({
                    "text": page['rec_texts'][i],
                    "score": float(page['rec_scores'][i]),
                    "poly": poly,  # 多边形坐标
                    "box": box,    # 矩形框坐标 [x1,y1,x2,y2]
                    "position": {
                        "x1": min(p[0] for p in poly),
                        "y1": min(p[1] for p in poly),
                        "x2": max(p[0] for p in poly),
                        "y2": max(p[1] for p in poly)
                    }
                })

        # 按Y坐标排序（模拟文档从上到下的顺序）
        ocr_data.sort(key=lambda x: x["position"]["y1"])

        # 构造大模型提示词
        prompt = {
            "system": "你是一个资深的OCR识别专家，擅长根据OCR结果还原文档结构。请将以下OCR识别结果转换为HTML代码，不能丢失OCR识别结果中的任何信息，要求：\n"
                    "1. 使用<table>还原表格区域，其他文本用<div>或<p>按坐标位置排列\n"
                    "符合以下特征的文本必须用<table>还原：\n"
                    "- 多行文本的Y坐标对齐（如项号、商品名称、数量）\n"
                    "- 数值型数据垂直对齐（如单价、总价）\n"
                    "- 有明显的表头（如项号 | 商品编号 | 商品名称）\n"
                    "2. 保留原文档的格式（如标题加粗、段落缩进）\n"
                    "3. 输出纯HTML代码，无需解释 \n",
            "user": f"OCR识别结果（已按从上到下排序）：\n{json.dumps(ocr_data, indent=2, ensure_ascii=False)}"
        }
        
        # 使用AI模型优化HTML格式
        try:

            messages = [{'role': 'system', 'content': prompt['system']}, 
                        {'role': 'user', 'content': prompt['user']}]
            
            from app.ai_models import get_ai_model
            ai_model = get_ai_model("doubao")
            
            # 调用AI模型优化HTML
            response = await ai_model.chat(messages)
            if response:
                return response
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.warning(f"AI模型优化HTML失败: {str(e)}")
        
        # 如果AI模型优化失败，返回基本HTML
        return None
# 征免方式
Exemption_Method = {
    "1":"照章征税",
    "2":"折半征税",
    "3":"全免",
    "4":"特案",
    "5":"征免性质",
    "6":"保证金",
    "7":"保函",
    "8":"折半补税",
    "9":"全额退税"
}

def exemption_method_handler(input_text):
    for code,exemption in Exemption_Method.items():
        if input_text == exemption or input_text == code or input_text in exemption:
            return code,exemption
    return None,input_text
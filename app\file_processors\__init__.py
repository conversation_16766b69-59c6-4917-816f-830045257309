from .base import BaseFileProcessor
from .pdf_processor import PDFProcessor
from .office_processor import OfficeProcessor
from .image_processor import ImageProcessor
from .zip_processor import ZipProcessor
from app.dependencies import get_ocr_initializer
from fastapi import Depends
from app.ocr.ocr_initializer import OCRInitializer
import logging
import os,json
import asyncio
import re
import copy
from typing import Dict, Any, Optional
from ..ai_models import get_ai_model
from ..content_processor import ContentProcessor
from collections import defaultdict
from app.file_processors.util import Util

class FileProcessor:
    """文件处理器工厂类"""
    
    def __init__(self, ocr_initializer: OCRInitializer, model_provider: str = "doubao"):
        """初始化文件处理器
        
        Args:
            ocr_initializer: OCR初始化器
            model_provider: AI模型提供者
        """
        if not isinstance(ocr_initializer, OCRInitializer):
            raise ValueError("ocr_initializer必须是OCRInitializer实例")
        
        print(f"初始化文件处理器，模型提供者: {model_provider}")
        self.ai_handler = get_ai_model(model_provider)
        self.content_processor = ContentProcessor(self.ai_handler)
        
        # 初始化各种处理器 - 注意: 如果处理器需要OCR，还需要传递self.ocr
        self.processors = {
            ".pdf": PDFProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".docx": OfficeProcessor(self.ai_handler, ocr_initializer=ocr_initializer),
            ".doc": OfficeProcessor(self.ai_handler, ocr_initializer=ocr_initializer),
            ".rtf": OfficeProcessor(self.ai_handler, ocr_initializer=ocr_initializer),
            ".xlsx": OfficeProcessor(self.ai_handler, ocr_initializer=ocr_initializer),
            ".xls": OfficeProcessor(self.ai_handler, ocr_initializer=ocr_initializer),
            ".jpg": ImageProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".jpeg": ImageProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".png": ImageProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".zip": ZipProcessor(self.ai_handler, self),
            ".rar": ZipProcessor(self.ai_handler, self)
        }
    #0603 增加处理舱单接口    
    async def process_file_manifest(self, file_path: str) -> Dict[str, Any]:
        """处理舱单文件"""
        try:
            processor = self.processors.get(os.path.splitext(file_path)[1].lower())
            if not processor:
                return {"error": "不支持的文件类型"}
            
            # 提取文件内容
            result = await processor.process(file_path,flag=False)
            if "error" in result:
                return result
            
            # 保存OCR文本
            contents = result.get("contents", "")
            ocr_texts = result.get("ocr_text", "")
            formatted_contents = []
            # print("文件名：",file_path,"，文件内容：",contents)
            if isinstance(contents,dict):
                tasks = [self.handle_one_bill_manifest(contents)]
                formatted_contents = await asyncio.gather(*tasks)
            elif isinstance(contents ,list) and all(isinstance(item,dict) for item in contents):
                tasks = [self.handle_one_bill_manifest(ocr_text) for ocr_text in contents]
                formatted_contents = await asyncio.gather(*tasks)

            return {
                "ocr_text": ocr_texts,
                "formatted_content": formatted_contents
            }
            
        except Exception as e:
            logging.error(f"文件处理失败: {str(e)}", exc_info=True)
            return {"error": str(e)}
    # 处理一种单据
    async def handle_one_bill_manifest(self,ocr_text):
        if ocr_text and isinstance(ocr_text, dict):
            ocr_text_type = ocr_text["单据类型"]
            ocr_text_content = ocr_text["单据内容"]
            try:
                return await self.content_processor.process_content_manifest(
                            ocr_text_content,
                            ocr_text_type
                        )
            except Exception as e:
                logging.error(f"处理文件失败: {ocr_text_type}，错误: {str(e)}", exc_info=True)
                return None
        else:
            return None
        
    async def process_file(self, file_path: str,ie_flag:str) -> Dict[str, Any]:
        """处理文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            processor = self.processors.get(os.path.splitext(file_path)[1].lower())
            if not processor:
                return {"error": "不支持的文件类型"}
            
            # 提取文件内容
            result = await processor.process(file_path,True,ie_flag)
            if "error" in result:
                return result
            
            # 保存OCR文本
            contents = result.get("contents", "")
            ocr_texts = result.get("ocr_text", "")
            formatted_contents = []
            # print("文件名：",file_path,"，文件内容：",contents)
            if isinstance(contents,dict):
                tasks = [self.handle_one_bill(contents,ie_flag)]
                formatted_contents = await asyncio.gather(*tasks)
            elif isinstance(contents ,list) and all(isinstance(item,dict) for item in contents):
                tasks = [self.handle_one_bill(ocr_text,ie_flag) for ocr_text in contents]
                formatted_contents = await asyncio.gather(*tasks)

            return {
                "ocr_text": ocr_texts,
                "formatted_content": formatted_contents
            }
            
        except Exception as e:
            logging.error(f"文件处理失败: {str(e)}", exc_info=True)
            return {"error": str(e)}

    # 处理一种单据
    async def handle_one_bill(self,ocr_text,ie_flag):
        if ocr_text and isinstance(ocr_text, dict):
            ocr_text_type = ocr_text["单据类型"]
            ocr_text_content = ocr_text["单据内容"]
            # 0617 增加以下逻辑
            # 移除OCR文本中的特殊字符
            ocr_text_content = ocr_text_content.replace("\"", "").replace("'", "").replace("\\", "").replace('"','').replace('“','').replace('”','')
            try:
                return await self.content_processor.process_content(
                            ocr_text_content,
                            ocr_text_type,
                            ie_flag
                        )
            except Exception as e:
                logging.error(f"处理文件失败: {ocr_text_type}，错误: {str(e)}", exc_info=True)
                return None
        else:
            return None


    async def merge_contents(self, contents):
        if not contents:
            return {}
        
        # 如果只有一个文档，直接返回
        # if len(contents) == 1:
        #     return contents[0]
        try:
            import time
            total_start = time.perf_counter()
            # 统计 deduplicate_json_fields 耗时
            dedup_start = time.perf_counter()
            contents = deduplicate_json_fields(contents)
            dedup_end = time.perf_counter()

            # 统计大模型 merge 耗时
            merge_start = time.perf_counter()
            result = await self.content_processor.merge_contents(contents)
            merge_end = time.perf_counter()

            total_end = time.perf_counter()  

            return result
            
        except Exception as e:
            logging.error(f"文件处理失败: {str(e)}", exc_info=True)
            return {"error": str(e)}
    async def smart_fill(self, text, split_rules,sbys_required):
        """智能字段提取专用接口"""
        return await self.content_processor.split_product_fields(text, split_rules,sbys_required)
    async def merge_manifest_jsons(self, json_list):
        """合并多个舱单JSON，商品信息去重，并移除空值"""
        if not json_list:
            return {}
        def remove_empty_fields(data):
            """递归移除空值字段"""
            if isinstance(data, dict):
                return {k: remove_empty_fields(v) for k, v in data.items() if v not in [None, "", {}, []]}
            elif isinstance(data, list):
                return [remove_empty_fields(item) for item in data if item not in [None, "", {}, []]]
            else:
                return data

        def count_non_empty_fields(data):
            """计算非空字段数量"""
            count = 0
            if isinstance(data, dict):
                for v in data.values():
                    count += count_non_empty_fields(v)
            elif isinstance(data, list):
                for item in data:
                    count += count_non_empty_fields(item)
            else:
                if data not in [None, "", {}, []]:
                    count += 1
            return count
        # 1. 移除空值字段并计算有效字段数
        cleaned_jsons = []
        for j in json_list:
            cleaned = remove_empty_fields(j)
            cleaned_jsons.append({
                "data": cleaned,
                "count": count_non_empty_fields(cleaned)
            })
        
        # 2. 找出有效值最多的JSON作为主JSON
        main_json = max(cleaned_jsons, key=lambda x: x["count"])["data"]
        other_jsons = [x["data"] for x in cleaned_jsons if x["data"] != main_json]
        
        # 3. 合并其他JSON到主JSON中
        for other in other_jsons:
            # 处理content部分
            if "content" in main_json and "content" in other:
                main_content = main_json["content"]
                other_content = other["content"]
                
                # 提单号特殊处理
                if "提单号" in other_content and other_content["提单号"]:
                    main_content["提单号"] = other_content["提单号"]
                
                # 合并其他字段
                for key, value in other_content.items():
                    if key != "提单号" and key != "集装箱信息":
                        if key not in main_content or not main_content[key]:
                            main_content[key] = value
                
                # 处理集装箱信息
                if "集装箱信息" in main_content and "集装箱信息" in other_content:
                    main_containers = main_content["集装箱信息"]
                    other_containers = other_content["集装箱信息"]
                    
                    if len(main_containers) == len(other_containers):
                        for i in range(len(main_containers)):
                            for k, v in other_containers[i].items():
                                if k not in main_containers[i] or not main_containers[i][k]:
                                    main_containers[i][k] = v
        
        return main_json
    async def merge_jsons(self, json_list):
        """合并多个JSON，商品信息去重，并移除空值"""
        # 提取优先级商品信息：报关单 > 出口发票
        customs_products = None
        export_invoice_products = None
        for j in json_list:
            doc_type = (j.get('content') or {}).get("单据类型")
            if doc_type == "报关单" and not customs_products:
                customs_products = (j.get('content') or {}).get("商品信息")
            elif doc_type == "出口发票" and not export_invoice_products:
                export_invoice_products = (j.get('content') or {}).get("商品信息")
            elif doc_type == "进口发票" and not export_invoice_products:
                export_invoice_products = (j.get('content') or {}).get("商品信息")


        def deep_merge(target, source):
            """递归合并两个字典，返回新对象（不修改输入）"""
            merged = copy.deepcopy(target)  # 创建 target 的副本
            for key, value in source.items():
                if key in merged:
                    if isinstance(merged[key], dict) and isinstance(value, dict):
                        merged[key] = deep_merge(merged[key], value)  # 递归合并字典
                    elif isinstance(merged[key], list) and isinstance(value, list) and key == "商品信息":
                        merged[key] = merge_products(merged[key], value, customs_products, export_invoice_products)
                    elif isinstance(merged[key], list) and isinstance(value, list):
                        # merged[key] = value.copy()  # 避免直接引用
                        continue
                    else:
                        if merged[key] in (None, "", [], {}):
                            merged[key] = value
                else:
                    merged[key] = copy.deepcopy(value)  # 避免直接引用
            return merged

        def merge_products(products1, products2, customs_products, export_invoice_products):
            #0421 暂时先使用这个逻辑
            """
                实际业务逻辑：如果有报告单草单，那么使用草单中的商品信息。如果没有草单，使用发票中的商品信息
                因为报告单中的商品信息字段是最多的，所以目前先直接按照字段数量判断
                0423 增加以下逻辑：
                    如果两个商品列表的数据量不一样，那么按照字段数量多的商品列表返回
                    如果两个商品列表的数据量是一样的，那么需要进行字段的合并
            """
            # 0506 增加以下逻辑
            """
                如果存在报关单，那么优先使用报关单中的商品信息
                如果存在出口发票，那么优先使用出口发票中的商品信息
                如果都不存在，使用之前的逻辑
            """
            #0522 增加以下逻辑
            """
                如果商品数量不一致，那么不进行合并
            """
            # 0526 增加以下逻辑
            """
                在处理商品之前，先进行以下处理
                1、去掉商品中单价、数量、总价为0的商品
                2、对商品中的境内货源地进行处理
            """
            import re

            def extract_numeric(value):
                if value is None:
                    return None
                
                # 国际通用数字正则（含负号/科学计数法/千分位）
                num_pattern = r"-?\d{1,3}(?:,\d{3})*(?:\.\d+)?(?:[eE][-+]?\d+)?"
                match = re.search(num_pattern, str(value).replace(",", ""))
                return float(match.group()) if match else None

            def is_effective_product(product):
                price = extract_numeric(product.get("单价"))
                quantity = extract_numeric(product.get("数量及单位"))
                amount = extract_numeric(product.get("总价"))
                
                # 只要任意一个字段存在且值为0，就认为无效（返回False）
                if (price is not None and price == 0) or \
                (quantity is not None and quantity == 0) or \
                (amount is not None and amount == 0):
                    return False

                # 其他情况（字段不存在，或存在但值不为0），保留商品
                return True

            def process_products(products):
                if not isinstance(products, list):
                    return []
                
                return [p for p in products if is_effective_product(p)]


            base_products = None
            if customs_products:
                base_products = customs_products
            elif export_invoice_products:
                base_products = export_invoice_products
            # 处理商品信息
            base_products = process_products(base_products)
            products1 = process_products(products1)
            products2 = process_products(products2)

            if base_products:
                merged_products = []
                base_product_len = len(base_products)
                products1_len = len(products1)
                products2_len = len(products2)
                print("商品长度分别是：",base_product_len,products1_len,products2_len)
                if base_product_len != products1_len and base_product_len != products2_len:
                    return base_products
                for i, base_p in enumerate(base_products):
                    merged_p = base_p.copy()
                    products_1 = products1[i] if i < len(products1) else {}
                    products_2 = products2[i] if i < len(products2) else {}
                    if base_product_len == products1_len:
                        for field, value in products_1.items():
                            if (field not in merged_p or merged_p[field] in (None, "", [], {})) and value not in (None, "", [], {}):
                                merged_p[field] = value
                    if base_product_len == products2_len:
                        for field, value in products_2.items():
                            if (field not in merged_p or merged_p[field] in (None, "", [], {})) and value not in (None, "", [], {}):
                                merged_p[field] = value
                    merged_products.append(merged_p)
                return merged_products
            else:
                if len(products1) != len(products2):
                    return products1 if sum(len([v for v in p.values() if v]) for p in products1 if p) >= sum(len([v for v in p.values() if v]) for p in products2 if p) else products2
                # 原始合并逻辑
                count_fields_1 = sum(len([v for v in p.values() if v]) for p in products1 if p)
                count_fields_2 = sum(len([v for v in p.values() if v]) for p in products2 if p)
                if count_fields_1 >= count_fields_2:
                    base_products, extra_products = products1, products2
                else:
                    base_products, extra_products = products2, products1

                # 合并两个列表
                merged_products = []
                for base_p, extra_p in zip(base_products, extra_products):
                    merged_p = base_p.copy()  # 避免修改原数据
                    # 补充独有字段（不覆盖原有字段）
                    for field, value in extra_p.items():
                        if field not in merged_p and value not in (None, "", [], {}):
                            merged_p[field] = value
                    merged_products.append(merged_p)
                # 如果一个列表比另一个列表长，将多余的元素添加到结果中
                merged_products.extend(base_products[len(extra_products):])

                return merged_products

        def remove_empty(data):
            """递归移除空值字段"""
            if isinstance(data, dict):
                result = {}
                for k, v in data.items():
                    if isinstance(v, (dict, list)):
                        v = remove_empty(v)
                    if v not in (None, "", [], {}) and (not isinstance(v, (dict, list)) or v):
                        result[k] = v
                return result
            elif isinstance(data, list):
                # 先递归处理每个元素，再过滤空值
                cleaned_list = [remove_empty(v) for v in data]
                # 过滤掉空字符串、None、空列表、空字典
                return [v for v in cleaned_list if v not in (None, "", [], {})]
            else:
                return data

        # 逐个合并JSON
        # 找出报关单对象作为基准 merged
        merged = None
        for j in json_list:
            doc_type = (j.get('content') or {}).get("单据类型")
            if doc_type == "报关单":
                merged = j
                break
        if not merged:  # 如果没有报关单，找出口发票
            for j in json_list:
                doc_type = (j.get('content') or {}).get("单据类型")
                if doc_type == "出口发票":
                    merged = j
                    break
        if not merged:  # 如果找进口发票
            for j in json_list:
                doc_type = (j.get('content') or {}).get("单据类型")
                if doc_type == "进口发票":
                    merged = j
                    break
        if not merged:
            merged = json_list[0]  # 如果没有报关单，使用第一个文档作为基准

        for j in json_list:
            if j is not merged:
                merged = deep_merge(merged, j)
        return remove_empty(merged)
    async def verify_json(self,content):
        """校验JSON数据"""
        return await self.content_processor.verify_json(content)
    async def verify_list_json(self,json_list):
        """校验多个JSON数据"""
        return await self.content_processor.verify_list_json(json_list)

def deduplicate_json_fields(data):
    """
    对 JSON 集合进行字段级去重（相同的字段名+字段值只保留一个），并删除无值字段。

    参数:
        data: List[dict] 或 JSON 字符串

    返回:
        去重且去空后的 List[dict]
    """
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON string provided")

    if not isinstance(data, list):
        raise TypeError("Input must be a JSON array (list of dicts)")

    seen_fields = {}

    for item in data:
        if not isinstance(item, dict):
            continue

        keys_to_remove = []

        for key, value in item.items():
            # 删除无值字段
            if value in [None, "", [], {}]:
                keys_to_remove.append(key)
                continue

            # 序列化值用于去重
            serialized_value = json.dumps(value, sort_keys=True)
            field_identity = (key, serialized_value)

            if field_identity in seen_fields:
                keys_to_remove.append(key)
            else:
                seen_fields[field_identity] = True

        for key in keys_to_remove:
            del item[key]

    return data


def get_processor(file_path: str, ai_handler, ocr_initializer: OCRInitializer) -> Optional[BaseFileProcessor]:
    """根据文件类型获取对应的处理器"""
    extension = os.path.splitext(file_path)[1].lower()
    
    # 图片处理器
    if extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
        return ImageProcessor(ai_model=ai_handler, ocr_initializer=ocr_initializer)
    
    # PDF处理器
    elif extension == '.pdf':
        return PDFProcessor(ai_model=ai_handler, ocr_initializer=ocr_initializer)
    
    # Office文档处理器
    elif extension in ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']:
        return OfficeProcessor(ai_handler)
    
    return None 


    
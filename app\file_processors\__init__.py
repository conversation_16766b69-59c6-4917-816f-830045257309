from .base import BaseFileProcessor
from .pdf_processor import PDFProcessor
from .office_processor import OfficeProcessor
from .image_processor import ImageProcessor
from .zip_processor import ZipProcessor
from app.dependencies import get_ocr_initializer
from fastapi import Depends
from app.ocr.ocr_initializer import OCRInitializer
import logging
import os,json
from typing import Dict, Any, Optional
from ..ai_models import get_ai_model
from ..content_processor import ContentProcessor
from collections import defaultdict

class FileProcessor:
    """文件处理器工厂类"""
    
    def __init__(self, ocr_initializer: OCRInitializer, model_provider: str = "doubao"):
        """初始化文件处理器
        
        Args:
            ocr_initializer: OCR初始化器
            model_provider: AI模型提供者
        """
        if not isinstance(ocr_initializer, OCRInitializer):
            raise ValueError("ocr_initializer必须是OCRInitializer实例")
        
        print(f"初始化文件处理器，模型提供者: {model_provider}")
        self.ai_handler = get_ai_model(model_provider)
        self.content_processor = ContentProcessor(self.ai_handler)
        
        # 初始化各种处理器 - 注意: 如果处理器需要OCR，还需要传递self.ocr
        self.processors = {
            ".pdf": PDFProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".docx": OfficeProcessor(self.ai_handler),
            ".doc": OfficeProcessor(self.ai_handler),
            ".xlsx": OfficeProcessor(self.ai_handler),
            ".xls": OfficeProcessor(self.ai_handler),
            ".jpg": ImageProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".jpeg": ImageProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".png": ImageProcessor(ai_model=self.ai_handler, ocr_initializer=ocr_initializer),
            ".zip": ZipProcessor(self.ai_handler, self),
            ".rar": ZipProcessor(self.ai_handler, self)
        }
    
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            processor = self.processors.get(os.path.splitext(file_path)[1].lower())
            if not processor:
                return {"error": "不支持的文件类型"}
            
            # 提取文件内容
            result = await processor.process(file_path)
            if "error" in result:
                return result
            
            # 保存OCR文本
            ocr_text = result.get("content", "")
            
            # 大模型处理提取的内容
            formatted_content = await self.content_processor.process_content(
                result["content"],
                result["file_info"]
            )
            
            return {
                "ocr_text": ocr_text,
                "formatted_content": formatted_content,
                **formatted_content  # 保持向后兼容
            }
            
        except Exception as e:
            logging.error(f"文件处理失败: {str(e)}", exc_info=True)
            return {"error": str(e)}
        
    async def merge_contents(self, contents):
        print(f"开始使用大模型合并 {len(contents)} 个文档")
        if not contents:
            return {}
        
        # 如果只有一个文档，直接返回
        # if len(contents) == 1:
        #     return contents[0]
        try:
            import time
            total_start = time.perf_counter()
            # 统计 deduplicate_json_fields 耗时
            dedup_start = time.perf_counter()
            contents = deduplicate_json_fields(contents)
            dedup_end = time.perf_counter()
            print(f"去重耗时: {dedup_end - dedup_start:.4f} 秒")

            # 统计大模型 merge 耗时
            merge_start = time.perf_counter()
            result = await self.content_processor.merge_contents(contents)
            merge_end = time.perf_counter()
            print(f"大模型合并耗时: {merge_end - merge_start:.4f} 秒")

            total_end = time.perf_counter()
            print(f"总耗时: {total_end - total_start:.4f} 秒")

            return result
            
        except Exception as e:
            logging.error(f"文件处理失败: {str(e)}", exc_info=True)
            return {"error": str(e)}
    async def smart_fill(self, text):
        """智能字段提取专用接口"""
        return await self.content_processor.split_product_fields(text)
    async def merge_jsons(self, json_list):
        """合并多个JSON，商品信息去重，并移除空值"""
        def deep_merge(target, source):
            """递归合并两个字典，优先保留非空值"""
            for key, value in source.items():
                if key in target:
                    # 如果都是字典，递归合并
                    if isinstance(target[key], dict) and isinstance(value, dict):
                        deep_merge(target[key], value)
                    # 如果是商品信息列表，合并去重
                    elif isinstance(target[key], list) and isinstance(value, list) and key == "商品信息":
                        target[key] = merge_products(target[key], value)
                    # 其他列表直接覆盖（或自定义规则）
                    elif isinstance(target[key], list) and isinstance(value, list):
                        target[key] = value
                    # 非列表字段：优先保留非空值
                    else:
                        if target[key] in (None, "", [], {}):
                            target[key] = value
                else:
                    target[key] = value
            return target

        def merge_products(products1, products2):
            """合并商品列表，基于型号去重，并合并字段（优先非空值）"""
            product_map = defaultdict(dict)
            # 合并所有商品，按型号分组
            for product in products1 + products2:
                model = product.get("型号", "")
                for k, v in product.items():
                    if v not in (None, "", [], {}) or k not in product_map[model]:
                        product_map[model][k] = v
            return list(product_map.values())

        def remove_empty(data):
            """递归移除空值字段"""
            if isinstance(data, dict):
                return {
                    k: remove_empty(v)
                    for k, v in data.items()
                    if v not in (None, "", [], {}) and remove_empty(v) not in (None, "", [], {})
                }
            elif isinstance(data, list):
                return [remove_empty(v) for v in data if v not in (None, "", [], {})]
            else:
                return data

        # 逐个合并JSON
        merged = {}
        for j in json_list:
            merged = deep_merge(merged, j)
        return remove_empty(merged)

def deduplicate_json_fields(data):
    """
    对 JSON 集合进行字段级去重（相同的字段名+字段值只保留一个），并删除无值字段。

    参数:
        data: List[dict] 或 JSON 字符串

    返回:
        去重且去空后的 List[dict]
    """
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON string provided")

    if not isinstance(data, list):
        raise TypeError("Input must be a JSON array (list of dicts)")

    seen_fields = {}

    for item in data:
        if not isinstance(item, dict):
            continue

        keys_to_remove = []

        for key, value in item.items():
            # 删除无值字段
            if value in [None, "", [], {}]:
                keys_to_remove.append(key)
                continue

            # 序列化值用于去重
            serialized_value = json.dumps(value, sort_keys=True)
            field_identity = (key, serialized_value)

            if field_identity in seen_fields:
                keys_to_remove.append(key)
            else:
                seen_fields[field_identity] = True

        for key in keys_to_remove:
            del item[key]

    return data


def get_processor(file_path: str, ai_handler, ocr_initializer: OCRInitializer) -> Optional[BaseFileProcessor]:
    """根据文件类型获取对应的处理器"""
    extension = os.path.splitext(file_path)[1].lower()
    
    # 图片处理器
    if extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
        return ImageProcessor(ai_model=ai_handler, ocr_initializer=ocr_initializer)
    
    # PDF处理器
    elif extension == '.pdf':
        return PDFProcessor(ai_model=ai_handler, ocr_initializer=ocr_initializer)
    
    # Office文档处理器
    elif extension in ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']:
        return OfficeProcessor(ai_handler)
    
    return None 
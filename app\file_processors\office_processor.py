import logging
import os
from typing import Dict, Any, Optional
from .base import BaseFileProcessor
from ..ai_models.base import BaseAIModel
import docx
import openpyxl
import subprocess
import xlrd  # 添加xlrd库用于处理.xls文件
import json
import re
from striprtf.striprtf import rtf_to_text
from pdf2image import convert_from_path
import unicodedata
from ..ai_models.prompts import SPLIT_PROMPT,SPLIT_PROMPT_I
from .util import Util
from decimal import Decimal, ROUND_HALF_EVEN,ROUND_HALF_UP
from app.ocr.ocr_initializer import OCRInitializer
from docx.oxml.text.paragraph import CT_P
from docx.oxml.table import CT_Tbl
from docx.table import Table
from docx.text.paragraph import Paragraph
from docx.oxml.ns import qn
from tabulate import tabulate
import pandas as pd
import datetime

class OfficeProcessor(BaseFileProcessor):
    """Office文档处理器"""
    
    def __init__(self, ai_model: BaseAIModel, ocr_initializer: OCRInitializer):
        super().__init__(ai_model)
        self.ai_model = ai_model
        if not isinstance(ocr_initializer, OCRInitializer):
            raise ValueError("ocr_initializer必须是OCRInitializer实例")
        try:
            from ..ocr import OCRProcessor
            self.ocr_processor = OCRProcessor(ocr_initializer)
            self.has_ocr = True
            logging.info("PDF处理器初始化成功")
        except ImportError as e:
            logging.error(f"OCR功能初始化失败: {str(e)}")
            self.has_ocr = False
        except Exception as e:
            logging.error(f"PDF处理器初始化失败: {str(e)}")
            self.has_ocr = False
    
    async def process(self, file_path: str,flag,ie_flag:str) -> Dict[str, Any]:
        """处理Office文档"""
        try:
            logging.info(f"开始处理Office文档: {file_path}")
            
            result = await self._extract_text(file_path,flag,ie_flag)
            if isinstance(result, dict) and result.get("error"):
                return result
                
            return result
        except Exception as e:
            logging.error(f"Office文档处理失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "file_name": os.path.basename(file_path)
            }
    
    async def _extract_text(self, file_path: str,flag,ie_flag="E") -> dict:
        """提取文档文本内容"""
        ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if ext == '.docx' or ext == '.doc' or ext == '.rtf' or ext == '.xlsx' or ext == '.xls':
                return await self._extract_word(file_path,flag,ie_flag)
            elif ext == '.xlsx':
                return await self._extract_excel(file_path,flag,ie_flag)
            elif ext == '.xls':
                return await self._extract_xls(file_path,flag,ie_flag)
            else:
                return {"error": f"不支持的文件类型: {ext}"}
        except Exception as e:
            logging.error(f"提取文本失败: {str(e)}", exc_info=True)
            return {"error": f"提取文本失败: {str(e)}"}
    
    def iter_block_items(self, parent):
        for child in parent.element.body.iterchildren():
            if isinstance(child, CT_P):
                para = Paragraph(child, parent)
                if any(run.text.strip() for run in para.runs):  # 检查实际内容
                    yield para
            elif isinstance(child, CT_Tbl):
                yield Table(child, parent)
            # 忽略其他元素如CT_SectPr

    # 读取word格式的文件内容
    async def _extract_word(self, file_path: str,flag,ie_flag) -> dict:
        """提取Word文档文本"""
        try:
            # 使用 LibreOffice 将 .doc 转换为 .docx
            pdf_file_path = os.path.splitext(file_path)[0] + ".pdf"
            subprocess.run(
                ["soffice", "--headless", "--convert-to", "pdf", file_path, "--outdir", os.path.dirname(file_path)],
                check=True
            )
            # 转换PDF为图片
            images = convert_from_path(pdf_file_path)
            if not images:
                return {
                    "error": "PDF转换失败或为空文件",
                    "file_name": os.path.basename(file_path)
                }
            
            # 处理每一页
            all_text = []
            max_pages = 50  # 设置最大处理页数为5
            for i, image in enumerate(images):
                if i >= max_pages:
                    break
                logging.info(f"处理第 {i+1}/{len(images)} 页")
                result = await self.ocr_processor.process_image(image)
                print("pdf文件读取结果是：",result)
                if result["status"] == "success":
                    text = result["text"]
                    all_text.append(text)
            
            if not all_text:
                return {
                    "error": "未能提取到文本内容",
                    "file_name": os.path.basename(file_path)
                }
            
            # 合并所有页面的文本
            ocr_text = "\n\n".join(all_text)
            # ocr_text = ocr_text.replace("\t", "\\\\t")
            # ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            ocr_text = "文件名：" + os.path.basename(file_path) + "\n文件内容：" + ocr_text

            if flag:
                #调用大模型对content进行分割
                if ie_flag == "I":
                    split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                else:
                    split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                split_message = [
                    {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                    {"role": "user", "content": split_prompt}
                    ]
                content = await self.ai_model.chat(split_message)
                result = content.replace("```json", "").replace("```", "")
                formatted = json.loads(result)
            else:
                formatted = {
                    "单据类型": "舱单",
                    "单据内容": ocr_text
                }
            return {
                "contents":formatted,
                "ocr_text":ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "docx",
                    "file_path": file_path
                }
            }
        except subprocess.CalledProcessError as e:
            print(f"Error converting DOC to DOCX: {e}")
            return ""
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""

    async def _extract_docx(self, file_path: str,flag,ie_flag) -> dict:
        """提取DOCX文档文本"""
        doc = docx.Document(file_path)
        full_text = []

        for block in self.iter_block_items(doc):
            
            if isinstance(block, Paragraph):
                full_text.append(block.text.strip())
            elif isinstance(block, Table):
                full_text.extend(self.extract_table_content(block))

        ocr_text = "\n\n".join(full_text)
        ocr_text = ocr_text.replace("\t", "\\\\t")
        ocr_text = ocr_text.replace("\n", "\\\\n")
        ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
        print("docx文件读取结果是：",ocr_text)
        if flag:
            #调用大模型对content进行分割
            if ie_flag == "I":
                split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
            else:
                split_prompt = SPLIT_PROMPT.format(text=ocr_text)
            split_message = [
                {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                {"role": "user", "content": split_prompt}
                ]
            content = await self.ai_model.chat(split_message)
            result = content.replace("```json", "").replace("```", "")
            formatted = json.loads(result)
        else:
            formatted = {
                "单据类型": "舱单",
                "单据内容": ocr_text
            }

        return {
            "contents":formatted,
            "ocr_text":ocr_text,
            "file_info": {
                "file_name": os.path.basename(file_path),
                "file_type": "docx",
                "file_path": file_path
            }
        }
    
    async def _extract_doc(self, file_path: str,flag,ie_flag) -> dict:
        """提取DOC文档文本和表格内容"""
        try:
            # 使用 LibreOffice 将 .doc 转换为 .docx
            docx_file_path = os.path.splitext(file_path)[0] + ".docx"
            subprocess.run(
                ["soffice", "--headless", "--convert-to", "docx", file_path, "--outdir", os.path.dirname(file_path)],
                check=True
            )
            # 使用 python-docx 提取内容
            doc = docx.Document(docx_file_path)
            full_text = []

            for block in self.iter_block_items(doc):
                if isinstance(block, Paragraph):
                    # 增强文本提取
                    text = block.text.strip()
                    if not text:
                        # 检查是否存在隐藏格式文本
                        runs_text = ''.join(run.text for run in block.runs).strip()
                        text = runs_text if runs_text else ""
                    
                    if text:
                        full_text.append(text)
                elif isinstance(block, Table):
                    full_text.extend(self.extract_table_content(block))

            ocr_text = "\n\n".join(full_text)
            ocr_text = ocr_text.replace("\t", "\\\\t").replace('\u3000', ' ')  # 全角空格转半角
            ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            print("doc文件读取结果是：",ocr_text)
            if flag:
                #调用大模型对content进行分割
                if ie_flag == "I":
                    split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                else:
                    split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                split_message = [
                    {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                    {"role": "user", "content": split_prompt}
                    ]
                content = await self.ai_model.chat(split_message)
                result = content.replace("```json", "").replace("```", "")
                formatted = json.loads(result)
            else:
                formatted = {
                    "单据类型": "舱单",
                    "单据内容": ocr_text
                }
            return {
                "contents":formatted,
                "ocr_text":ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "docx",
                    "file_path": file_path
                }
            }
        except subprocess.CalledProcessError as e:
            print(f"Error converting DOC to DOCX: {e}")
            return ""
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""
    async def _extract_rtf(self, file_path: str,flag,ie_flag) -> dict:
        """提取RTF文档文本"""
        try:
            # 读取RTF文件
            with open(file_path, "r", encoding="utf-8") as f:
                rtf_content = f.read()

            ocr_text = rtf_to_text(rtf_content)
            ocr_text = "\n\n".join(ocr_text)
            ocr_text = ocr_text.replace("\t", "\\\\t")
            ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            print("rtf文件读取结果是：",ocr_text)

            if flag:
                #调用大模型对content进行分割
                if ie_flag == "I":
                    split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                else:
                    split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                split_message = [
                    {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                    {"role": "user", "content": split_prompt}
                    ]
                content = await self.ai_model.chat(split_message)
                result = content.replace("```json", "").replace("```", "")
                formatted = json.loads(result)
            else:
                formatted = {
                    "单据类型": "舱单",
                    "单据内容": ocr_text
                }
            return {
                "contents":formatted,
                "ocr_text":ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "docx",
                    "file_path": file_path
                }
            }
        except subprocess.CalledProcessError as e:
            print(f"Error converting DOC to DOCX: {e}")
            return ""
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""

    async def _extract_excel(self, file_path: str,flag,ie_flag) -> dict:
        """提取Excel文档文本(.xlsx格式)"""
        def get_decimal_places_from_format(fmt):
            # 识别类似 0.00、#,##0.000、0.0%、0.00E+00 的小数位数
            match = re.search(r"\.(0+)", fmt)
            if match:
                return len(match.group(1))
            return 0

        def is_percent_format(fmt):
            return "%" in fmt

        def format_number(value, fmt):
            if fmt == "General":
                return str(value)  # 直接返回原始值，或保留一定小数位数
            decimal_places = get_decimal_places_from_format(fmt)
            d = Decimal(str(value))
            rounding_fmt = '1.' + '0' * decimal_places
            rounded = d.quantize(Decimal(rounding_fmt), rounding=ROUND_HALF_UP)

            if is_percent_format(fmt):
                rounded *= 100  # openpyxl 把百分比原始存储为 0.05，而显示为 5%
                return f"{rounded:.{decimal_places}f}%"
            return f"{rounded:.{decimal_places}f}" if decimal_places > 0 else f"{rounded:.0f}"
        
        # 新增函数：从格式字符串提取货币符号
        def get_currency_symbol(format_str):
            # 货币代码到符号的映射表
            CURRENCY_MAP = {
                '€': '€', '$': '$', '£': '£', '¥': '¥',
                'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥',
                'CNY': '¥', 'INR': '₹', 'RUB': '₽', 'KRW': '₩',
                'TRY': '₺', 'UAH': '₴', 'CHF': 'CHF', 'AUD': 'A$',
                'CAD': 'C$', 'NZD': 'NZ$', 'ZAR': 'R'
            }

            # 1. 优先匹配Excel方括号格式（如[$€]、[$$-409]）
            bracket_match = re.search(r'\[\$([^\]]+)\]', format_str)
            if bracket_match:
                currency_code = bracket_match.group(1).split('-')[0]
                return CURRENCY_MAP.get(currency_code, currency_code)

            # 2. 匹配常见Unicode货币符号
            unicode_symbols = re.search(r'[\$€£¥₹₽₩₺₴]', format_str)
            if unicode_symbols:
                return unicode_symbols.group()

            # 3. 处理特殊符号别名
            if '￥' in format_str:
                return '¥'
            
            # 4. 匹配货币代码（如USD、EUR）
            code_match = re.search(r'\b([A-Z]{3})\b', format_str)
            if code_match and code_match.group(1) in CURRENCY_MAP:
                return CURRENCY_MAP[code_match.group(1)]

            # 5. 特殊处理复合符号（A$、C$等）
            if re.search(r'A\$', format_str): return 'A$'
            if re.search(r'C\$', format_str): return 'C$'
            if re.search(r'NZ\$', format_str): return 'NZ$'

            return None

        try:
            wb = openpyxl.load_workbook(file_path, data_only=True)
            sheets_data = []
            sheets_text = []
            
            # 收集所有Sheet内容
            for sheet in wb.worksheets:
                sheet_state = sheet.sheet_state  
                if sheet.title.startswith("XLR_") or sheet.title.startswith("Chart") or sheet.max_row == 1 and sheet.max_column == 1 or sheet_state != "visible":
                    continue
                sheet_data = {}
                rows_data = []
                max_actual_column = 0  # 用于记录实际最大列数

                for row in sheet.rows:
                    row_text = []
                    current_row_max_column = 0  # 当前行的最大列数

                    for cell_index, cell in enumerate(row):
                        if cell.value is not None:
                            current_row_max_column = cell_index + 1  # 记录当前行中最后一个有值的列
                            if isinstance(cell.value, (float, int)):
                                number_format = cell.number_format or ''
                                try:
                                    formatted_value = format_number(cell.value, number_format)
                                    # 新增货币符号处理
                                    currency_symbol = get_currency_symbol(number_format)
                                    if currency_symbol:
                                        # 处理符号可能已包含的情况（如format_number已添加）
                                        if not formatted_value.startswith(currency_symbol):
                                            formatted_value = f"{currency_symbol}{formatted_value}"
                                except Exception:
                                    formatted_value = str(cell.value)
                                clean_value = formatted_value.replace("\n", " ")
                            else:
                                clean_value = str(cell.value).replace("\n", " ")
                            row_text.append(clean_value)
                        else:
                            if current_row_max_column > 0:
                                row_text.append(" ")  # 占位符

                    if current_row_max_column > max_actual_column:
                        max_actual_column = current_row_max_column  # 更新实际最大列数

                    # 检查是否需要去掉行尾的空占位符
                    while len(row_text) > max_actual_column:
                        row_text.pop()

                    if row_text and not all(cell == "" for cell in row_text):
                        rows_data.append("\t".join(row_text))
                if rows_data:  # 只记录有内容的Sheet
                    sheet_content = "\n".join(rows_data)
                    sheet_data["单据类型"] = sheet.title
                    sheet_data["单据内容"] = sheet_content
                    sheets_data.append(sheet_data)
                    sheets_text.append(sheet_content)
            
            # 合并所有页面的文本
            ocr_text = "\n\n".join(sheets_text)
            # ocr_text = ocr_text.replace("\t", "\\\\t")
            # ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            # 构建基础返回结构
            base_result = {
                "ocr_text": ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "xlsx",  # 修正为xlsx
                    "file_path": file_path
                }
            }
            # 根据Sheet数量决定contents格式
            if len(sheets_data) > 1:
                base_result["contents"] = sheets_data
            else:
                if flag:
                    #调用大模型对content进行分割
                    if ie_flag == "I":
                        split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                    else:
                        split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                    split_message = [
                        {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                        {"role": "user", "content": split_prompt}
                        ]
                    content = await self.ai_model.chat(split_message)
                    result = content.replace("```json", "").replace("```", "")
                    formatted = json.loads(result,strict=False)
                    base_result["contents"] = formatted
                else:
                    formatted = {
                        "单据类型": "舱单",
                        "单据内容": ocr_text
                    }
                    base_result["contents"] = formatted
            
            return base_result
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"Excel处理失败: {str(e)}")
            return {"error": f"Excel(.xlsx)处理失败: {str(e)}"}
            
    async def _extract_excel_html(self, file_path: str,flag,ie_flag) -> dict:
        """提取Excel文档文本(.xlsx格式)"""
        def clean_text(text: str) -> str:
            """清洗文本"""
            if not text:
                return "&nbsp;"
            return str(text).replace("\n", " ").strip()
        def get_decimal_places_from_format(fmt):
            # 识别类似 0.00、#,##0.000、0.0%、0.00E+00 的小数位数
            match = re.search(r"\.(0+)", fmt)
            if match:
                return len(match.group(1))
            return 0

        def is_percent_format(fmt):
            return "%" in fmt

        def format_number(value, fmt):
            if fmt == "General":
                return str(value)  # 直接返回原始值，或保留一定小数位数
            decimal_places = get_decimal_places_from_format(fmt)
            d = Decimal(str(value))
            rounding_fmt = '1.' + '0' * decimal_places
            rounded = d.quantize(Decimal(rounding_fmt), rounding=ROUND_HALF_UP)

            if is_percent_format(fmt):
                rounded *= 100  # openpyxl 把百分比原始存储为 0.05，而显示为 5%
                return f"{rounded:.{decimal_places}f}%"
            return f"{rounded:.{decimal_places}f}" if decimal_places > 0 else f"{rounded:.0f}"
        
        # 新增函数：从格式字符串提取货币符号
        def get_currency_symbol(format_str):
            # 货币代码到符号的映射表
            CURRENCY_MAP = {
                '€': '€', '$': '$', '£': '£', '¥': '¥',
                'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥',
                'CNY': '¥', 'INR': '₹', 'RUB': '₽', 'KRW': '₩',
                'TRY': '₺', 'UAH': '₴', 'CHF': 'CHF', 'AUD': 'A$',
                'CAD': 'C$', 'NZD': 'NZ$', 'ZAR': 'R'
            }

            # 1. 优先匹配Excel方括号格式（如[$€]、[$$-409]）
            bracket_match = re.search(r'\[\$([^\]]+)\]', format_str)
            if bracket_match:
                currency_code = bracket_match.group(1).split('-')[0]
                return CURRENCY_MAP.get(currency_code, currency_code)

            # 2. 匹配常见Unicode货币符号
            unicode_symbols = re.search(r'[\$€£¥₹₽₩₺₴]', format_str)
            if unicode_symbols:
                return unicode_symbols.group()

            # 3. 处理特殊符号别名
            if '￥' in format_str:
                return '¥'
            
            # 4. 匹配货币代码（如USD、EUR）
            code_match = re.search(r'\b([A-Z]{3})\b', format_str)
            if code_match and code_match.group(1) in CURRENCY_MAP:
                return CURRENCY_MAP[code_match.group(1)]

            # 5. 特殊处理复合符号（A$、C$等）
            if re.search(r'A\$', format_str): return 'A$'
            if re.search(r'C\$', format_str): return 'C$'
            if re.search(r'NZ\$', format_str): return 'NZ$'

            return None

        try:
            wb = openpyxl.load_workbook(file_path, data_only=True)
            sheets_data = []
            sheets_text = []
            
            # 处理每个sheet
            for sheet in wb.worksheets:
                # 跳过隐藏或系统sheet
                if (sheet.title.startswith("XLR_") or 
                    sheet.title.startswith("Chart") or 
                    sheet.sheet_state != "visible" or
                    (sheet.max_row == 1 and sheet.max_column == 1)):
                    continue
                
                # 初始化HTML
                html = [
                    '<!DOCTYPE html><html><head><meta charset="UTF-8">',
                    '<style>table {border-collapse: collapse;} td {border: 1px solid #ddd; padding: 4px;}</style>',
                    '</head><body><table>'
                ]
                
                # 获取合并单元格信息
                merged_cells = {}
                for range_ in sheet.merged_cells.ranges:
                    merged_cells[(range_.min_row, range_.min_col)] = {
                        'value': sheet.cell(range_.min_row, range_.min_col).value,
                        'rowspan': range_.max_row - range_.min_row + 1,
                        'colspan': range_.max_col - range_.min_col + 1
                    }
                
                # 处理每一行
                for row in sheet.iter_rows():
                    html.append("<tr>")
                    
                    for cell in row:
                        # 检查是否是合并单元格
                        cell_key = (cell.row, cell.column)
                        if cell_key in merged_cells:
                            # 合并单元格的左上角
                            mc = merged_cells[cell_key]
                            cell_value = mc['value']
                            rowspan = f' rowspan="{mc["rowspan"]}"'
                            colspan = f' colspan="{mc["colspan"]}"'
                        elif any((r <= cell.row < r + rs and c <= cell.column < c + cs) 
                            for (r, c), mc in merged_cells.items() 
                            for rs, cs in [(mc['rowspan'], mc['colspan'])]):
                            # 合并单元格的其他部分，跳过
                            continue
                        else:
                            # 普通单元格
                            cell_value = cell.value
                            rowspan = ''
                            colspan = ''
                        
                        # 处理单元格值
                        if cell_value is not None and cell_value != '':
                            # 处理数字类型
                            if isinstance(cell_value, (int, float)):
                                number_format = cell.number_format or 'General'
                                try:
                                    formatted_value = format_number(cell_value, number_format)
                                    # 添加货币符号
                                    currency_symbol = get_currency_symbol(number_format)
                                    if currency_symbol and not formatted_value.startswith(currency_symbol):
                                        formatted_value = f"{currency_symbol}{formatted_value}"
                                    cell_value = formatted_value
                                except Exception:
                                    cell_value = str(cell_value)
                            # 处理日期类型
                            elif isinstance(cell_value, datetime.datetime):
                                cell_value = cell_value.strftime('%Y-%m-%d')
                            elif isinstance(cell_value, datetime.date):
                                cell_value = cell_value.strftime('%Y-%m-%d')
                            
                            cell_value = clean_text(cell_value)
                        else:
                            cell_value = "&nbsp;"
                        
                        # 添加单元格到HTML
                        html.append(f'<td{rowspan}{colspan}>{cell_value}</td>')
                    
                    html.append("</tr>")
                
                # 完成当前sheet的HTML
                html.append("</table></body></html>")
                sheet_content = "".join(html)
                
                # 保存当前sheet的数据
                sheet_data = {
                    "单据类型": sheet.title,
                    "单据内容": sheet_content
                }
                sheets_data.append(sheet_data)
                sheets_text.append(sheet_content)
            
            # 合并所有页面的文本
            ocr_text = "\n\n".join(sheets_text)
            # ocr_text = ocr_text.replace("\t", "\\\\t")
            # ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            # 构建基础返回结构
            base_result = {
                "ocr_text": ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "xlsx",  # 修正为xlsx
                    "file_path": file_path
                }
            }
            # 根据Sheet数量决定contents格式
            if len(sheets_data) > 1:
                base_result["contents"] = sheets_data
            else:
                if flag:
                    #调用大模型对content进行分割
                    if ie_flag == "I":
                        split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                    else:
                        split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                    split_message = [
                        {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                        {"role": "user", "content": split_prompt}
                        ]
                    content = await self.ai_model.chat(split_message)
                    result = content.replace("```json", "").replace("```", "")
                    formatted = json.loads(result,strict=False)
                    base_result["contents"] = formatted
                else:
                    formatted = {
                        "单据类型": "舱单",
                        "单据内容": ocr_text
                    }
                    base_result["contents"] = formatted
            
            return base_result
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"Excel处理失败: {str(e)}")
            return {"error": f"Excel(.xlsx)处理失败: {str(e)}"}
    
    async def _extract_xls(self, file_path: str,flag,ie_flag) -> dict:
        """提取Excel文档文本(.xls格式)
        返回格式:
        - 多个Sheet有内容时: {"contents": {"sheet1": "内容1", "sheet2": "内容2"}, ...}
        - 单个Sheet有内容时: {"contents": 大模型处理后的内容, ...}
        """
        def clean_text(text: str) -> str:
            """深度清洗文本：
            1. 移除非打印控制字符
            2. 替换异常空白符
            3. 处理Unicode特殊字符
            """
            # 保留的合法空白符（制表符、换行、回车）
            allowed_whitespace = {'\t', '\n', '\r'}
            cleaned = []
            for char in text:
                # 处理控制字符
                if unicodedata.category(char)[0] == 'C' and char not in allowed_whitespace:
                    continue
                # 替换异常空格
                if char.isspace() and char not in allowed_whitespace:
                    cleaned.append(' ')
                    continue
                # 过滤BOM头等特殊字符
                if ord(char) in (0xFEFF, 0xFFFE, 0xFFFF):
                    continue
                cleaned.append(char)
            return ''.join(cleaned)
        def get_decimal_places(format_str):
            """
            从格式字符串中提取小数位数（只考虑"0"和"#"）
            """
            if '.' in format_str:
                decimal_part = format_str.split('.')[-1]
                return sum(1 for c in decimal_part if c in '0#')
            return 0
        def round_bankers(value, decimal_places):
            d = Decimal(str(value))  # 先转为 Decimal，避免 float 精度问题
            fmt = '1.' + '0' * decimal_places  # 例如：'1.00' 表示保留2位
            return d.quantize(Decimal(fmt), rounding=ROUND_HALF_UP)
        
        # 新增函数：从格式字符串提取货币符号
        def get_currency_symbol(format_str):
            # 货币代码到符号的映射表
            CURRENCY_MAP = {
                '€': '€', '$': '$', '£': '£', '¥': '¥',
                'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥',
                'CNY': '¥', 'INR': '₹', 'RUB': '₽', 'KRW': '₩',
                'TRY': '₺', 'UAH': '₴', 'CHF': 'CHF', 'AUD': 'A$',
                'CAD': 'C$', 'NZD': 'NZ$', 'ZAR': 'R'
            }

            # 1. 优先匹配Excel方括号格式（如[$€]、[$$-409]）
            bracket_match = re.search(r'\[\$([^\]]+)\]', format_str)
            if bracket_match:
                currency_code = bracket_match.group(1).split('-')[0]
                return CURRENCY_MAP.get(currency_code, currency_code)

            # 2. 匹配常见Unicode货币符号
            unicode_symbols = re.search(r'[\$€£¥₹₽₩₺₴]', format_str)
            if unicode_symbols:
                return unicode_symbols.group()

            # 3. 处理特殊符号别名
            if '￥' in format_str:
                return '¥'
            
            # 4. 匹配货币代码（如USD、EUR）
            code_match = re.search(r'\b([A-Z]{3})\b', format_str)
            if code_match and code_match.group(1) in CURRENCY_MAP:
                return CURRENCY_MAP[code_match.group(1)]

            # 5. 特殊处理复合符号（A$、C$等）
            if re.search(r'A\$', format_str):
                return 'A$'
            if re.search(r'C\$', format_str):
                return 'C$'
            if re.search(r'NZ\$', format_str):
                return 'NZ$'

            return None

        
        try:
            # 打开Excel文件
            wb = xlrd.open_workbook(file_path,formatting_info=True)
            sheets_data = []
            sheets_text = []

            # # 收集所有Sheet内容
            for i in range(wb.nsheets):
                sheet = wb.sheet_by_index(i)
                visibility = wb._sheet_visibility[i]  # 注意是列表索引，不是方法调用
                if sheet.name.startswith("XLR_") or sheet.nrows == 0 or sheet.ncols == 0 or visibility != 0:
                    # 跳过系统生成或空的 Sheet
                    continue
                sheet_data = {}
                rows_data = []
                # 记录实际最大列数，用于后续判断是否需要占位符
                max_actual_col = 0 
                for row_idx in range(sheet.nrows):
                    row_text = []
                    last_valid_col = -1
                    for col_idx in range(sheet.ncols):
                        is_merged = False
                        for (rlo, rhi, clo, chi) in sheet.merged_cells:
                            if (row_idx >= rlo and row_idx < rhi and 
                                col_idx >= clo and col_idx < chi and 
                                (row_idx != rlo or col_idx != clo)):
                                is_merged = True
                                break
                        if is_merged:
                            continue  # 跳过合并区域的非左上角单元格
                        value = sheet.cell_value(row_idx, col_idx)
                        if value is not None and value != '':
                            # 处理日期类型
                            if sheet.cell_type(row_idx, col_idx) == xlrd.XL_CELL_DATE:
                                try:
                                    value = xlrd.xldate.xldate_as_datetime(value, wb.datemode).strftime('%Y-%m-%d')
                                except Exception:
                                    pass
                            # 数字处理：根据单元格格式保留对应的小数位数
                            elif sheet.cell_type(row_idx, col_idx) == xlrd.XL_CELL_NUMBER:
                                xf_index = sheet.cell_xf_index(row_idx, col_idx)
                                xf = wb.xf_list[xf_index]
                                fmt_key = xf.format_key
                                fmt_str = wb.format_map.get(fmt_key, None)
                                print("value:",value,"-----fmt_str.format_str:",fmt_str.format_str)
                                if fmt_str and fmt_str.format_str != "General":
                                    decimal_places = get_decimal_places(fmt_str.format_str)
                                    currency_symbol = get_currency_symbol(fmt_str.format_str)
                                    value = str(round_bankers(value, decimal_places))
                                    # 添加货币符号
                                    if currency_symbol:
                                        value = f"{currency_symbol}{value}"
                                else:
                                    if isinstance(value, float) and value.is_integer():
                                        value = int(value)
                                    value = str(value)  # fallback
                                print(f"读取excel内容:{fmt_str.format_str}:{value}")
                            # 清洗文本并转为字符串
                            cleaned_value = clean_text(str(value))
                            row_text.append(cleaned_value)
                            last_valid_col = col_idx  # 更新当前行最大的有效列索引
                            max_actual_col = max(max_actual_col, col_idx)  # 更新实际最大列数
                        else:
                            # 如果当前单元格为空，但在它前面存在非空单元格，才添加占位符
                            if col_idx >= last_valid_col and col_idx <= max_actual_col:
                                row_text.append(" ")
                    # 如果一行全是空格，跳过该行
                    if not any(cell != " " for cell in row_text):
                        continue
                    # 检查是否需要去掉行尾的空占位符
                    while len(row_text) > max_actual_col + 1:
                        row_text.pop()
                    # 移除行尾的空格占位符
                    while row_text and row_text[-1] == " ":
                        row_text.pop()
                    if row_text:
                        rows_data.append("\t".join(row_text))
                if rows_data:  # 只记录有内容的Sheet
                    sheet_content = "\n".join(rows_data)
                    # sheet_content = convert_rows_to_markdown(rows_data)
                    # sheet_content = markdown_table
                    # print(markdown_table)
                    sheet_data["单据类型"] = sheet.name
                    sheet_data["单据内容"] = sheet_content
                    sheets_data.append(sheet_data)
                    sheets_text.append(sheet_content)
            
            # 合并所有页面的文本
            ocr_text = "\n\n".join(sheets_text)
            # ocr_text = ocr_text.replace("\t", "\\\\t")
            # ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            
            # 构建基础返回结构
            base_result = {
                "ocr_text": ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "xls",
                    "file_path": file_path
                }
            }
            
            # 根据Sheet数量决定contents格式
            if len(sheets_data) > 1:
                base_result["contents"] = sheets_data
            else:
                if flag:
                    # 调用大模型对content进行分割
                    if ie_flag == "I":
                        split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                    else:
                        split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                    split_message = [
                        {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                        {"role": "user", "content": split_prompt}
                    ]
                    content = await self.ai_model.chat(split_message)
                    result = content.replace("```json", "").replace("```", "")
                    formatted = json.loads(result)
                    base_result["contents"] = formatted
                else:
                    formatted = {
                        "单据类型": "舱单",
                        "单据内容": ocr_text
                    }
                    base_result["contents"] = formatted
            
            return base_result
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"Excel处理失败: {str(e)}")
            return {
                "error": f"Excel(.xls)处理失败: {str(e)}",
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "xls",
                    "file_path": file_path
                }
            } 
        
    async def _extract_xls_html(self, file_path: str,flag,ie_flag) -> dict:
        """提取Excel文档文本(.xls格式)
        返回格式:
        - 多个Sheet有内容时: {"contents": {"sheet1": "内容1", "sheet2": "内容2"}, ...}
        - 单个Sheet有内容时: {"contents": 大模型处理后的内容, ...}
        """
        def clean_text(text: str) -> str:
            """深度清洗文本：
            1. 移除非打印控制字符
            2. 替换异常空白符
            3. 处理Unicode特殊字符
            """
            # 保留的合法空白符（制表符、换行、回车）
            allowed_whitespace = {'\t', '\n', '\r'}
            cleaned = []
            for char in text:
                # 处理控制字符
                if unicodedata.category(char)[0] == 'C' and char not in allowed_whitespace:
                    continue
                # 替换异常空格
                if char.isspace() and char not in allowed_whitespace:
                    cleaned.append(' ')
                    continue
                # 过滤BOM头等特殊字符
                if ord(char) in (0xFEFF, 0xFFFE, 0xFFFF):
                    continue
                cleaned.append(char)
            return ''.join(cleaned)
        def get_decimal_places(format_str):
            """
            从格式字符串中提取小数位数（只考虑"0"和"#"）
            """
            if '.' in format_str:
                decimal_part = format_str.split('.')[-1]
                return sum(1 for c in decimal_part if c in '0#')
            return 0
        def round_bankers(value, decimal_places):
            d = Decimal(str(value))  # 先转为 Decimal，避免 float 精度问题
            fmt = '1.' + '0' * decimal_places  # 例如：'1.00' 表示保留2位
            return d.quantize(Decimal(fmt), rounding=ROUND_HALF_UP)
        
        # 新增函数：从格式字符串提取货币符号
        def get_currency_symbol(format_str):
            # 货币代码到符号的映射表
            CURRENCY_MAP = {
                '€': '€', '$': '$', '£': '£', '¥': '¥',
                'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥',
                'CNY': '¥', 'INR': '₹', 'RUB': '₽', 'KRW': '₩',
                'TRY': '₺', 'UAH': '₴', 'CHF': 'CHF', 'AUD': 'A$',
                'CAD': 'C$', 'NZD': 'NZ$', 'ZAR': 'R'
            }

            # 1. 优先匹配Excel方括号格式（如[$€]、[$$-409]）
            bracket_match = re.search(r'\[\$([^\]]+)\]', format_str)
            if bracket_match:
                currency_code = bracket_match.group(1).split('-')[0]
                return CURRENCY_MAP.get(currency_code, currency_code)

            # 2. 匹配常见Unicode货币符号
            unicode_symbols = re.search(r'[\$€£¥₹₽₩₺₴]', format_str)
            if unicode_symbols:
                return unicode_symbols.group()

            # 3. 处理特殊符号别名
            if '￥' in format_str:
                return '¥'
            
            # 4. 匹配货币代码（如USD、EUR）
            code_match = re.search(r'\b([A-Z]{3})\b', format_str)
            if code_match and code_match.group(1) in CURRENCY_MAP:
                return CURRENCY_MAP[code_match.group(1)]

            # 5. 特殊处理复合符号（A$、C$等）
            if re.search(r'A\$', format_str):
                return 'A$'
            if re.search(r'C\$', format_str):
                return 'C$'
            if re.search(r'NZ\$', format_str):
                return 'NZ$'

            return None
        try:
            wb = xlrd.open_workbook(file_path, formatting_info=True)
            sheets_data = []
            sheets_text = []

            for i in range(wb.nsheets):
                sheet = wb.sheet_by_index(i)
                visibility = wb._sheet_visibility[i]
                
                # 跳过系统生成或空的 Sheet
                if sheet.name.startswith("XLR_") or sheet.nrows == 0 or sheet.ncols == 0 or visibility != 0:
                    continue

                # 预先获取合并单元格信息
                merged_cells = []
                for crange in sheet.merged_cells:
                    rlo, rhi, clo, chi = crange
                    merged_cells.append({
                        'range': (rlo, rhi, clo, chi),
                        'value': sheet.cell_value(rlo, clo)
                    })

                # 为当前sheet初始化HTML
                html = ['<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body><table border="1">']
                
                for row_idx in range(sheet.nrows):
                    html.append("<tr>")
                    
                    for col_idx in range(sheet.ncols):
                        cell_value = ""
                        rowspan = 1
                        colspan = 1
                        
                        # 处理合并单元格
                        for mc in merged_cells:
                            rlo, rhi, clo, chi = mc['range']
                            if (row_idx >= rlo and row_idx < rhi and 
                                col_idx >= clo and col_idx < chi):
                                if row_idx == rlo and col_idx == clo:  # 合并区域的左上角
                                    rowspan = rhi - rlo
                                    colspan = chi - clo
                                    cell_value = mc['value']
                                else:  # 跳过合并区域的其他单元格
                                    continue
                        
                        # 如果未处理合并单元格，则取普通单元格值
                        if not cell_value:
                            cell_value = sheet.cell_value(row_idx, col_idx)
                        
                        # 处理各种数据类型
                        if cell_value is not None and cell_value != '':
                            # 处理日期类型
                            if sheet.cell_type(row_idx, col_idx) == xlrd.XL_CELL_DATE:
                                try:
                                    cell_value = xlrd.xldate.xldate_as_datetime(
                                        cell_value, wb.datemode).strftime('%Y-%m-%d')
                                except Exception:
                                    pass
                            # 处理数字类型
                            elif sheet.cell_type(row_idx, col_idx) == xlrd.XL_CELL_NUMBER:
                                xf_index = sheet.cell_xf_index(row_idx, col_idx)
                                xf = wb.xf_list[xf_index]
                                fmt_key = xf.format_key
                                fmt_str = wb.format_map.get(fmt_key, None)
                                
                                if fmt_str and fmt_str.format_str != "General":
                                    decimal_places = get_decimal_places(fmt_str.format_str)
                                    currency_symbol = get_currency_symbol(fmt_str.format_str)
                                    cell_value = str(round_bankers(cell_value, decimal_places))
                                    if currency_symbol:
                                        cell_value = f"{currency_symbol}{cell_value}"
                                else:
                                    if isinstance(cell_value, float) and cell_value.is_integer():
                                        cell_value = int(cell_value)
                                    cell_value = str(cell_value)
                            # 清洗文本
                            cell_value = clean_text(str(cell_value))
                        else:
                            cell_value = "&nbsp;"
                        
                        # 添加单元格到HTML
                        rowspan_attr = f' rowspan="{rowspan}"' if rowspan > 1 else ''
                        colspan_attr = f' colspan="{colspan}"' if colspan > 1 else ''
                        html.append(f'<td{rowspan_attr}{colspan_attr}>{cell_value}</td>')
                    
                    html.append("</tr>")
                
                # 完成当前sheet的HTML
                html.append("</table></body></html>")
                sheet_content = "".join(html)
                
                # 保存当前sheet的数据
                sheet_data = {
                    "单据类型": sheet.name,
                    "单据内容": sheet_content
                }
                sheets_data.append(sheet_data)
                sheets_text.append(sheet_content)
            
            # 合并所有页面的文本
            ocr_text = "\n\n".join(sheets_text)
            # ocr_text = ocr_text.replace("\t", "\\\\t")
            # ocr_text = ocr_text.replace("\n", "\\\\n")
            ocr_text = json.dumps(ocr_text, ensure_ascii=False, indent=4)
            
            # 构建基础返回结构
            base_result = {
                "ocr_text": ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "xls",
                    "file_path": file_path
                }
            }
            
            # 根据Sheet数量决定contents格式
            if len(sheets_data) > 1:
                base_result["contents"] = sheets_data
            else:
                if flag:
                    # 调用大模型对content进行分割
                    if ie_flag == "I":
                        split_prompt = SPLIT_PROMPT_I.format(text=ocr_text)
                    else:
                        split_prompt = SPLIT_PROMPT.format(text=ocr_text)
                    split_message = [
                        {"role": "system", "content": "你是一个严谨的文档分割助手，只输出用户要求的JSON，不要添加其他文字"},
                        {"role": "user", "content": split_prompt}
                    ]
                    content = await self.ai_model.chat(split_message)
                    result = content.replace("```json", "").replace("```", "")
                    formatted = json.loads(result)
                    base_result["contents"] = formatted
                else:
                    formatted = {
                        "单据类型": "舱单",
                        "单据内容": ocr_text
                    }
                    base_result["contents"] = formatted
            
            return base_result
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"Excel处理失败: {str(e)}")
            return {
                "error": f"Excel(.xls)处理失败: {str(e)}",
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "xls",
                    "file_path": file_path
                }
            } 
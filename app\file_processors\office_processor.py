import logging
import os
from typing import Dict, Any, Optional
from .base import BaseFileProcessor
from ..ai_models.base import BaseAIModel
import docx
import openpyxl
import subprocess
import xlrd  # 添加xlrd库用于处理.xls文件

class OfficeProcessor(BaseFileProcessor):
    """Office文档处理器"""
    
    def __init__(self, ai_model: BaseAIModel):
        super().__init__(ai_model)
    
    async def process(self, file_path: str) -> Dict[str, Any]:
        """处理Office文档"""
        try:
            logging.info(f"开始处理Office文档: {file_path}")
            
            content = await self._extract_text(file_path)
            if isinstance(content, dict) and content.get("error"):
                return content
                
            return {
                "content": content,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": os.path.splitext(file_path)[1][1:],
                    "file_path": file_path
                }
            }
        except Exception as e:
            logging.error(f"Office文档处理失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "file_name": os.path.basename(file_path)
            }
    
    async def _extract_text(self, file_path: str) -> str:
        """提取文档文本内容"""
        ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if ext == '.docx':
                return self._extract_docx(file_path)
            elif ext == '.doc':
                return self._extract_doc(file_path)
            elif ext == '.xlsx':
                return self._extract_excel(file_path)
            elif ext == '.xls':
                return self._extract_xls(file_path)
            else:
                return {"error": f"不支持的文件类型: {ext}"}
        except Exception as e:
            logging.error(f"提取文本失败: {str(e)}", exc_info=True)
            return {"error": f"提取文本失败: {str(e)}"}
    
    def _extract_docx(self, file_path: str) -> str:
        """提取DOCX文档文本"""
        doc = docx.Document(file_path)
        full_text = []
        for para in doc.paragraphs:
            full_text.append(para.text)
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    # full_text.append(cell.text)
                    row_text.append(cell.text)
                full_text.append("\t".join(row_text))  # 使用制表符分隔表格单元格
        return '\n'.join(full_text)
    
    def _extract_doc(self, file_path: str) -> str:
        """提取DOC文档文本和表格内容"""
        try:
            # 使用 LibreOffice 将 .doc 转换为 .docx
            docx_file_path = os.path.splitext(file_path)[0] + ".docx"
            subprocess.run(
                ["soffice", "--headless", "--convert-to", "docx", file_path, "--outdir", os.path.dirname(file_path)],
                check=True
            )
            # 使用 python-docx 提取内容
            doc = docx.Document(docx_file_path)
            full_text = []
            for para in doc.paragraphs:
                full_text.append(para.text)
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text)
                    full_text.append("\t".join(row_text))  # 使用制表符分隔表格单元格
            return '\n'.join(full_text)
        except subprocess.CalledProcessError as e:
            print(f"Error converting DOC to DOCX: {e}")
            return ""
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""

    def _extract_excel(self, file_path: str) -> str:
        """提取Excel文档文本(.xlsx格式)"""
        try:
            wb = openpyxl.load_workbook(file_path, data_only=True)
            sheets_data = []
            for sheet in wb.worksheets:
                sheets_data.append(f"表格: {sheet.title}")
                rows_data = []
                for row in sheet.rows:
                    row_text = []
                    for cell in row:
                        if cell.value is not None:
                            row_text.append(str(cell.value))
                    if row_text:
                        rows_data.append("\t".join(row_text))
                sheets_data.append("\n".join(rows_data))
            return "\n\n".join(sheets_data)
        except Exception as e:
            logging.error(f"Excel处理失败: {str(e)}")
            return {"error": f"Excel(.xlsx)处理失败: {str(e)}"}
    
    def _extract_xls(self, file_path: str) -> str:
        """提取Excel文档文本(.xls格式)"""
        try:
            wb = xlrd.open_workbook(file_path)
            sheets_data = []
            for sheet in wb.sheets():
                sheets_data.append(f"表格: {sheet.name}")
                rows_data = []
                for row_idx in range(sheet.nrows):
                    row_text = []
                    for col_idx in range(sheet.ncols):
                        value = sheet.cell_value(row_idx, col_idx)
                        if value is not None and value != '':
                            # 处理日期类型
                            if sheet.cell_type(row_idx, col_idx) == xlrd.XL_CELL_DATE:
                                try:
                                    value = xlrd.xldate.xldate_as_datetime(value, wb.datemode).strftime('%Y-%m-%d')
                                except Exception:
                                    pass
                            row_text.append(str(value))
                    if row_text:
                        rows_data.append("\t".join(row_text))
                sheets_data.append("\n".join(rows_data))
            return "\n\n".join(sheets_data)
        except Exception as e:
            logging.error(f"Excel处理失败: {str(e)}")
            return {"error": f"Excel(.xls)处理失败: {str(e)}"} 
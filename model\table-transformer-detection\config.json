{"activation_dropout": 0.0, "activation_function": "relu", "architectures": ["TableTransformerForObjectDetection"], "attention_dropout": 0.0, "auxiliary_loss": false, "backbone": "resnet18", "bbox_cost": 5, "bbox_loss_coefficient": 5, "ce_loss_coefficient": 1, "class_cost": 1, "d_model": 256, "decoder_attention_heads": 8, "decoder_ffn_dim": 2048, "decoder_layerdrop": 0.0, "decoder_layers": 6, "dice_loss_coefficient": 1, "dilation": false, "dropout": 0.1, "encoder_attention_heads": 8, "encoder_ffn_dim": 2048, "encoder_layerdrop": 0.0, "encoder_layers": 6, "eos_coefficient": 0.4, "giou_cost": 2, "giou_loss_coefficient": 2, "id2label": {"0": "table", "1": "table rotated"}, "init_std": 0.02, "init_xavier_std": 1.0, "is_encoder_decoder": true, "label2id": {"table": 0, "table rotated": 1}, "mask_loss_coefficient": 1, "max_position_embeddings": 1024, "model_type": "table-transformer", "num_channels": 3, "num_hidden_layers": 6, "num_queries": 15, "position_embedding_type": "sine", "scale_embedding": false, "torch_dtype": "float32", "transformers_version": "4.24.0.dev0", "use_pretrained_backbone": true}
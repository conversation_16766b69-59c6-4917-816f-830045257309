// 加载可用模型列表
async function loadAvailableModels() {
    console.log("开始加载模型列表...");
    
    try {
        const response = await fetch('/api/models');
        if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`);
        }
        
        const models = await response.json();
        console.log("获取到模型数据:", models);
        
        const modelSelect = document.getElementById('model-provider');
        if (!modelSelect) {
            console.error("未找到模型选择元素!");
            return;
        }
        
        // 清空现有选项
        modelSelect.innerHTML = '';
        
        // 添加模型选项
        if (Object.keys(models).length === 0) {
            console.warn("模型列表为空!");
            // 添加一个默认选项
            const option = document.createElement('option');
            option.value = 'deepseek';
            option.textContent = 'DeepSeek - 默认模型';
            modelSelect.appendChild(option);
        } else {
            Object.entries(models).forEach(([key, model]) => {
                console.log(`添加模型选项: ${key}`);
                const option = document.createElement('option');
                option.value = key;
                option.textContent = `${model.name} - ${model.description}`;
                modelSelect.appendChild(option);
            });
        }
        
        // 强制确保Ollama为默认选中模型
        setTimeout(() => {
            const modelSelect = document.getElementById('model-provider');
            if (modelSelect) {
                modelSelect.value = 'ollama';
                console.log("强制设置默认模型为Ollama:", modelSelect.value);
            }
        }, 100); // 延迟一小段时间确保在其他脚本之后执行
    } catch (error) {
        console.error('加载模型列表失败:', error);
        
        // 出错时添加一个默认选项
        const modelSelect = document.getElementById('model-provider');
        if (modelSelect) {
            modelSelect.innerHTML = '';
            const option = document.createElement('option');
            option.value = 'deepseek';
            option.textContent = 'DeepSeek - 默认模型';
            modelSelect.appendChild(option);
        }
    }
}

// 确保DOM加载后再执行
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM已加载，准备获取模型列表");
    loadAvailableModels();
});

// 如果已经加载了DOM，立即执行
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    console.log("页面已经加载，立即获取模型列表");
    loadAvailableModels();
}

function displayResults(results) {
    console.log("接收到的结果:", results);
    // 解构新的数据结构
    const { detail_ocr, detail_format, result } = results;
    console.log("解构后的result:", result);
    const form = document.getElementById('resultForm');
    const errorInfo = document.getElementById('errorInfo');
    const goodsTable = document.getElementById('goodsTable').getElementsByTagName('tbody')[0];
    const resultTable = document.getElementById('resultTable');
    
    // 清空之前的内容
    goodsTable.innerHTML = '';
    errorInfo.style.display = 'none';
    resultTable.innerHTML = '';
    
    // 创建一个新的错误信息容器（放在最上面）
    const errorContainer = document.createElement('div');
    errorContainer.id = 'errorContainer';
    resultTable.appendChild(errorContainer);
    
    // 显示各个文件的错误信息
    if (detail_format && Array.isArray(detail_format)) {
        detail_format.forEach(item => {
            if (item.content && item.content.error) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mb-3';
                errorDiv.innerHTML = `<strong>${item.file_name}:</strong> ${item.content.error}`;
                errorContainer.appendChild(errorDiv);
            }
        });
    }
    
    // 显示合并结果的错误信息
    if (result && result.error) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger mb-3';
        errorDiv.innerHTML = `<strong>文件合并异常：</strong> ${result.error}`;
        errorContainer.appendChild(errorDiv);
    }
    
    // 检查表单元素是否存在
    if (!form) {
        console.error("未找到表单元素!");
        return;
    }
    
    // 获取所有input元素
    const inputs = form.querySelectorAll('input');
    console.log("表单中的所有input元素:", inputs);
    
    // 创建表单和表格容器
    const contentContainer = document.createElement('div');
    contentContainer.className = 'mt-4';
    resultTable.appendChild(contentContainer);
    
    // 确保result是数组且不为空
    // if (!Array.isArray(result) || result.length === 0) {
    //     errorInfo.textContent = "没有有效的处理结果";
    //     errorInfo.style.display = 'block';
    //     return;
    // }
    
    const data = result['content'];  // 获取第一个结果
    console.log("处理的数据:", data);
    
    // 确保data是对象
    if (!data || typeof data !== 'object') {
        errorInfo.textContent = "数据格式不正确";
        errorInfo.style.display = 'block';
        return;
    }
    
    // 填充表单字段
    for (let key in data) {
        if (key !== 'error' && key !== '商品信息' && key !== 'file_info' && key !== 'content' && key !== 'file_name') {
            const input = form.querySelector(`input[name="${key}"]`);
            console.log(`查找字段 ${key} 的input元素:`, input);
            if (input) {
                // 处理null或undefined值
                input.value = data[key] || '';
                console.log(`设置字段 ${key} 的值为:`, data[key]);
            } else {
                console.log(`未找到字段 ${key} 的输入框`);
            }
        }
    }
    
    // 填充商品信息表格
    const goodsInfo = data.商品信息;
    if (goodsInfo && Array.isArray(goodsInfo) && goodsInfo.length > 0) {
        console.log("商品信息:", goodsInfo);
        goodsInfo.forEach(item => {
            if (!item || typeof item !== 'object') return;
            const row = goodsTable.insertRow();
            row.insertCell(0).textContent = item.商品名称 || '';
            row.insertCell(1).textContent = item.规格型号 || '';
            row.insertCell(2).textContent = item.数量 || '';
            row.insertCell(3).textContent = item.单价 || '';
            row.insertCell(4).textContent = item.总价 || '';
            row.insertCell(5).textContent = item.商品编号 || '';
            row.insertCell(6).textContent = item.原产国 || '';
            row.insertCell(7).textContent = item.最终目的国 || '';
            row.insertCell(8).textContent = item.境内货源地 || '';
            row.insertCell(9).textContent = item.征免 || '';
        });
    } else {
        // 如果没有商品信息，显示提示
        const row = goodsTable.insertRow();
        const cell = row.insertCell(0);
        cell.colSpan = 10;
        cell.className = 'text-center text-muted';
        cell.textContent = '无商品信息';
    }
    
    // 创建底部按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'mt-4 pt-3 border-top';
    
    // 添加查看详细信息按钮
    if (true) {
        // 添加OCR结果按钮
        const ocrBtn = document.createElement('button');
        ocrBtn.className = 'btn btn-outline-info mt-3 me-2';
        ocrBtn.innerHTML = '<i class="fas fa-file-alt me-2"></i>查看OCR结果';
        ocrBtn.onclick = createDetailViewer(detail_ocr, 'OCR结果', buttonContainer);
        buttonContainer.appendChild(ocrBtn);
        
        // 添加格式化结果按钮
        const formatBtn = document.createElement('button');
        formatBtn.className = 'btn btn-outline-success mt-3 me-2';
        formatBtn.innerHTML = '<i class="fas fa-code me-2"></i>查看格式化结果';
        formatBtn.onclick = createDetailViewer(detail_format, '格式化结果', buttonContainer);
        buttonContainer.appendChild(formatBtn);
        
        // 原有的查看JSON按钮
        const debugBtn = document.createElement('button');
        debugBtn.className = 'btn btn-outline-secondary mt-3';
        debugBtn.innerHTML = '<i class="fas fa-code me-2"></i>查看最终结果';
        debugBtn.onclick = createDetailViewer(result, '最终结果', buttonContainer);
        buttonContainer.appendChild(debugBtn);
    }
    
    // 将按钮容器添加到结果区域的最底部
    resultTable.appendChild(buttonContainer);
}

// 创建详细信息查看器
function createDetailViewer(data, title, buttonContainer) {
    return function() {
        const existingDebug = document.getElementById('debug-output');
        if (existingDebug) {
            existingDebug.remove();
            this.innerHTML = `<i class="fas fa-code me-2"></i>查看${title}`;
            return;
        }
        
        const debugOutput = document.createElement('pre');
        debugOutput.className = 'json-viewer mt-3 p-3 bg-light border rounded fade-in';
        debugOutput.textContent = JSON.stringify(data, null, 2);
        debugOutput.id = 'debug-output';
        
        buttonContainer.appendChild(debugOutput);
        this.innerHTML = `<i class="fas fa-times me-2"></i>隐藏${title}`;
    };
} 
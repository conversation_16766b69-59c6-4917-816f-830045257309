# 特殊企业提示词
SP_PROMPTS = {
    "国投中鲁果汁股份有限公司1":
    """
    1、在备注字段（NoteS）上添加发票号，格式为发票号：1234（具体发票号（InvoiceNo）），如果没有发票号，那么不需要处理，如果没有NoteS字段，需要在返回的JSON里面增加NoteS
    2、商品申报要素中加工方法不取单据上数值默认：未经过发酵,未添加酒精,冷冻/非冷冻。冷冻还是非冷冻取单据上的保存方法
    3、商品申报要素中的成分含量不使用单据上的值，而是根据赋值规则：
        商品名（GName）为浓缩苹果清汁的成分为100%苹果汁
        商品名（GName）为浓缩苹果汁的成分为100%苹果汁   
        商品名（GName）为浓缩梨清汁的成分为100%梨汁
        商品名（GName）为浓缩梨汁的成分为100%梨汁
        商品名（GName）为浓缩红薯清汁的成分为100%红薯汁
        商品名（GName）为浓缩红薯汁的成分为100%红薯汁
        商品名（GName）为非浓缩白桃浆的成分为100%白桃浆
    4、运费处理：将现在的运费（FeeRate）*集装箱数量（DecContainer中有几条数据，就代表有几个集装箱）得到的运费更新到运费（FeeRate）字段
    ## 规则：
    1. **只修改用户明确要求的字段**，不改变其他任何字段。
    2. **保存方法（储藏温度）**字段的值应保留原样，**不进行任何修改**。即使其他字段有所更改，温度字段必须保持其原始格式，包括：
        - 保持**负号**（如“-18°C”）；
        - 保持**温度单位**（如“°C”），不去除或更改单位；
    3. 温度字段的格式和单位是**固定的**，模型不得对其进行格式化或清洗。
    4. **保存方法（储藏温度）**字段没有被明确要求修改，系统必须保持该字段不变。

    ## 处理细节：
    - **保存方法（储藏温度）**字段中的温度值必须保持其完整格式，包含负号和单位（例如：“冷冻，-18°C”应保持不变）。
    - 不允许系统对储藏温度字段进行任何类型的格式化、清洗或其他处理。
    """,
    "国投中鲁果汁股份有限公司":
    """
        1、在备注字段（NoteS）上添加发票号，格式为发票号：1234（具体发票号（InvoiceNo）），如果没有发票号，那么不需要处理，如果没有NoteS字段，需要在返回的JSON里面增加NoteS
        2、商品申报要素中加工方法不取单据上数值默认：未经过发酵,未添加酒精,冷冻/非冷冻。冷冻还是非冷冻取单据上的保存方法
        3、商品申报要素中的成分含量不使用单据上的值，而是根据赋值规则：
            商品名（GName）为浓缩苹果清汁的成分为100%苹果汁
            商品名（GName）为浓缩苹果汁的成分为100%苹果汁   
            商品名（GName）为浓缩梨清汁的成分为100%梨汁
            商品名（GName）为浓缩梨汁的成分为100%梨汁
            商品名（GName）为浓缩红薯清汁的成分为100%红薯汁
            商品名（GName）为浓缩红薯汁的成分为100%红薯汁
            商品名（GName）为非浓缩白桃浆的成分为100%白桃浆
        4、运费处理：将现在的运费（FeeRate）*集装箱数量（DecContainer中有几条数据，就代表有几个集装箱）得到的运费更新到运费（FeeRate）字段
    """,
    "Cangshan Foodservice Products Co., Ltd.":
    "商品编号是3924100000申报要素中的用途统一更改为：厨房用"
}

def format_by_tradeName(sys_rules,json):
    print("======",sys_rules)
    if sys_rules:
        return f"""
        # 任务：根据要求对输入的JSON数据进行更改，必须保证原JSON的格式不发生变化。
        
        ## 说明
        你的任务是根据用户的要求对输入的JSON进行修改，然后将修改完成的JSON返回，只针对修改要求的字段进行修改，其他字段不要有任何改动
        ## 注意
        1、JSON中的申报要素原始为最原始的申报要素相关内容，只用于信息参考，不进行任何修改
    
        ## JSON修改要求
        {sys_rules}
        
        ## 输入的JSON：
        {json}
        
        ## 注意
        - 只返回JSON格式的结果，不要添加任何其他解释
    """
    else:
        return ""
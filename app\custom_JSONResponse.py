from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from typing import Any, Optional, Dict

class CustomJSONResponse(JSONResponse):
    def __init__(
        self,
        code: int = 200,  # 自定义 code 参数
        data: Any = None,
        message: str = "success",
        **kwargs
    ):
        content = {
            "code": code,
            "message": message,
            "data": data
        }
        super().__init__(content=jsonable_encoder(content), status_code=code, **kwargs)
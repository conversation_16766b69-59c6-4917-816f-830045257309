from typing import Optional
from fastapi import Depends
from app.ocr.ocr_initializer import OCRInitializer

_ocr_initializer: Optional[OCRInitializer] = None

def get_ocr_initializer() -> OCRInitializer:
    if _ocr_initializer is None:
        raise RuntimeError("OCR资源未初始化")
    return _ocr_initializer

def set_ocr_initializer(initializer: OCRInitializer):
    global _ocr_initializer
    if not isinstance(initializer, OCRInitializer):
        raise TypeError("必须传递OCRInitializer实例")
    _ocr_initializer = initializer

ocr_dependency = Depends(get_ocr_initializer) 
# 类似于DeepSeek处理器的实现 

import logging
import json
import os
import aiohttp
import asyncio
from typing import Dict, Any, List, Optional
from .base import BaseAIModel
import time


class DoubaoHandler(BaseAIModel):
    """豆包模型处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_url = config["api_url"]
        self.api_key = config["api_key"]
        self.max_tokens = config.get("max_tokens", 12000)
        self.model_id = config.get("model", "doubao-v1")
        self.session = aiohttp.ClientSession()  # 创建异步会话
    def __del__(self):
        if not hasattr(self, 'session') or self.session.closed:
            return
        
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():  # 检查循环是否运行中
                loop.create_task(self.session.close())  # 非阻塞提交任务
            else:
                loop.run_until_complete(self.session.close())
        except RuntimeError:  # 无事件循环时直接放弃
            pass
    async def close(self):
        """关闭模型"""
        if not self.session.closed:
            await self.session.close()
    def _calculate_message_length(self, messages: List[Dict[str, str]]) -> int:
        """计算消息总长度"""
        return sum(len(msg.get("content", "")) for msg in messages)
    
    async def chat(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """调用豆包 API"""
        start_time = time.perf_counter()  # 开始计时
        msg_length = self._calculate_message_length(messages)
        print(messages)
        if msg_length > 4000:
            #如果内容长度过长，直接使用流式输出
            return await self._stream_chat(messages, self.model_id)
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_id,
                "messages": messages,
                "temperature": 0.1,
                "top_p": 0.9,
                "stream": False
            }
            
            async with self.session.post(
                f"{self.api_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=200
            ) as response:
                elapsed = time.perf_counter() - start_time  # 结束计时
                # 统一获取原始响应文本
                response_text = await response.text()
                # HTTP错误处理
                if response.status != 200:
                    logging.error(f"API错误 [{response.status}]: {response_text[:500]}")
                    return None

                # 手动解析JSON
                try:
                    result = json.loads(response_text)
                    print(result)
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败 | 错误: {e} | 响应内容: {response_text[:200]}...")
                    return None

                # 验证响应结构
                if not isinstance(result.get("choices"), list) or len(result["choices"]) == 0:
                    logging.error(f"响应结构异常: {result.keys()}")
                    return None

                return result["choices"][0].get("message", {}).get("content")
                
        except aiohttp.ClientError as e:
            logging.error(f"网络请求失败: {str(e)}")
        except asyncio.TimeoutError:
            logging.error("API请求超时")
            return await self._stream_chat(messages, self.model_id)
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"调用豆包 API失败: {str(e)}")
            return None
        return None
    
    async def _stream_chat(self, messages: List[Dict[str, str]], model: str) -> Optional[str]:
        """启用流式模式调用"""
        stream_headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "X-DashScope-SSE": "enable"
        }

        stream_payload = {
            "model": model,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "stream": True,
            "parameters": {
                "temperature": 0.1,
                "top_p": 0.9,
                "result_format": "message"
            }
        }

        try:
            timeout = aiohttp.ClientTimeout(total=900)  # 设置 60 秒超时
            async with self.session.post(
                f"{self.api_url}/chat/completions",
                headers=stream_headers,
                json=stream_payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    text = await resp.text()
                    logging.error(f"流式响应失败 [{resp.status}]: {text[:300]}")
                    return None

                full_response = ""
                print("开始流式输出")
                # print(resp.content)
                async for line in resp.content:
                    line = line.decode("utf-8").strip()
                    # print("line:",line)
                    if line.startswith("data: "):
                        data_str = line[6:].strip()
                        if data_str == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            delta = data.get("choices", [{}])[0].get("delta", {}).get("content", "")
                            full_response += delta
                        except Exception as e:
                            logging.warning(f"解析流式数据出错: {e}，原始数据: {data_str[:200]}")
                            continue
                print("流式输出结束，返回结果:",full_response)
                return full_response or None

        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"流式输出失败: {e}")
            return None

    
    
    
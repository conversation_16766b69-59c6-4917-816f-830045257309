# 类似于DeepSeek处理器的实现 

import logging
import json
import os
import aiohttp
import asyncio
from typing import Dict, Any, List, Optional
from .base import BaseAIModel

class DoubaoHandler(BaseAIModel):
    """豆包模型处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_url = config["api_url"]
        self.api_key = config["api_key"]
        self.max_tokens = config.get("max_tokens", 10000)
        self.model_id = config.get("model", "doubao-v1")
        self.session = aiohttp.ClientSession()  # 创建异步会话
    async def close(self):
        """关闭模型"""
        await self.session.close()
        
    async def chat(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """调用豆包 API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_id,
                "messages": messages,
                "temperature": 0.1,
                "top_p": 0.9,
                "stream": False
            }
            
            async with self.session.post(
                f"{self.api_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=200
            ) as response:
                # 统一获取原始响应文本
                response_text = await response.text()

                # HTTP错误处理
                if response.status != 200:
                    logging.error(f"API错误 [{response.status}]: {response_text[:500]}")
                    return None

                # 手动解析JSON
                try:
                    result = json.loads(response_text)
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败 | 错误: {e} | 响应内容: {response_text[:200]}...")
                    return None

                # 验证响应结构
                if not isinstance(result.get("choices"), list) or len(result["choices"]) == 0:
                    logging.error(f"响应结构异常: {result.keys()}")
                    return None

                return result["choices"][0].get("message", {}).get("content")
                
        except aiohttp.ClientError as e:
            logging.error(f"网络请求失败: {str(e)}")
        except asyncio.TimeoutError:
            logging.error("API请求超时")
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"调用豆包 API失败: {str(e)}")
            return None
        return None
    
    
    
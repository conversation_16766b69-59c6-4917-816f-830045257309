from typing import Optional, List, Dict, Any, Union, Tuple
from mysql.connector import pooling, cursor
from db.connector import MySQLConnector

class DBQuery:
    """数据库通用查询接口"""
    
    @staticmethod
    def execute(
        sql: str, 
        params: Optional[Union[Tuple, Dict]] = None,
        fetch: bool = False,
        commit: bool = False,
        many: bool = False
    ) -> Optional[Union[List[Dict], int]]:
        """
        执行SQL查询
        
        :param sql: SQL语句
        :param params: 参数(元组或字典)
        :param fetch: 是否获取结果
        :param commit: 是否提交事务
        :param many: 是否批量操作
        :return: 查询结果或影响行数
        """
        conn = None
        cursor = None
        try:
            # 获取连接
            conn = MySQLConnector.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 执行SQL
            if many and params:
                cursor.executemany(sql, params)
            else:
                cursor.execute(sql, params)
            
            # 处理结果
            result = None
            if fetch:
                result = cursor.fetchall()
            if commit:
                conn.commit()
                result = cursor.rowcount
            
            return result
            
        except Exception as e:
            if conn:
                conn.rollback()
            raise RuntimeError(f"数据库操作失败: {e}")
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    @staticmethod
    def fetch_one(sql: str, params: Optional[Union[Tuple, Dict]] = None) -> Optional[Dict]:
        """查询单条记录"""
        result = DBQuery.execute(sql, params, fetch=True)
        return result[0] if result else None
    
    @staticmethod
    def fetch_all(sql: str, params: Optional[Union[Tuple, Dict]] = None) -> List[Dict]:
        """查询多条记录"""
        return DBQuery.execute(sql, params, fetch=True) or []
    
    @staticmethod
    def insert(sql: str, params: Union[Tuple, Dict]) -> int:
        """插入数据并返回影响行数"""
        return DBQuery.execute(sql, params, commit=True) or 0
    
    @staticmethod
    def insert_many(sql: str, params: List[Union[Tuple, Dict]]) -> int:
        """批量插入数据并返回影响行数"""
        return DBQuery.execute(sql, params, commit=True, many=True) or 0
    
    @staticmethod
    def update(sql: str, params: Union[Tuple, Dict]) -> int:
        """更新数据并返回影响行数"""
        return DBQuery.execute(sql, params, commit=True) or 0
    
    @staticmethod
    def delete(sql: str, params: Union[Tuple, Dict]) -> int:
        """删除数据并返回影响行数"""
        return DBQuery.execute(sql, params, commit=True) or 0
import logging
import os
import tempfile
import zipfile
import shutil
from typing import Dict, Any, List
from .base import BaseFileProcessor
from ..ai_models.base import BaseAIModel

class ZipProcessor(BaseFileProcessor):
    """ZIP文件处理器"""
    
    def __init__(self, ai_model: BaseAIModel, file_processor_factory=None):
        super().__init__(ai_model)
        self.file_processor_factory = file_processor_factory
    
    async def process(self, file_path: str) -> List[Dict[str, Any]]:
        """处理ZIP压缩文件"""
        try:
            logging.info(f"开始处理ZIP文件: {file_path}")
            
            # 检查文件处理器工厂
            if self.file_processor_factory is None:
                return [{"error": "ZIP处理器未配置文件处理器工厂"}]
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            logging.info(f"创建临时目录: {temp_dir}")
            
            try:
                # 解压文件
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                
                # 查找所有解压后的文件
                all_files = []
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if not file.startswith('.'):  # 排除隐藏文件
                            file_path = os.path.join(root, file)
                            all_files.append(file_path)
                
                logging.info(f"解压出{len(all_files)}个文件")
                
                # 处理每个文件
                results = []
                for extracted_file in all_files:
                    try:
                        result = await self.file_processor_factory.process_file(extracted_file)
                        if result:
                            # 添加相对路径信息
                            rel_path = os.path.relpath(extracted_file, temp_dir)
                            if isinstance(result, dict):
                                result["archive_path"] = rel_path
                                results.append(result)
                            elif isinstance(result, list):
                                for item in result:
                                    if isinstance(item, dict):
                                        item["archive_path"] = rel_path
                                        results.append(item)
                    except Exception as e:
                        logging.error(f"处理解压文件失败: {extracted_file} - {str(e)}")
                        results.append({
                            "error": str(e),
                            "archive_path": os.path.relpath(extracted_file, temp_dir)
                        })
                
                return results
                
            finally:
                # 清理临时目录
                shutil.rmtree(temp_dir, ignore_errors=True)
                
        except Exception as e:
            logging.error(f"ZIP处理失败: {str(e)}", exc_info=True)
            return [{"error": str(e)}] 
import logging
from typing import Dict, Any, List
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR
import subprocess
import torch
from transformers import DetrImageProcessor, TableTransformerForObjectDetection
from pdf2image import convert_from_path
import platform
import os
import cv2

os.environ["TOKENIZERS_PARALLELISM"] = "false"  # 禁用tokenizer并行
class OCRProcessor:
    """OCR文本识别处理器"""
    
    def __init__(self):
        """
        接收已初始化的OCR资源
        ocr_initializer: OCRInitializer实例
        """
        
        self.ocr = PaddleOCR(
            use_angle_cls=True, 
            lang="ch", 
            use_gpu=True,
            rec_model_dir='./model/v3.0/ch_PP-OCRv3_rec_infer/',
            cls_model_dir='./model/v3.0/ch_ppocr_mobile_v2.0_cls_infer/',
            det_model_dir='./model/v3.0/ch_PP-OCRv3_det_infer/'
        )
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        # 初始化表格检测模型
        self.processor = DetrImageProcessor.from_pretrained("./model/table-transformer-detection",local_files_only=True, use_safetensors=True)
        self.table_model = TableTransformerForObjectDetection.from_pretrained(
            "./model/table-transformer-detection",
            local_files_only=True,
            trust_remote_code=True,
            use_safetensors=True
        ).to(device)

        try:
            # 尝试通过brew获取路径（兼容Intel和Apple Silicon）
            brew_path = subprocess.check_output(["brew", "--prefix", "poppler"], stderr=subprocess.DEVNULL).decode().strip()
            detected_poppler_path = f"{brew_path}/bin"
        except (subprocess.CalledProcessError, FileNotFoundError):
            # 如果brew不可用，尝试常见安装路径
            common_paths = [
                "/opt/homebrew/opt/poppler/bin",  # Apple Silicon
                "/usr/local/opt/poppler/bin"      # Intel Mac
            ]
            for path in common_paths:
                if os.path.exists(f"{path}/pdfimages"):
                    detected_poppler_path = path
                    break
            else:
                raise RuntimeError("未找到poppler路径，请通过brew安装: brew install poppler")
        
        self.poppler_path = detected_poppler_path

    def recognize_text(self, image):
        """识别图片中的文本"""
        result = self.ocr.ocr(np.array(image), cls=True)
        text_data = []
        for line in result:
            if not line:
                continue
            for item in line:
                try:
                    if isinstance(item, list) and len(item) >= 2:
                        bbox = item[0]
                        text_info = item[1]
                        text = text_info[0] if isinstance(text_info, (list, tuple)) else text_info
                        text_data.append({"text": str(text), "bbox": bbox})
                except Exception as e:
                    logging.warning(f"解析OCR结果时出错: {str(e)}，忽略该项")
        return text_data

    def detect_tables(self, image):
        """检测图片中的表格"""
        inputs = self.processor(images=image, return_tensors="pt")
        with torch.no_grad():
            outputs = self.table_model(**inputs)
        target_sizes = torch.tensor([image.size[::-1]])
        results = self.processor.post_process_object_detection(outputs, target_sizes=target_sizes)[0]

        scores = results["scores"]
        if isinstance(scores, list) and len(scores) > 0 and isinstance(scores[0], list):
            scores = [s[0] for s in scores]

        return [{"bbox": box.tolist()} for score, label, box in zip(scores, results["labels"], results["boxes"]) if isinstance(score, float) and score > 0.5]

    def is_overlap(self, bbox1, bbox2, threshold=0.5):
        """检查两个边界框是否重叠"""
        x1, y1, x2, y2 = bbox1
        a1, b1, a2, b2 = bbox2
        inter_x1, inter_y1 = max(x1, a1), max(y1, b1)
        inter_x2, inter_y2 = min(x2, a2), min(y2, b2)
        if inter_x1 >= inter_x2 or inter_y1 >= inter_y2:
            return False
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        bbox1_area = (x2 - x1) * (y2 - y1)
        bbox2_area = (a2 - a1) * (b2 - b1)
        iou = inter_area / (bbox1_area + bbox2_area - inter_area)
        return iou > threshold

    async def process_image(self, image) -> Dict[str, Any]:
        """处理单张图片
        Args:
            image: 图片路径或PIL Image对象
        Returns:
            Dict[str, Any]: OCR结果
        """
        try:
            if isinstance(image, str) and os.path.exists(image):
                image = cv2.imread(image)
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            elif isinstance(image, Image.Image):
                image = np.array(image)
            
            # 使用PaddleOCR进行识别
            result = self.ocr.ocr(image, cls=True)
            
            # 处理PaddleOCR返回None的情况
            if result is None:
                logging.warning("PaddleOCR返回空结果，可能图片格式不支持")
                return {
                    "text": [],
                    "status": "success",
                    "warning": "图片格式不支持或无法解析"
                }
            
            # 检查OCR结果有效性
            if not result or not isinstance(result, list) or len(result) == 0:
                logging.warning(f"未检测到有效文本内容: {image.size if hasattr(image, 'size') else 'Unknown'}")
                return {
                    "text": [],
                    "status": "success",  # 视为成功但无内容
                    "warning": "未检测到可识别文本"
                }
            
            # 提取文本
            text_list = []
            if isinstance(result[0], list) and len(result[0]) > 0:
                for line in result[0]:
                    if line and len(line) >= 2:
                        text = line[1][0]  # PaddleOCR格式: [[[x1,y1],[x2,y2]...], (text, confidence)]
                        text_list.append(text)
            
            return {
                "text": text_list,
                "status": "success"
            }
            
        except Exception as e:
            logging.error(f"图片处理失败: {str(e)}", exc_info=True)
            return {
                "text": [],
                "status": "failed",
                "error": str(e)
            } 
        

if __name__ == "__main__":
    processor = OCRProcessor()
    processor.process_image("/Users/<USER>/Documents/讯吉安/大模型/AI制单/山东迅吉安/镭刻-空运3/test.png")
# 征免性质
FREE_NATURE = {
    "101":"一般征税",
    "118":"整车征税",
    "119":"零部件征税",
    "201":"无偿援助",
    "299":"其他法定",
    "301":"特定区域",
    "307":"保税区  ",
    "399":"其他地区",
    "401":"科教用品",
    "402":"示范平台用品",
    "403":"技术改造",
    "405":"科技开发用品",
    "406":"重大项目",
    "407":"动漫用品",
    "408":"重大技术装备",
    "409":"科技重大专项",
    "412":"基础设施",
    "413":"残疾人  ",
    "417":"远洋渔业",
    "418":"国产化  ",
    "419":"整车特征",
    "422":"集成电路",
    "423":"新型显示器件",
    "426":"集成电路和软件企业进口设备",
    "428":"集成电路产业进口货物",
    "481":"合作区自用设备",
    "482":"合作区基建物资",
    "490":"暂出修理货物",
    "491":"零关税自用生产设备",
    "492":"零关税交通工具及游艇",
    "493":"零关税自用生产设备缴纳进口环节税",
    "494":"零关税交通工具游艇缴纳进口环节税",
    "495":"暂进修理内销货物",
    "496":"含进口料件加工增值货物",
    "497":"不含进口料件加工增值货物",
    "498":"加工贸易副产品",
    "499":"ITA产品",
    "501":"加工设备",
    "502":"来料加工",
    "503":"进料加工",
    "506":"边境小额",
    "510":"港澳OPA",
    "591":"零关税原辅料",
    "592":"原辅料部分征税",
    "593":"内外贸同船运输加注保税油",
    "594":"内外贸同船运输加注本地生产燃料油",
    "595":"进出岛航班加注保税油全免",
    "596":"进出岛航班加注保税油部分征税",
    "601":"中外合资",
    "602":"中外合作",
    "603":"外资企业",
    "605":"勘探开发煤层气",
    "606":"海洋石油",
    "608":"陆上石油",
    "609":"贷款项目",
    "610":"海上应急救援",
    "611":"贷款中标",
    "666":"加征关税排除措施",
    "686":"市场化组织采购排除措施",
    "698":"公益收藏",
    "701":"部分进口饲料",
    "702":"中资方便旗船",
    "703":"航空公司进口飞机",
    "704":"花卉种子",
    "705":"科普事业",
    "706":"租赁企业进口飞机",
    "707":"博览会留购展品",
    "708":"替代种植农产品",
    "789":"鼓励项目",
    "799":"自有资金",
    "801":"救灾捐赠",
    "802":"慈善捐赠",
    "803":"抗艾滋病药物",
    "811":"种子种源",
    "815":"野生动植物",
    "818":"中央储备粮油",
    "819":"科教图书",
    "888":"航材减免",
    "898":"国批减免",
    "899":"选择征税",
    "901":"科研院所",
    "902":"高等学校",
    "903":"工程研究中心",
    "904":"国家企业技术中心",
    "905":"转制科研机构",
    "906":"重点实验室",
    "907":"国家工程技术研究中心",
    "908":"社会研发机构",
    "909":"示范平台",
    "910":"外资研发中心",
    "911":"出版物进口单位",
    "912":"国家实验室",
    "913":"国家产业创新中心",
    "915":"国家制造业创新中心",
    "916":"国家技术创新中心",
    "917":"公共图书馆",
    "918":"国家临床医学研究中心",
    "919":"党校（行政学院）",
    "921":"大型客机研制物资",
    "922":"进博会留购展品",
    "924":"消防救援装备",
    "926":"服贸会留购展品",
    "927":"进口钻石",
    "928":"三代核电",
    "930":"疫情防控物资",
    "931":"消博会留购展品",
    "932":"邮轮直供",
    "939":"运动会",
    "940":"技能大赛",
    "941":"广交会留购展品",
    "942":"高水平医院",
    "943":"高水平医院科教图书",
    "997":"自贸协定",
    "998":"内部暂定",
    "999":"例外减免",
}

def free_nature_handler(input_free_nature):
    for code,nature in FREE_NATURE.items():
        if input_free_nature == nature or input_free_nature == code or input_free_nature in nature:
            return code,nature
    return None,input_free_nature
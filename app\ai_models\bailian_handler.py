import logging
import json
import aiohttp
import asyncio
import time
from typing import Dict, Any, List, Optional
from .base import BaseAIModel

class BailianHandler(BaseAIModel):
    """百炼模型处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_url = config["api_url"]
        self.api_key = config["api_key"]
        self.max_tokens = config.get("max_tokens", 10000)
        self.model_id = config.get("model", "qwen-turbo-latest")
        self.model_max_id = config.get("model_max", "qwen-max-latest")
        self.model_plus_id = config.get("model_plus", "qwen-plus-latest")
        self.model_long_id = config.get("model_long", "qwen-long-latest")
        timeout = aiohttp.ClientTimeout(total=60)
        self.session = aiohttp.ClientSession(timeout=timeout)
    def __del__(self):
        if not hasattr(self, 'session') or self.session.closed:
            return
        
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():  # 检查循环是否运行中
                loop.create_task(self.session.close())  # 非阻塞提交任务
            else:
                loop.run_until_complete(self.session.close())
        except RuntimeError:  # 无事件循环时直接放弃
            pass
    async def close(self):
        """关闭模型"""
        if not self.session.closed:
            await self.session.close()

    def _calculate_message_length(self, messages: List[Dict[str, str]]) -> int:
        """计算消息总长度"""
        return sum(len(msg.get("content", "")) for msg in messages)

    async def chat(self, messages: List[Dict[str, str]]) -> Optional[str]:
        start_time = time.perf_counter()  # 开始计时
        print(f"messages: {messages}")
        """调用百炼API，如果超时则使用流式模式"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "X-DashScope-SSE": "disable"
        }
        msg_length = self._calculate_message_length(messages)
        select_model = self.model_id
        if msg_length > 2000:
            select_model = self.model_max_id
        if msg_length > 3000:
            select_model = self.model_plus_id
            # return await self._stream_chat(messages, select_model)
        if msg_length > 4000:
            #如果内容长度过长，直接使用流式输出
            select_model = self.model_long_id
            return await self._stream_chat(messages, select_model)
            
        print(f"计算消息总长度: {msg_length}，选择模型: {select_model}")
        print(f"messages: {messages}")
        payload = {
            "model": select_model,
            "messages": messages,
            "parameters": {
                "temperature": 0.1,
                "top_p": 0.9,
                "result_format": "message"
            }
        }
        try:
            timeout = aiohttp.ClientTimeout(total=900)  # 设置 60 秒超时
            async with self.session.post(
                f"{self.api_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=timeout
            ) as response:
                elapsed = time.perf_counter() - start_time  # 结束计时
                response_text = await response.text()
                print("response_text:",response_text)
                if response.status == 200:
                    result = json.loads(response_text)
                    if not isinstance(result.get("choices"), list) or not result["choices"]:
                        logging.error(f"响应结构异常: {result.keys()}")
                        return None
                    return result["choices"][0].get("message", {}).get("content")
                else:
                    logging.error(f"API错误 [{response.status}]: {response_text[:500]}")
                    print(f"尝试启用流式输出: {select_model},消息长度: {msg_length}")
                    return await self._stream_chat(messages, select_model)
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logging.warning(f"第一次请求超时或异常，尝试启用流式输出: {str(e)}")
            logging.info(f"尝试启用流式输出: {select_model},消息长度: {msg_length}")
            return await self._stream_chat(messages, select_model)
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"调用百炼API异常: {str(e)}")
            return None

    async def _stream_chat(self, messages: List[Dict[str, str]], model: str) -> Optional[str]:
        """启用流式模式调用"""
        stream_headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "X-DashScope-SSE": "enable"
        }

        stream_payload = {
            "model": model,
            "messages": messages,
            "stream": True,
            "enable_thinking":True,
            "parameters": {
                "temperature": 0.1,
                "top_p": 0.9,
                "result_format": "message"
            }
        }

        try:
            timeout = aiohttp.ClientTimeout(total=900)  # 设置 60 秒超时
            async with self.session.post(
                f"{self.api_url}/chat/completions",
                headers=stream_headers,
                json=stream_payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    text = await resp.text()
                    logging.error(f"流式响应失败 [{resp.status}]: {text[:300]}")
                    return None

                full_response = ""
                print("开始流式输出")
                print(resp.content)
                async for line in resp.content:
                    line = line.decode("utf-8").strip()
                    print("line:",line)
                    if line.startswith("data: "):
                        data_str = line[6:].strip()
                        if data_str == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            delta = data.get("choices", [{}])[0].get("delta", {}).get("content", "")
                            full_response += delta
                        except Exception as e:
                            logging.warning(f"解析流式数据出错: {e}，原始数据: {data_str[:200]}")
                            continue
                return full_response or None

        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"流式输出失败: {e}")
            return None
    async def chat_code(self, messages: List[Dict[str, str]]) -> Optional[str]:
        start_time = time.perf_counter()  # 开始计时
        print(f"messages: {messages}")
        """调用百炼API，如果超时则使用流式模式"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "X-DashScope-SSE": "disable"
        }
        msg_length = self._calculate_message_length(messages)
        select_model = self.model_id
        if msg_length > 2000:
            select_model = self.model_max_id
        if msg_length > 3000:
            select_model = self.model_plus_id
            # return await self._stream_chat(messages, select_model)
        if msg_length > 4000:
            #如果内容长度过长，直接使用流式输出
            select_model = self.model_long_id
            return await self._stream_chat(messages, select_model)
            
        print(f"计算消息总长度: {msg_length}，选择模型: {select_model}")
        print(f"messages: {messages}")
        payload = {
            "model": "qwen3-coder-plus",
            "messages": messages,
            "parameters": {
                "temperature": 0.1,
                "top_p": 0.9,
                "result_format": "message"
            }
        }
        try:
            timeout = aiohttp.ClientTimeout(total=900)  # 设置 60 秒超时
            async with self.session.post(
                f"{self.api_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=timeout
            ) as response:
                elapsed = time.perf_counter() - start_time  # 结束计时
                response_text = await response.text()
                print("response_text:",response_text)
                if response.status == 200:
                    result = json.loads(response_text)
                    if not isinstance(result.get("choices"), list) or not result["choices"]:
                        logging.error(f"响应结构异常: {result.keys()}")
                        return None
                    return result["choices"][0].get("message", {}).get("content")
                else:
                    logging.error(f"API错误 [{response.status}]: {response_text[:500]}")
                    print(f"尝试启用流式输出: {select_model},消息长度: {msg_length}")
                    return await self._stream_chat(messages, select_model)
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logging.warning(f"第一次请求超时或异常，尝试启用流式输出: {str(e)}")
            logging.info(f"尝试启用流式输出: {select_model},消息长度: {msg_length}")
            return await self._stream_chat(messages, select_model)
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"调用百炼API异常: {str(e)}")
            return None
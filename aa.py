import logging
from typing import Dict, Any, List
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR
import subprocess
import torch
from transformers import DetrImageProcessor, TableTransformerForObjectDetection
from pdf2image import convert_from_path
import platform
import os
import cv2

os.environ["TOKENIZERS_PARALLELISM"] = "false"  # 禁用tokenizer并行

class OCRProcessor:
    """OCR文本识别处理器"""
    
    def __init__(self):
        """
        接收已初始化的OCR资源
        ocr_initializer: OCRInitializer实例
        """
        
        self.ocr = PaddleOCR(
            use_angle_cls=True, 
            lang="ch", 
            use_gpu=True,
            rec_model_dir='model/v3.0/ch_PP-OCRv3_rec_infer/',
            cls_model_dir='model/v3.0/ch_ppocr_mobile_v2.0_cls_infer/',
            det_model_dir='model/v3.0/ch_PP-OCRv3_det_infer/'
        )
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        # 初始化表格检测模型
        self.processor = DetrImageProcessor.from_pretrained("model/table-transformer-detection", local_files_only=True, use_safetensors=True)
        self.table_model = TableTransformerForObjectDetection.from_pretrained(
            "model/table-transformer-detection",
            local_files_only=True,
            trust_remote_code=True,
            use_safetensors=True
        ).to(device)

        try:
            # 尝试通过brew获取路径（兼容Intel和Apple Silicon）
            brew_path = subprocess.check_output(["brew", "--prefix", "poppler"], stderr=subprocess.DEVNULL).decode().strip()
            detected_poppler_path = f"{brew_path}/bin"
        except (subprocess.CalledProcessError, FileNotFoundError):
            # 如果brew不可用，尝试常见安装路径
            common_paths = [
                "/opt/homebrew/opt/poppler/bin",  # Apple Silicon
                "/usr/local/opt/poppler/bin"      # Intel Mac
            ]
            for path in common_paths:
                if os.path.exists(f"{path}/pdfimages"):
                    detected_poppler_path = path
                    break
            else:
                raise RuntimeError("未找到poppler路径，请通过brew安装: brew install poppler")
        
        self.poppler_path = detected_poppler_path

    def recognize_text(self, image):
        """识别图片中的文本"""
        result = self.ocr.ocr(np.array(image), cls=True)
        text_data = []
        for line in result:
            if not line:
                continue
            for item in line:
                try:
                    if isinstance(item, list) and len(item) >= 2:
                        bbox = item[0]
                        text_info = item[1]
                        text = text_info[0] if isinstance(text_info, (list, tuple)) else text_info
                        text_data.append({"text": str(text), "bbox": bbox})
                except Exception as e:
                    logging.warning(f"解析OCR结果时出错: {str(e)}，忽略该项")
        return text_data

    def detect_tables(self, image):
        """返回绝对坐标的表格bbox"""
        if image.mode != "RGB":
            image = image.convert("RGB")
            
        # 保存原始尺寸
        width, height = image.size
        
        inputs = self.processor(
            images=image, 
            return_tensors="pt"
        ).to(self.table_model.device)
        
        with torch.no_grad():
            outputs = self.table_model(**inputs)
        
        # 转换为绝对坐标
        target_sizes = torch.tensor([(height, width)])
        results = self.processor.post_process_object_detection(
            outputs,
            target_sizes=target_sizes,
            threshold=0.3  # 适当降低阈值
        )[0]
        
        return [
            {
                "bbox": [
                    box[0].item(),  # xmin
                    box[1].item(),  # ymin
                    box[2].item(),  # xmax
                    box[3].item()   # ymax
                ],
                "score": score.item()
            }
            for score, box in zip(results["scores"], results["boxes"])
            if score > 0.3  # 置信度过滤
        ]

    def is_overlap(self, ocr_bbox, table_bbox, threshold=0.1):
        """处理不同格式的bbox比较"""
        
        # 处理OCR的四边形bbox：转换为矩形
        if isinstance(ocr_bbox[0], (list, tuple)):
            # PaddleOCR的四边形坐标 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]
            x_coords = [p[0] for p in ocr_bbox]
            y_coords = [p[1] for p in ocr_bbox]
            ocr_rect = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
        else:
            # 已经是矩形格式 [x1,y1,x2,y2]
            ocr_rect = ocr_bbox

        # 处理表格的归一化坐标（假设table_bbox是归一化的）
        # 转换为绝对坐标（需要知道图像尺寸，这里需要额外参数）
        # 注意：根据实际情况调整这部分
        if all(0 <= x <= 1 for x in table_bbox):
            # 如果是归一化坐标，转换为绝对坐标
            img_width, img_height = self.current_image_size  # 需要在处理时保存图像尺寸
            table_rect = [
                table_bbox[0] * img_width,
                table_bbox[1] * img_height,
                table_bbox[2] * img_width,
                table_bbox[3] * img_height
            ]
        else:
            table_rect = table_bbox

        # 转换为浮点数
        x1, y1, x2, y2 = map(float, ocr_rect)
        a1, b1, a2, b2 = map(float, table_rect)

        # 计算交集
        inter_x1 = max(x1, a1)
        inter_y1 = max(y1, b1)
        inter_x2 = min(x2, a2)
        inter_y2 = min(y2, b2)

        if inter_x1 >= inter_x2 or inter_y1 >= inter_y2:
            return False

        # 计算IOU
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        area_ocr = (x2 - x1) * (y2 - y1)
        area_table = (a2 - a1) * (b2 - b1)
        
        iou = inter_area / (area_ocr + area_table - inter_area + 1e-6)  # 防止除零
        
         # 新增：判断文本中心点是否在表格框内
        text_center_x = (x1 + x2) / 2
        text_center_y = (y1 + y2) / 2
        print(f"Table bbox: {table_rect}, Text bbox: {ocr_rect}")
        if (a1 <= text_center_x <= a2) and (b1 <= text_center_y <= b2):
            return True
        return iou > threshold

    def extract_table_content(self, image, tables):
        """从表格区域提取内容"""
        table_contents = []
        for table in tables:

             # 使用表格检测模型识别单元格
            table_img = image.crop(table["bbox"])
            inputs = self.processor(table_img, return_tensors="pt").to(self.device)
            with torch.no_grad():
                outputs = self.table_model(**inputs)

            bbox = table["bbox_2d"]
            x1, y1, x2, y2 = map(int, bbox)
            table_region = image.crop((x1, y1, x2, y2))
            # 使用PaddleOCR识别表格区域中的文字
            table_ocr_result = self.ocr.ocr(np.array(table_region), cls=True)
            table_text = []
            if table_ocr_result and isinstance(table_ocr_result[0], list):
                for line in table_ocr_result[0]:
                    if line and len(line) >= 2:
                        text = line[1][0]
                        table_text.append(text)
            table_contents.append({
                "bbox": bbox,
                "score": table["score"],
                "content": table_text
            })
        return table_contents

    def recognize_text(self, image):
        result = self.ocr.ocr(np.array(image), cls=True)
        text_data = []
        for line in result:
            if not line:
                continue
            for item in line:
                try:
                    # 兼容不同版本的OCR结果结构
                    if isinstance(item, list) and len(item) >= 2:
                        bbox = item[0]
                        text_info = item[1]
                        # 处理可能存在的 (text, confidence) 元组
                        text = text_info[0] if isinstance(text_info, (list, tuple)) else text_info
                        text_data.append({"text": str(text), "bbox": bbox})
                except Exception as e:
                    logging.warning(f"解析OCR结果时出错: {str(e)}，忽略该项")
        return text_data

    def process_page(self, image, page_num=0):
        logging.info(f"处理第 {page_num+1} 页...")
        print(f"当前图像尺寸: {image.size}")  # 添加尺寸打印
        


        text_data = self.recognize_text(image)
        table_data = self.detect_tables(image)
        
        for table in table_data:
            table["table_data"] = [t["text"] for t in text_data if self.is_overlap(t["bbox"], table["bbox"])]
        page_text = [t["text"] for t in text_data]
        if table_data:
            page_text.append("[TABLE_START]")
            for idx, table in enumerate(table_data):
                page_text.append(f"表格 {page_num+1}-{idx+1}:")
                page_text.extend(table["table_data"])
            page_text.append("[TABLE_END]")


        


        self.current_image_size = image.size
        # 添加可视化调试
        debug_img = np.array(image.copy())
        # 绘制表格框（绿色）
        for table in table_data:
            x1, y1, x2, y2 = map(int, table["bbox"])
            cv2.rectangle(debug_img, (x1,y1), (x2,y2), (0,255,0), 3)
        # 绘制文本框（红色）
        for text in text_data:
            if isinstance(text["bbox"][0], list):  # 处理四边形坐标
                points = np.array(text["bbox"], dtype=np.int32).reshape((-1,1,2))
                cv2.polylines(debug_img, [points], True, (0,0,255), 1)
            else:  # 矩形坐标
                x1, y1, x2, y2 = map(int, text["bbox"])
                cv2.rectangle(debug_img, (x1,y1), (x2,y2), (0,0,255), 1)
        Image.fromarray(debug_img).save(f"debug_page_{page_num}.jpg")



        return {"page": page_num + 1, "text": page_text, "tables": table_data}
        

if __name__ == "__main__":
    processor = OCRProcessor()

    # from pdf2image import convert_from_path
    # images = convert_from_path("/Users/<USER>/Documents/讯吉安/大模型/AI制单/山东迅吉安/镭刻-空运3/430220250000001784.pdf")
    # # image = Image.open("/Users/<USER>/Documents/讯吉安/大模型/AI制单/山东迅吉安/镭刻-空运3/test.png")
    # # image = cv2.imread("/Users/<USER>/Documents/讯吉安/大模型/AI制单/山东迅吉安/镭刻-空运3/test.png")
    # cc = processor.process_page(images[0])
    # print(cc)
    # # 在代码最后添加验证
    # print("表格检测结果示例：")
    # for table in cc["tables"]:
    #     print(f"表格坐标：{table['bbox']}")
    #     print(f"包含文本：{table['table_data'][:3]}")  # 打印前3个文本

    # # 可视化验证
    # debug_img = np.array(images[0].copy())
    # for table in cc["tables"]:
    #     x1, y1, x2, y2 = map(int, table["bbox"])
    #     cv2.rectangle(debug_img, (x1,y1), (x2,y2), (0,255,0), 2)
    # Image.fromarray(debug_img).save("debug_result.jpg")

    from paddleocr import PPStructure

    table_engine = PPStructure(show_log=True, layout=False)
    result = table_engine("/Users/<USER>/Documents/讯吉安/大模型/AI制单/山东迅吉安/镭刻-空运3/test.png")
    print(result)
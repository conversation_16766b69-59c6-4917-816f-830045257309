# 关区
CUSTOMS = {
"0000":"海关总署",
"0100":"北京关区",
"0101":"京机场关",
"0102":"京监管处",
"0103":"京关展览",
"0104":"京西城关",
"0105":"京会展关",
"0106":"大兴机场",
"0107":"机场库区",
"0108":"京综合处",
"0109":"机场旅检",
"0110":"平谷海关",
"0111":"京车站关",
"0112":"京邮局关",
"0113":"京中关村",
"0114":"海淀海关",
"0115":"京东城关",
"0116":"大兴旅检",
"0117":"亦庄海关",
"0118":"京朝阳关",
"0119":"通州海关",
"0121":"京稽查处",
"0123":"机场调技",
"0124":"北京站",
"0125":"西客站",
"0126":"丰台海关",
"0127":"京快件",
"0128":"顺义海关",
"0129":"天竺海关",
"0130":"亦庄物流",
"0131":"大兴综保",
"0132":"中关村综保",
"0200":"天津海关",
"0201":"津南开关",
"0202":"新港海关",
"0203":"塘沽海关",
"0204":"东港海关",
"0205":"津塘沽办",
"0206":"津邮局关",
"0207":"津机场办",
"0208":"津保空港",
"0209":"津蓟州关",
"0210":"武清海关",
"0211":"泰达综保",
"0212":"津保海港",
"0213":"东疆综保",
"0214":"津滨综保",
"0215":"津机快件",
"0216":"津开物流",
"0217":"津东疆关",
"0218":"静海海关",
"0219":"津北辰关",
"0220":"津关税处",
"0221":"津宁河关",
"0222":"津大港关",
"0223":"津临港关",
"0224":"津南疆关",
"0225":"津西青关",
"0226":"津北塘关",
"0227":"津河西关",
"0228":"津东丽关",
"0229":"津蓟物流",
"0230":"津保税关",
"0400":"石家庄区",
"0401":"鹿泉海关",
"0402":"秦皇岛关",
"0403":"唐山海关",
"0404":"廊坊海关",
"0405":"保定海关",
"0406":"邯郸海关",
"0407":"秦综保区",
"0408":"沧州海关",
"0409":"廊坊综保",
"0410":"石机场关",
"0411":"张家口关",
"0412":"曹妃甸关",
"0413":"邢台海关",
"0414":"曹综保区",
"0415":"衡水海关",
"0416":"石关快件",
"0417":"承德海关",
"0418":"石综保区",
"0419":"武安物流",
"0420":"京唐物流",
"0421":"正定海关",
"0422":"京唐港关",
"0423":"雄安海关",
"0424":"黄骅港关",
"0425":"辛集物流",
"0426":"北戴河关",
"0427":"石关邮件",
"0428":"雄安综保",
"0429":"高邑B保",
"0500":"太原关区",
"0501":"晋阳海关",
"0502":"并机场关",
"0503":"大同海关",
"0504":"临汾海关",
"0505":"方略物流",
"0506":"太原综保",
"0507":"运城海关",
"0508":"晋城海关",
"0509":"兰花物流",
"0510":"太原邮件",
"0511":"武宿海关",
"0512":"长治海关",
"0513":"阳泉海关",
"0514":"朔州海关",
"0515":"忻州海关",
"0516":"大同保B",
"0600":"满洲里关",
"0601":"海拉尔关",
"0602":"额尔古纳",
"0603":"满十八里",
"0604":"赤峰海关",
"0605":"通辽海关",
"0606":"满哈沙特",
"0607":"满室韦",
"0608":"满互贸区",
"0609":"满铁路",
"0610":"满市区",
"0611":"满机场",
"0612":"阿尔山关",
"0613":"赤峰物流",
"0614":"额布都格",
"0615":"满综保区",
"0616":"满关邮办",
"0617":"满综口岸",
"0618":"通辽 B 保",
"0700":"呼特关区",
"0701":"赛罕海关",
"0702":"二关铁路",
"0703":"包头海关",
"0704":"呼关邮办",
"0705":"二关公路",
"0706":"包头箱站",
"0707":"额济纳关",
"0708":"乌拉特关",
"0709":"满达口岸",
"0710":"东乌海关",
"0711":"机场海关",
"0712":"呼综保区",
"0713":"鄂尔多斯",
"0714":"集宁海关",
"0715":"乌海海关",
"0716":"鄂综保区",
"0717":"包头保B",
"0718":"巴市保B",
"0719":"二关公直",
"0720":"七苏保B",
"0721":"阿拉善关",
"0800":"沈阳关区",
"0801":"浑南海关",
"0802":"锦州海关",
"0803":"沈邮局关",
"0804":"抚顺海关",
"0805":"铁西海关",
"0806":"辽阳海关",
"0807":"沈机场关",
"0808":"辽中海关",
"0809":"沈快件",
"0810":"葫芦岛关",
"0811":"辽宁朝阳",
"0812":"沈抚新区",
"0813":"铁岭海关",
"0814":"铁保（B型）",
"0815":"阜新海关",
"0816":"锦港保B",
"0817":"班列沈集",
"0900":"大连海关",
"0901":"大连港湾",
"0902":"大连机场",
"0903":"连开发区",
"0904":"湾里综保",
"0905":"北良港关",
"0906":"连保税区",
"0907":"连物流园",
"0908":"连大窑湾",
"0909":"连邮局关",
"0910":"连窑综保",
"0911":"长兴岛关",
"0912":"大连快件",
"0915":"庄河海关",
"0916":"七贤岭关",
"0917":"旅顺海关",
"0919":"金石滩关",
"0930":"丹东海关",
"0931":"本溪海关",
"0932":"丹太平湾",
"0940":"营口海关",
"0941":"盘锦海关",
"0942":"盘港物流",
"0943":"营口综保",
"0950":"鲅鱼圈关",
"0951":"营港物流",
"0953":"仙人岛办",
"0960":"大东港关",
"0980":"鞍山海关",
"1500":"长春关区",
"1501":"绿园海关",
"1502":"兴隆海关",
"1503":"长白海关",
"1504":"临江口岸",
"1505":"图们海关",
"1506":"通化海关",
"1507":"珲春海关",
"1508":"吉林海关",
"1509":"延吉海关",
"1510":"长春综保",
"1511":"龙嘉机场",
"1512":"松原海关",
"1513":"白城海关",
"1514":"白山海关",
"1515":"图们车办",
"1516":"通海关村",
"1517":"珲长岭子",
"1518":"吉关车办",
"1519":"图们三合",
"1521":"一汽场站",
"1522":"长白山海关",
"1523":"四平海关",
"1524":"辽源海关",
"1525":"图们桥办",
"1526":"通集青石",
"1527":"珲春圈河",
"1528":"吉林物流",
"1529":"延吉南坪",
"1531":"兴隆铁路",
"1536":"通集铁路",
"1537":"珲沙坨子",
"1539":"图开山屯",
"1546":"通集公路",
"1547":"珲综保区",
"1549":"延古城里",
"1557":"珲春车办",
"1559":"延吉邮办",
"1567":"珲综口岸",
"1569":"延吉机办",
"1579":"延关快件",
"1589":"延吉保B",
"1591":"长邮海关",
"1593":"长白邮办",
"1595":"图们邮办",
"1596":"通集邮办",
"1900":"哈尔滨区",
"1901":"哈尔滨关",
"1902":"绥关铁路",
"1903":"黑河海关",
"1904":"同江海关",
"1905":"佳木斯海关",
"1906":"牡丹江关",
"1907":"东宁海关",
"1908":"逊克海关",
"1909":"齐齐哈尔",
"1910":"大庆海关",
"1911":"密山海关",
"1912":"虎林海关",
"1913":"同江富锦",
"1914":"抚远海关",
"1915":"漠河海关",
"1916":"萝北海关",
"1917":"嘉荫海关",
"1918":"饶河海关",
"1919":"冰城香办",
"1920":"哈开发区",
"1921":"绥综保区",
"1922":"冰城邮办",
"1923":"哈关车办",
"1924":"哈机场关",
"1925":"绥关公路",
"1926":"哈综保区",
"1927":"哈综口岸",
"1928":"绥综口岸",
"1929":"牡保B型",
"1930":"冰城海关",
"1931":"鹤岗海关",
"1932":"黑河保B",
"1933":"绥化海关",
"1934":"黑关公路",
"1935":"同关铁路",
"2200":"上海海关",
"2201":"浦江海关",
"2202":"吴淞海关",
"2203":"虹桥机场",
"2204":"闵开发区",
"2205":"车站海关",
"2206":"邮局海关",
"2207":"洋山海关",
"2208":"宝山海关",
"2209":"龙吴海关",
"2210":"浦东海关",
"2211":"卢湾监管",
"2212":"奉贤海关",
"2213":"莘庄海关",
"2214":"漕河泾发",
"2215":"西北物流",
"2216":"浦机综保",
"2217":"嘉定海关",
"2218":"外高桥关",
"2219":"沪杨浦关",
"2220":"金山海关",
"2221":"松江海关",
"2222":"青浦海关",
"2223":"沪科创关",
"2224":"崇明海关",
"2225":"外港海关",
"2226":"贸易网点",
"2227":"普陀区站",
"2228":"沪会展关",
"2229":"航交办",
"2230":"沪徐汇关",
"2231":"洋山市内",
"2232":"嘉定综保",
"2233":"浦东机场",
"2234":"沪钻交所",
"2235":"松江综A",
"2236":"洋山芦潮",
"2237":"松江综B",
"2238":"青浦综保",
"2239":"奉贤综保",
"2240":"漕河泾综",
"2241":"沪黄浦关",
"2242":"沪业二处",
"2243":"沪虹口关",
"2244":"上海快件",
"2245":"金桥综保",
"2246":"外高桥综",
"2247":"沪化工区",
"2248":"洋山港区",
"2249":"洋山特综",
"2250":"沪空港办",
"2251":"沪金桥办",
"2252":"虹桥B保",
"2300":"南京海关",
"2301":"连云港关",
"2302":"南通海关",
"2303":"苏州海关",
"2304":"无锡海关",
"2305":"张家港关",
"2306":"常州海关",
"2307":"镇江海关",
"2308":"新生圩关",
"2309":"盐城海关",
"2310":"扬州海关",
"2311":"徐州海关",
"2312":"江阴海关",
"2313":"张保税区",
"2314":"苏工业区",
"2315":"淮安海关",
"2316":"泰州海关",
"2317":"禄口机场",
"2318":"金陵江北",
"2319":"如皋海关",
"2320":"锡关机办",
"2321":"常溧阳办",
"2322":"镇丹阳办",
"2323":"金陵海关",
"2324":"常熟海关",
"2325":"昆山海关",
"2326":"吴江海关",
"2327":"太仓海关",
"2328":"苏吴中办",
"2329":"启东海关",
"2330":"泰泰兴办",
"2331":"宜兴海关",
"2332":"锡锡山办",
"2333":"南通关办",
"2334":"新沂保B",
"2335":"昆山加工",
"2336":"苏园加工",
"2337":"徐州综保",
"2338":"苏关邮办",
"2339":"南通加工",
"2340":"无锡加工",
"2341":"连关加工",
"2342":"连赣榆办",
"2343":"宁南加工",
"2344":"苏高加工",
"2345":"镇江加工",
"2346":"连关综保",
"2347":"苏园B区",
"2348":"金港海关",
"2349":"金陵邮办",
"2350":"苏高物流",
"2351":"金陵江宁",
"2352":"宁空保B",
"2353":"常关出加",
"2354":"扬关出加",
"2355":"常熟出加",
"2356":"吴江出加",
"2357":"常关武办",
"2358":"苏园保税",
"2359":"吴中出加",
"2360":"盐关港办",
"2361":"淮关出加",
"2362":"澄关物流",
"2363":"靖江物流",
"2364":"武进出加",
"2365":"张保税港",
"2366":"宿迁海关",
"2367":"泰出加区",
"2368":"苏高综保",
"2369":"昆山综保",
"2370":"连关物流",
"2371":"盐机场办",
"2372":"盐城综保",
"2373":"淮安综保",
"2374":"锡高综保",
"2375":"靖江海关",
"2376":"南通综保",
"2377":"龙潭综保",
"2378":"江宁综保",
"2379":"苏相城办",
"2380":"太仓综保",
"2381":"苏园贸易",
"2382":"苏虎丘办",
"2383":"如东海关",
"2384":"如皋物流",
"2385":"泰州综保",
"2386":"镇江综保",
"2387":"常州综保",
"2388":"武进综保",
"2389":"常熟综保",
"2390":"吴江综保",
"2391":"徐州物流",
"2392":"通海门办",
"2393":"通海安办",
"2394":"吴中综保",
"2395":"大丰物流",
"2396":"海安物流",
"2397":"江阴综保",
"2398":"扬州综保",
"2900":"杭州关区",
"2901":"钱综三处",
"2902":"钱江海关",
"2903":"温州海关",
"2904":"舟山海关",
"2905":"台州海关",
"2906":"绍兴海关",
"2907":"湖州海关",
"2908":"嘉兴海关",
"2909":"钱关下沙",
"2910":"杭州机场",
"2911":"钱关邮办",
"2912":"钱关萧办",
"2915":"丽水海关",
"2916":"杭州快件",
"2917":"衢州海关",
"2918":"钱关余办",
"2919":"钱关富办",
"2920":"金华海关",
"2921":"义乌海关",
"2922":"金关永办",
"2923":"义乌物流",
"2924":"金义综保",
"2925":"义乌综保",
"2927":"义关机办",
"2928":"杭关电商",
"2929":"钱关建办",
"2931":"温关邮办",
"2932":"温经开关",
"2933":"温关机办",
"2934":"温关鳌办",
"2935":"温关瑞办",
"2936":"温关乐办",
"2937":"温州物流",
"2938":"温州综保",
"2941":"嵊泗海关",
"2942":"舟关金塘",
"2943":"舟关综保",
"2951":"台关临办",
"2952":"台关温办",
"2953":"台关玉办",
"2954":"台州综保",
"2961":"绍关虞办",
"2962":"绍关诸办",
"2963":"绍关新办",
"2964":"绍兴综保",
"2971":"湖关安办",
"2972":"湖关德办",
"2973":"德清保B",
"2979":"湖州保B",
"2981":"嘉关乍办",
"2982":"嘉关善办",
"2983":"嘉兴综保",
"2984":"嘉关宁办",
"2985":"嘉兴桐办",
"2986":"嘉综B区",
"2991":"杭州综保",
"2992":"杭州物流",
"3100":"宁波关区",
"3101":"海曙海关",
"3102":"镇海海关",
"3103":"甬北城办",
"3104":"北仑海关",
"3105":"甬保税区",
"3106":"大榭海关",
"3107":"余姚海关",
"3108":"慈溪海关",
"3109":"甬机场关",
"3110":"象山海关",
"3111":"甬加工区",
"3112":"甬物流区",
"3113":"慈加工区",
"3114":"鄞州海关",
"3115":"栎社海关",
"3116":"梅山港区",
"3117":"梅山保税",
"3118":"宁波快件",
"3119":"甬邮局关",
"3120":"镇海物流",
"3121":"甬北港办",
"3122":"甬北穿办",
"3123":"甬江海关",
"3124":"奉化海关",
"3125":"宁海海关",
"3126":"甬新区关",
"3300":"合肥海关",
"3301":"芜湖海关",
"3302":"安庆海关",
"3303":"马鞍山关",
"3304":"黄山海关",
"3305":"蚌埠海关",
"3306":"铜陵海关",
"3307":"阜阳海关",
"3308":"池州海关",
"3309":"滁州海关",
"3310":"庐州海关",
"3311":"新桥机场",
"3312":"芜湖综保",
"3313":"经开综保",
"3315":"宣城海关",
"3316":"蚌埠物流",
"3317":"合肥综保",
"3318":"淮南海关",
"3319":"宿州海关",
"3320":"合关快件",
"3321":"安庆物流",
"3322":"宣城物流",
"3323":"空港物流",
"3324":"马综保区",
"3325":"六安海关",
"3326":"淮北海关",
"3327":"亳州海关",
"3328":"铜陵物流",
"3329":"安庆综保",
"3330":"池州物流",
"3500":"福州关区",
"3501":"马尾海关",
"3502":"榕福清办",
"3503":"宁德海关",
"3504":"三明海关",
"3505":"榕罗源办",
"3506":"莆田海关",
"3507":"长乐机场",
"3508":"榕江阴港",
"3509":"榕邮局办",
"3510":"南平海关",
"3511":"武夷山关",
"3512":"黄岐监管",
"3513":"榕快安办",
"3515":"平潭港区",
"3516":"平潭海关",
"3518":"榕城海关",
"3519":"宁德保B",
"3520":"福州综保",
"3521":"榕翔保B",
"3522":"福物流园",
"3523":"榕江阴综",
"3700":"厦门关区",
"3701":"厦门海关",
"3702":"泉刺桐办",
"3703":"漳州海关",
"3704":"东山海关",
"3705":"泉石狮办",
"3706":"龙岩海关",
"3707":"泉泉港办",
"3708":"海沧港区",
"3709":"海沧保税",
"3710":"高崎海关",
"3711":"东渡海关",
"3712":"海沧海关",
"3713":"邮局海关",
"3714":"象屿保税",
"3715":"机场海关",
"3716":"集同海关",
"3717":"象屿综保",
"3718":"泉综保区",
"3719":"厦门加工",
"3720":"厦门物流",
"3722":"大嶝监管",
"3723":"泉晋江办",
"3724":"邮轮海关",
"3725":"翔安海关",
"3726":"泉陆地港",
"3727":"古雷海关",
"3728":"泉安溪办",
"3729":"泉南安办",
"3730":"厦五通办",
"3731":"漳招银办",
"3732":"泉州海关",
"3733":"漳州保B",
"3735":"泉州保B",
"3777":"厦稽查处",
"3788":"厦侦查局",
"4000":"南昌关区",
"4001":"赣江海关",
"4002":"九江海关",
"4003":"赣州海关",
"4004":"景德镇关",
"4005":"吉安海关",
"4006":"昌北机场",
"4007":"青山湖关",
"4008":"龙南海关",
"4009":"新余海关",
"4010":"浔关区办",
"4011":"洪关区办",
"4012":"虔关区办",
"4013":"上饶海关",
"4014":"南昌物流",
"4015":"吉井加工",
"4016":"鹰潭海关",
"4017":"赣州综保",
"4018":"宜春海关",
"4019":"南昌综保",
"4020":"龙南物流",
"4021":"萍乡海关",
"4022":"抚州海关",
"4023":"九江综保",
"4024":"南昌快件",
"4025":"南昌邮件",
"4026":"吉井综保",
"4200":"青岛海关",
"4201":"烟台海关",
"4202":"日照海关",
"4203":"龙口海关",
"4204":"威海海关",
"4208":"烟综保西",
"4209":"荣成海关",
"4210":"青保税区",
"4211":"济宁海关",
"4213":"临沂海关",
"4214":"青前湾港",
"4215":"菏泽海关",
"4217":"枣庄海关",
"4218":"青开发区",
"4219":"蓬莱海关",
"4220":"青机场关",
"4221":"烟机场办",
"4222":"莱州海关",
"4223":"青岛邮关",
"4224":"烟关长办",
"4225":"威海港办",
"4227":"青岛大港",
"4228":"烟关快件",
"4230":"前湾保税",
"4231":"烟开发区",
"4232":"日岚山办",
"4236":"荣龙眼办",
"4238":"威邮局办",
"4240":"青关快件",
"4241":"烟综保东",
"4242":"威综保北",
"4243":"济曲阜办",
"4245":"烟台邮办",
"4246":"青胶综保",
"4247":"威机场办",
"4250":"青西综区",
"4253":"日照物流",
"4254":"青岛物流",
"4256":"威港快件",
"4257":"临综保区",
"4258":"前湾口岸",
"4259":"黄关快件",
"4260":"青港快件",
"4261":"烟港快件",
"4262":"威综保南",
"4263":"海阳海关",
"4264":"烟莱阳办",
"4265":"胶州海关",
"4266":"即墨海关",
"4267":"烟招远办",
"4268":"青董港关",
"4269":"石港快件",
"4270":"威文登办",
"4271":"日照综保",
"4272":"大港快件",
"4273":"即墨综保",
"4274":"烟福保B",
"4275":"烟芝罘办",
"4276":"菏港保税",
"4277":"青西保B",
"4278":"威高快件",
"4279":"临沂机场",
"4280":"青空综保",
"4288":"烟八角办",
"4300":"济南海关",
"4301":"泉城海关",
"4302":"济机场关",
"4303":"济综保区",
"4305":"济邮局关",
"4306":"章锦综保",
"4310":"潍坊海关",
"4311":"潍诸城办",
"4312":"潍综保区",
"4313":"潍寿光办",
"4315":"诸城物流",
"4316":"潍综电商",
"4320":"淄博海关",
"4321":"淄博物流",
"4322":"淄博综保",
"4330":"泰安海关",
"4341":"济机快件",
"4342":"济邮快件",
"4350":"东营海关",
"4351":"东营综保",
"4360":"聊城海关",
"4370":"德州海关",
"4380":"滨州海关",
"4381":"滨州物流",
"4390":"莱芜海关",
"4600":"郑州关区",
"4601":"金水海关",
"4602":"洛阳海关",
"4603":"南阳海关",
"4604":"郑机场关",
"4605":"郑邮局关",
"4606":"郑车站关",
"4607":"安阳海关",
"4608":"新区海关",
"4609":"商丘海关",
"4610":"周口海关",
"4611":"经开综保",
"4612":"新郑海关",
"4613":"郑州空港",
"4614":"焦作海关",
"4615":"三门峡关",
"4616":"新乡海关",
"4617":"信阳海关",
"4618":"鹤壁海关",
"4619":"德众物流",
"4620":"郑航空港",
"4621":"许昌海关",
"4622":"南阳综保",
"4623":"商丘物流",
"4624":"漯河海关",
"4625":"濮阳海关",
"4626":"民权办",
"4627":"民权保B",
"4628":"济源海关",
"4629":"驻马店海关",
"4630":"平顶山海关",
"4631":"开封海关",
"4632":"许昌保B",
"4633":"洛阳综保",
"4634":"开封综合",
"4700":"武汉海关",
"4701":"宜昌海关",
"4702":"荆州海关",
"4703":"襄阳海关",
"4704":"黄石海关",
"4705":"汉阳海关",
"4706":"宜三峡办",
"4707":"鄂加工区",
"4708":"现场一处",
"4709":"武江物流",
"4710":"武关货管",
"4711":"武关快件",
"4712":"武关机场",
"4713":"邮局海关",
"4714":"新港海关",
"4715":"汉口海关",
"4716":"十堰海关",
"4718":"武昌海关",
"4719":"东湖综保",
"4720":"黄石物流",
"4721":"仙桃海关",
"4722":"东湖陆港",
"4723":"宜昌物流",
"4724":"襄阳物流",
"4725":"空港综保",
"4726":"新港综保",
"4727":"荆门海关",
"4728":"鄂州海关",
"4729":"随州海关",
"4730":"恩施海关",
"4731":"经开综保",
"4732":"仙桃物流",
"4733":"荆门保B",
"4734":"宜昌综保",
"4735":"襄阳综保",
"4736":"黄石综保",
"4738":"鄂州保B",
"4900":"长沙关区",
"4901":"衡阳海关",
"4902":"岳阳海关",
"4903":"郴州海关",
"4904":"常德海关",
"4905":"星关霞办",
"4906":"株洲海关",
"4907":"韶山海关",
"4908":"湘机场关",
"4909":"株关醴办",
"4910":"郴州综保",
"4911":"永州海关",
"4913":"金霞物流",
"4914":"张家界关",
"4915":"衡阳综保",
"4916":"星沙海关",
"4917":"湘潭综保",
"4918":"星关浏办",
"4919":"岳阳综保",
"4920":"湘邮海关",
"4921":"黄花综保",
"4922":"株洲物流",
"4923":"邵阳海关",
"4924":"益阳海关",
"4925":"怀化海关",
"4926":"湘西海关",
"4927":"星关铁运",
"4928":"娄底海关",
"5000":"广东分署",
"5100":"广州海关",
"5101":"内港新风",
"5102":"越秀海关",
"5103":"清远海关",
"5104":"清远英德",
"5105":"天河保税",
"5106":"南沙散货",
"5107":"肇庆大旺",
"5108":"肇庆德庆",
"5109":"海珠滘心",
"5110":"南海海关",
"5111":"南海陆港",
"5112":"南海九江",
"5113":"南海北村",
"5114":"南海平洲",
"5115":"荔湾海关",
"5116":"南海业务",
"5117":"桂江车场",
"5118":"平洲旅检",
"5119":"南海三山",
"5120":"海珠海关",
"5121":"内港芳村",
"5122":"内港洲嘴",
"5123":"内港四仓",
"5124":"双东车场",
"5125":"从化海关",
"5126":"内港赤航",
"5130":"广州萝岗",
"5131":"花都海关",
"5132":"花都码头",
"5133":"穗知识城",
"5134":"穗保税处",
"5135":"天河海关",
"5136":"穗统计处",
"5137":"穗价格处",
"5138":"高明食出",
"5139":"穗监管处",
"5140":"穗关税处",
"5141":"广州机场",
"5142":"民航快件",
"5143":"广州车站",
"5144":"穗机综保",
"5145":"广州邮办",
"5146":"穗关会展",
"5147":"穗邮办监",
"5148":"穗大郎站",
"5149":"大铲海关",
"5150":"顺德海关",
"5151":"顺德保税",
"5152":"顺德食出",
"5153":"顺德车场",
"5154":"北窖车场",
"5155":"顺德旅检",
"5157":"陈村车场",
"5158":"顺德勒流",
"5160":"番禺海关",
"5161":"沙湾车场",
"5162":"番禺旅检",
"5163":"番禺货柜",
"5164":"番禺船舶",
"5165":"南沙保税",
"5166":"南沙新港",
"5167":"南沙货港",
"5168":"南沙汽车",
"5169":"南沙海关",
"5170":"肇庆海关",
"5171":"肇庆高要",
"5172":"肇庆车场",
"5173":"肇庆新港",
"5174":"肇庆旅检",
"5175":"肇庆码头",
"5176":"肇庆四会",
"5177":"肇庆三榕",
"5178":"云浮海关",
"5179":"罗定海关",
"5180":"佛关禅办",
"5181":"高明海关",
"5182":"佛山澜石",
"5183":"三水码头",
"5184":"佛山窖口",
"5185":"佛山快件",
"5186":"佛山保税",
"5187":"佛山车场",
"5188":"佛山海关",
"5189":"佛山新港",
"5190":"韶关海关",
"5191":"韶关乐昌",
"5192":"三水海关",
"5193":"三水车场",
"5194":"三水港",
"5195":"审单中心",
"5196":"云浮新港",
"5197":"白云电商",
"5198":"穗河源关",
"5199":"穗技术处",
"5200":"黄埔关区",
"5201":"埔老港关",
"5202":"埔新港关",
"5203":"增城海关",
"5204":"东莞海关",
"5205":"太平海关",
"5206":"惠州海关（作废）",
"5207":"凤岗海关",
"5208":"穗东海关",
"5209":"埔东海关",
"5210":"埔红海办",
"5211":"河源海关",
"5212":"新沙海关",
"5213":"埔长安关",
"5214":"常平海关",
"5216":"沙田海关",
"5217":"寮步车场",
"5218":"江龙车场",
"5219":"黄埔综保",
"5220":"东莞物流",
"5221":"埔荔城办",
"5222":"清溪物流",
"5223":"东莞邮办",
"5224":"萝岗海关",
"5225":"虎门综保",
"5300":"深圳海关",
"5301":"皇岗海关",
"5302":"罗湖海关",
"5303":"沙头角关",
"5304":"蛇口海关",
"5305":"福强海关",
"5306":"笋岗海关",
"5307":"南头海关",
"5308":"龙岗海关",
"5309":"布吉海关",
"5310":"淡水办",
"5311":"深关车站",
"5312":"深监管处",
"5313":"深调查局",
"5314":"深关邮局",
"5315":"惠东海关",
"5316":"大鹏海关",
"5317":"深机场关",
"5318":"梅林海关",
"5319":"同乐海关",
"5320":"文锦渡关",
"5321":"福田海关",
"5322":"沙保税关",
"5323":"深审单处",
"5324":"深审价办",
"5325":"深关税处",
"5326":"深数统处",
"5327":"深法规处",
"5328":"深规范处",
"5329":"深保税处",
"5330":"盐保税关",
"5331":"三门岛办",
"5332":"深财务处",
"5333":"深侦查局",
"5334":"深稽查处",
"5335":"深技术处",
"5336":"深办公室",
"5337":"大亚湾核",
"5338":"惠州港关",
"5339":"坪山海关",
"5340":"沙湾海关",
"5341":"深惠州关",
"5342":"深红海办",
"5343":"深盐物流",
"5344":"惠石化办",
"5345":"深圳湾关",
"5346":"深机快件",
"5348":"深关大铲",
"5349":"前海综保",
"5350":"大运通关",
"5351":"前海保税",
"5352":"梅沙海关",
"5353":"西九龙关",
"5354":"莲塘海关",
"5355":"福中海关",
"5356":"前海海关",
"5357":"观澜海关",
"5358":"西沥海关",
"5700":"拱北关区",
"5701":"拱稽查处",
"5710":"闸口海关",
"5720":"中山海关",
"5721":"中山港",
"5724":"中石岐办",
"5725":"坦洲货场",
"5726":"中山物流",
"5727":"小榄港",
"5728":"神湾港",
"5729":"中山快件",
"5730":"香洲海关",
"5740":"湾仔海关",
"5741":"湾仔船舶",
"5750":"九洲海关",
"5760":"拱白石办",
"5770":"斗门海关",
"5771":"斗井岸办",
"5772":"斗平沙办",
"5780":"高栏海关",
"5781":"高综保区",
"5782":"宏达码头",
"5788":"大桥海关",
"5790":"青茂海关",
"5791":"拱跨工区",
"5792":"拱保税区",
"5793":"万山海关",
"5794":"桂山中途",
"5795":"横琴海关",
"5796":"澳大校区",
"5798":"拱关邮检",
"5799":"珠海快件",
"6000":"汕头海关",
"6001":"汕关货一",
"6002":"汕关货二",
"6003":"汕关行邮",
"6004":"潮汕机场",
"6006":"汕关保税",
"6007":"汕关业务",
"6008":"濠江海关",
"6009":"汕关邮包",
"6010":"汕保物流",
"6011":"揭阳海关",
"6012":"普宁海关",
"6013":"澄海海关",
"6014":"广澳海关",
"6015":"南澳海关",
"6016":"濠江现场",
"6018":"揭关惠来",
"6019":"龙湖海关",
"6020":"汕港海关",
"6021":"潮州海关",
"6022":"饶平海关",
"6023":"饶平快件",
"6026":"汕头综保",
"6028":"潮阳海关",
"6031":"汕尾海关",
"6032":"海城海关",
"6033":"尾关陆丰",
"6038":"汕关快件",
"6041":"梅州海关",
"6042":"梅州兴宁",
"6046":"梅州综保",
"6400":"海口关区",
"6401":"海口港",
"6402":"三亚海关",
"6403":"八所海关",
"6404":"洋浦海关",
"6405":"海保税区",
"6406":"文昌海关",
"6407":"美兰机场",
"6408":"洋浦保税",
"6409":"海口综保",
"6410":"马村港",
"6411":"椰城海关",
"6412":"三沙海关",
"6413":"博鳌机场",
"6414":"海空综保",
"6415":"三亚B保",
"6416":"三亚机场",
"6700":"湛江关区",
"6701":"湛江海关",
"6702":"茂名海关",
"6703":"徐闻海关",
"6704":"海东海关",
"6705":"茂名水东",
"6706":"湛江吴川",
"6707":"廉江海关",
"6708":"茂名高州",
"6709":"茂名信宜",
"6710":"东海岛关",
"6711":"霞山海关",
"6712":"霞海海关",
"6713":"湛江机场",
"6714":"湛江博贺",
"6715":"湛江快件",
"6716":"湛江物流",
"6717":"湛江调顺",
"6718":"湛江遂溪",
"6719":"湛江综保",
"6800":"江门关区",
"6810":"江门海关",
"6811":"高沙海关",
"6812":"外海海关",
"6813":"江门旅检",
"6815":"外海电商",
"6816":"江门车场",
"6817":"江门保税",
"6820":"新会海关",
"6821":"新会港",
"6827":"新会稽查",
"6830":"台山海关",
"6831":"台公益港",
"6837":"台山稽查",
"6840":"开平海关",
"6841":"开平码头",
"6847":"开平电商",
"6850":"恩平海关",
"6851":"恩平港",
"6857":"恩平稽查",
"6860":"鹤山海关",
"6861":"鹤山码头",
"6866":"南方电商",
"6867":"鹤松电商",
"6870":"阳江海关",
"6871":"阳江港",
"6872":"阳江车场",
"6877":"阳江稽查",
"7200":"南宁关区",
"7201":"邕州海关",
"7202":"北海海关",
"7203":"梧州海关",
"7204":"桂林海关",
"7205":"柳州海关",
"7206":"防城海关",
"7207":"东兴海关",
"7208":"凭祥海关",
"7209":"贵港海关",
"7210":"水口海关",
"7211":"龙邦海关",
"7212":"钦州海关",
"7213":"桂林机办",
"7214":"北海综保",
"7215":"钦州综保",
"7216":"南宁综保",
"7217":"钦州港关",
"7218":"玉林海关",
"7219":"南凭综保",
"7220":"友谊关",
"7221":"邕机场关",
"7222":"邕邮局关",
"7223":"贺州海关",
"7224":"河池海关",
"7225":"爱店海关",
"7226":"峒中海关",
"7227":"防港保B",
"7228":"硕龙海关",
"7229":"平孟海关",
"7230":"柳州保B",
"7231":"梧州综保",
"7900":"成都关区",
"7901":"蓉青关",
"7902":"蓉机双流",
"7903":"蓉乐关",
"7904":"蓉攀关",
"7905":"蓉绵关",
"7906":"蓉邮关",
"7907":"蓉盐关",
"7908":"蓉内关",
"7909":"公路场站",
"7910":"蓉机快件",
"7911":"蓉泸关",
"7912":"蓉戎关",
"7913":"蓉南关",
"7914":"蓉绵加工",
"7915":"蓉锦关",
"7916":"蓉锦西区",
"7917":"蓉遂关",
"7918":"蓉旌关",
"7919":"蓉通关",
"7920":"蓉机保B",
"7921":"蓉泸保B",
"7922":"蓉锦双流",
"7923":"蓉戎保B",
"7924":"蓉青保B",
"7925":"蓉新关",
"7926":"蓉利关",
"7927":"蓉新物流",
"7928":"蓉青综保",
"7929":"蓉泸综保",
"7930":"蓉戎综保",
"7931":"蓉锦综保",
"7932":"蓉绵综保",
"7933":"蓉南保B",
"7934":"蓉賨关",
"7935":"蓉机天府",
"7936":"天府快件",
"7937":"蓉内保B",
"8000":"重庆关区",
"8001":"重庆海关",
"8002":"两江海关",
"8003":"重庆机场",
"8004":"重庆邮局",
"8005":"万州海关",
"8006":"渝州海关",
"8007":"重庆水港",
"8008":"两寸海关",
"8009":"涪陵海关",
"8010":"寸滩水港",
"8011":"渝关快件",
"8012":"两果两路",
"8013":"西永综保",
"8014":"西永海关",
"8015":"渝贸园区",
"8016":"渝铁物流",
"8017":"渝公物流",
"8018":"黔江海关",
"8019":"江津综保",
"8020":"永川海关",
"8021":"涪陵综保",
"8022":"两果果园",
"8023":"万州综保",
"8024":"永川综保",
"8300":"贵阳海关",
"8301":"筑城海关",
"8302":"黔机场关",
"8303":"遵义海关",
"8304":"贵阳综保",
"8305":"贵阳现场",
"8306":"贵安综保",
"8307":"贵安海关",
"8308":"六盘水关",
"8309":"遵义综保",
"8310":"凯里海关",
"8311":"毕节海关",
"8312":"兴义海关",
"8313":"铜仁海关",
"8600":"昆明关区",
"8601":"滇中海关",
"8602":"畹町海关",
"8603":"瑞丽海关",
"8604":"章凤海关",
"8605":"盈江海关",
"8606":"孟连海关",
"8607":"南伞海关",
"8608":"孟定海关",
"8609":"打洛海关",
"8610":"腾冲海关",
"8611":"沧源海关",
"8612":"勐腊海关",
"8613":"河口海关",
"8614":"金水河关",
"8615":"天保海关",
"8616":"田蓬海关",
"8617":"大理海关",
"8618":"芒市海关",
"8619":"腾驻隆阳",
"8620":"昆明机场",
"8621":"邮局海关",
"8622":"西双版纳",
"8623":"丽江海关",
"8624":"思茅海关",
"8625":"河口山腰",
"8626":"怒江海关",
"8627":"昆明高新",
"8628":"昆明加工",
"8629":"香格里拉",
"8631":"勐康海关",
"8632":"昆明快件",
"8633":"红河综保",
"8634":"昆明综保",
"8635":"昆综口岸",
"8636":"高新物流",
"8637":"红综口岸",
"8638":"磨憨快件",
"8639":"机场电商",
"8640":"腾俊物流",
"8641":"都龙海关",
"8642":"瑞驻姐告",
"8643":"瑞驻弄岛",
"8644":"孟驻临翔",
"8645":"西驻关累",
"8646":"蒙自海关",
"8647":"曲靖海关",
"8648":"玉溪海关",
"8649":"磨憨铁路",
"8800":"拉萨海关",
"8801":"聂拉木关",
"8802":"日喀则关",
"8803":"狮泉河关",
"8804":"贡嘎机场",
"8805":"八廓海关",
"8806":"普兰海关",
"8807":"亚东海关",
"8808":"吉隆海关",
"8809":"林芝海关",
"8810":"拉萨综保",
"8811":"里孜口岸",
"9000":"西安关区",
"9001":"关中海关",
"9002":"咸阳机场",
"9003":"宝鸡海关",
"9004":"邮局海关",
"9005":"关中综A",
"9006":"关中综B",
"9007":"西安综保",
"9008":"高新综保",
"9009":"车站海关",
"9010":"延安海关",
"9011":"渭南海关",
"9012":"榆林海关",
"9013":"西咸综保",
"9014":"汉中海关",
"9015":"商洛海关",
"9016":"航空综保",
"9017":"宝鸡综保",
"9018":"杨凌综保",
"9400":"乌关区",
"9401":"乌昌公路",
"9402":"霍尔果斯",
"9403":"吐尔尕特",
"9404":"阿拉山口",
"9405":"塔城海关",
"9406":"伊宁海关",
"9407":"吉木乃关",
"9408":"喀什海关",
"9409":"红其拉甫",
"9410":"阿勒泰关",
"9411":"塔克什肯",
"9412":"乌拉斯太",
"9413":"哈密关",
"9414":"红山嘴",
"9415":"伊尔克什",
"9416":"库尔勒关",
"9417":"乌机场关",
"9418":"乌加工区",
"9419":"都拉塔关",
"9420":"乌昌关",
"9421":"霍中心A",
"9422":"石河子关",
"9423":"山口综保",
"9424":"喀什综保",
"9425":"卡拉苏关",
"9426":"奎屯物流",
"9427":"中哈合作中心配套区",
"9428":"乌邮局关",
"9429":"乌综保区",
"9430":"阿克苏关",
"9431":"和田海关",
"9500":"兰州关区",
"9501":"金城海关",
"9502":"酒泉海关",
"9503":"中川机场",
"9504":"武威物流",
"9505":"天水海关",
"9506":"金昌海关",
"9507":"兰州综保",
"9508":"敦煌机场",
"9509":"平凉海关",
"9600":"银川海关",
"9601":"兴庆海关",
"9602":"河东机场",
"9603":"石嘴山关",
"9604":"银川综保",
"9605":"银关快件",
"9606":"石嘴山物流",
"9607":"中卫海关",
"9700":"西宁关区",
"9701":"西海海关",
"9702":"青海物流",
"9703":"西宁机场",
"9704":"格尔木海关",
"9705":"西宁综保",
"9800":"香港海关",
"9900":"政法司",
"A100":"澳门海关",
}
def customs_handler(input_customs):

    for code,customs in CUSTOMS.items():
        if input_customs == customs or input_customs == code:
            return code,customs
    return None,input_customs

# print(customs_handler("北京关区"))
# print(len(CUSTOMS))
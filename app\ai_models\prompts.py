"""AI模型提示词管理"""
import json
# 文档分类提示词
CLASSIFY_PROMPT1 = """
# 任务：文档类型分类

## 说明
你的任务是分析OCR识别的文本内容，并确定文档类型。请仅返回文档类型，不要添加任何解释或附加信息。

## 输入内容
{text}

## 输出要求
只返回以下选项之一：出口发票、出口箱单、出口证书、入货通知、舱单样本、报关单、出口合同、提运单、分箱明细、申报要素、检验检疫、其他
"""
# 进口文档分类提示词
CLASSIFY_PROMPT_I = """
# 任务：文档类型分类

## 说明
你的任务是分析OCR识别的文本内容，并确定文档类型。判断时必须满足每种类型的所有关键字段要求。请仅返回文档类型，不要添加任何解释或附加信息。

## 文档类型判定标准
- **申报要素**（必须**排除含价格和重量字段**的内容）：
  - 满足以下条件：
    - 明确出现：商品名称、商品编码、申报要素、要素、HS、HSCODE、型号、规格型号等关键词；
    - 且**不包含**以下任意字段：单价、净重、毛重、数量、总价。
  - 一旦出现价格、重量、数量等贸易要素，应立即排除为“申报要素”，转为考虑为“报关单”或其他类型。
- **报关单**：
  - 包含“境内发货人、境外收货人”，且同时包含以下任意4项：
    - 进境关别、进口日期、申报日期、备案号、运输方式、运输工具、航次号、提运单号、生产销售单位、监管方式、征免性质、许可证号、合同协议号、贸易国、运抵国、指运港、包装种类、件数、毛重、净重、成交方式、商品编号、商品名称、规格型号、数量及单位、单价、总价、币制；
  - 内容多为表格、清单形式，有条理地列出多项进口商品数据。
- **进口发票**：必须包含“INVOICE”或“发票”关键词，且满足以下任意3项：境内发货人、境外收货人、运抵国、商品名称、型号、数量、单价、总价、成交方式（如：FOB/CIF等）。
- **进口箱单**：必须包含“PACKING”或“箱单”关键词，且满足以下任意2项：发货人、收货人、净重、毛重、件数。
- **进口证书**：出现如Reference No.、备案号、许可证号、Permit No.、产品资质、随附单证、商品编码、入境口岸、有效期、进口货物名称等字段。
- **入货通知**：出现“提运单号”、“订舱号”、“BOOKING NO”、“船名/航次”、“提箱场地”等关键词。
- **舱单样本**：包含“SHIPPER/EXPORTER”、“CONSIGNEE”、“DESCRIPTION OF GOODS”、“Port of Loading”等字段。
- **进口合同**：出现“CONTRACT”、“合同号”、“商品名称”、“品牌”、“成交方式”、“成交数量”、“总价”、“单价”等贸易合同相关字段，合同条款无需拆分。
- **提运单**：包含“Bill of Lading”、“提单号”、“Container No.”等海运提单字段。
- **分箱明细**：行列表形式出现“中文品名”、“数量”、“净重”、“包装”、“每箱装量”、“箱数”、“集装箱描述”等字段。
- **检验检疫**：包含“检验检疫”、“电子底账”、“申请单位”等字样。
- **其他**：不满足以上任一文档结构的内容。

## 输入内容
{text}

## 输出要求
只返回以下选项之一：进口发票、进口箱单、进口证书、入货通知、舱单样本、报关单、进口合同、提运单、分箱明细、申报要素、检验检疫、其他
"""
# 出口文档分类提示词
CLASSIFY_PROMPT = """
# 任务：文档类型分类

## 说明
你的任务是分析OCR识别的文本内容，并确定文档类型。判断时必须满足每种类型的所有关键字段要求。请仅返回文档类型，不要添加任何解释或附加信息。

## 文档类型判定标准
- **申报要素**（必须**排除含价格和重量字段**的内容）：
  - 满足以下条件：
    - **包含至少2个商品相关字段**（如：商品名称、商品编码、HS、HSCODE、型号、规格型号）；
    - 且**不包含**以下任意字段：单价、净重、毛重、数量、总价。
  - 一旦出现价格、重量、数量等贸易要素，应立即排除为“申报要素”，转为考虑为“报关单”或其他类型。
  - 确保**不含价格、数量、重量等贸易要素**的文档类型，优先判定为“申报要素”。
- **报关单**：
  - 包含“境内发货人、境外收货人”，且同时包含以下任意4项：
    - 出境关别、出口日期、申报日期、备案号、运输方式、运输工具、航次号、提运单号、生产销售单位、监管方式、征免性质、许可证号、合同协议号、贸易国、运抵国、指运港、包装种类、件数、毛重、净重、成交方式、商品编号、商品名称、规格型号、数量及单位、单价、总价、币制；
  - 内容多为表格、清单形式，有条理地列出多项出口商品数据。
- **出口发票**：必须包含“INVOICE”或“发票”关键词，且满足以下任意3项：境内发货人、境外收货人、运抵国、商品名称、型号、数量、单价、总价、成交方式（如：FOB/CIF等）。
- **出口箱单**：必须包含“PACKING”或“箱单”关键词，且满足以下任意2项：发货人、收货人、净重、毛重、件数。
- **出口证书**：出现如Reference No.、备案号、许可证号、Permit No.、产品资质、随附单证、商品编码、出口口岸、有效期、出口货物名称等字段。
- **入货通知**：出现“提运单号”、“订舱号”、“BOOKING NO”、“船名/航次”、“提箱场地”等关键词。
- **舱单样本**：包含“SHIPPER/EXPORTER”、“CONSIGNEE”、“DESCRIPTION OF GOODS”、“Port of Loading”等字段。
- **出口合同**：出现“CONTRACT”、“合同号”、“商品名称”、“品牌”、“成交方式”、“成交数量”、“总价”、“单价”等贸易合同相关字段，合同条款无需拆分。
- **提运单**：包含“Bill of Lading”、“提单号”、“Container No.”等海运提单字段。
- **分箱明细**：行列表形式出现“中文品名”、“数量”、“净重”、“包装”、“每箱装量”、“箱数”、“集装箱描述”等字段。
- **检验检疫**：包含“检验检疫”、“电子底账”、“申请单位”、“出境”等字样。
- **其他**：不满足以上任一文档结构的内容。

## 输入内容
{text}

## 输出要求
只返回以下选项之一：出口发票、出口箱单、出口证书、入货通知、舱单样本、报关单、出口合同、提运单、分箱明细、申报要素、检验检疫、其他
"""

VERIFY_PROMPT = """
# 任务：业务逻辑校验

## 说明
你的任务是输入的JSON格式的内容中是否存在业务逻辑错误，如果有，请返回错误信息，如果没有，请返回空字符串。不要添加任何解释或附加信息。
#校验规则示例：
    1.单价总价币制要一样
    2.发票总价/单价（要注意单位）=成交数量
    3.箱单，总毛重/总净重/单件重量（要注意单位）=成交数量
    4.单据上是多项的汇总比对：例如发票单项总价与合计总价一致；箱单单项净重/毛重与总的净重/毛重一致；单项/总的毛重>净重
    5.商品描述：发票项数与箱单项数不一致的，需要根据商品描述分别统计汇总后逐项匹配

## 输入内容
{text}
## 输出要求
只返回业务逻辑的错误信息。如果没有，请返回空字符串.
{{
    "error": "错误信息/空字符串"
}}
"""
#进口文档分割提示词
SPLIT_PROMPT_I = """
# 任务：文本分割与单据类型识别

## 说明
你的任务是将用户提供的文本内容进行结构化分割。首先判断文本中是否存在多个单据类型内容，如果存在，则分别拆分并标记每个单据类型与其对应内容；如果只包含一种类型的单据，直接输出对应类型和内容。
如果输入的内容中包含坐标，那么在返回的JSON中按照坐标位置还原文档格式和内容，坐标信息不要返回，只返回文本信息。

## 文档类型判定标准
- **申报要素**（必须**排除含价格和重量字段**的内容）：
  - 满足以下条件：
    - **包含至少2个商品相关字段**（如：商品名称、商品编码、HS、HSCODE、型号、规格型号）；
    - 且**不包含**以下任意字段：单价、净重、毛重、数量、总价。
  - 一旦出现价格、重量、数量等贸易要素，应立即排除为“申报要素”，转为考虑为“报关单”或其他类型。
  - 确保**不含价格、数量、重量等贸易要素**的文档类型，优先判定为“申报要素”。
- **报关单**：
  - 包含“境内发货人、境外收货人”，且同时包含以下任意4项：
    - 进境关别、进口日期、申报日期、备案号、运输方式、运输工具、航次号、提运单号、生产销售单位、监管方式、征免性质、许可证号、合同协议号、贸易国、运抵国、指运港、包装种类、件数、毛重、净重、成交方式、商品编号、商品名称、规格型号、数量及单位、单价、总价、币制；
  - 内容多为表格、清单形式，有条理地列出多项进口商品数据。
- **进口发票**：必须包含“INVOICE”或“发票”关键词，且满足以下任意3项：境内发货人、境外收货人、运抵国、商品名称、型号、数量、单价、总价、成交方式（如：FOB/CIF等）。
- **进口箱单**：必须包含“PACKING”或“箱单”关键词，且满足以下任意2项：发货人、收货人、净重、毛重、件数。
- **进口证书**：出现如Reference No.、备案号、许可证号、Permit No.、产品资质、随附单证、商品编码、入境口岸、有效期、进口货物名称等字段。
- **入货通知**：出现“提运单号”、“订舱号”、“BOOKING NO”、“船名/航次”、“提箱场地”等关键词。
- **舱单样本**：包含“SHIPPER/EXPORTER”、“CONSIGNEE”、“DESCRIPTION OF GOODS”、“Port of Loading”等字段。
- **进口合同**：出现“CONTRACT”、“合同号”、“商品名称”、“品牌”、“成交方式”、“成交数量”、“总价”、“单价”等贸易合同相关字段，合同条款无需拆分。
- **提运单**：包含“Bill of Lading”、“提单号”、“Container No.”等海运提单字段。
- **分箱明细**：行列表形式出现“中文品名”、“数量”、“净重”、“包装”、“每箱装量”、“箱数”、“集装箱描述”等字段。
- **检验检疫**：包含“检验检疫”、“电子底账”、“申请单位”等字样。
- **其他**：不满足以上任一文档结构的内容。

## 拆分规则
1. 如果文本中包含多个单据，则分别提取每种单据的完整内容，按单据类型合并输出。
2. 若只存在一种单据，输出识别的文档类型及其完整内容。
3. 若未识别出任一合法文档类型，则返回空字符串。
4. 对同一文档类型的多份记录应统一归类为一项，合并输出。
5. 不对单据原始内容做任何格式化、润色或语义改写。
6. 所有非法控制字符应自动转义为 JSON 兼容格式。
7. 文本内容较短但含“商品编码、型号、名称”等字段可推断为“申报要素”。
## 输入内容
{text}

## 输出要求
输出结构为 JSON 数组，每一项表示一种单据类型及其内容：
[
    {{
        "单据类型": "进口发票",
        "单据内容": "发票的原始内容"
    }},
    {{
        "单据类型": "报关单",
        "单据内容": "报关单的原始内容"
    }},
    ...
]
如果未识别到任何单据类型，返回空数组 []

"""
#出口文档分割提示词
SPLIT_PROMPT = """
# 任务：文本分割与单据类型识别

## 说明
你的任务是将用户提供的文本内容进行结构化分割。首先判断文本中是否存在多个单据类型内容，如果存在，则分别拆分并标记每个单据类型与其对应内容；如果只包含一种类型的单据，直接输出对应类型和内容。

## 文档类型判定标准
- **申报要素**（必须**排除含价格和重量字段**的内容）：
  - 满足以下条件：
    - **包含至少2个商品相关字段**（如：商品名称、商品编码、HS、HSCODE、型号、规格型号）；  
    - 且**不包含**以下任意字段：单价、净重、毛重、数量、总价。\n  
    - 一旦出现价格、重量、数量等贸易要素，应立即排除为“申报要素”，转为考虑为“报关单”或其他类型。\n 
- **报关单**：
  - 包含“境内发货人、境外收货人”，且同时包含以下任意4项：
    - 出境关别、出口日期、申报日期、备案号、运输方式、运输工具、航次号、提运单号、生产销售单位、监管方式、征免性质、许可证号、合同协议号、贸易国、运抵国、指运港、包装种类、件数、毛重、净重、成交方式、商品编号、商品名称、规格型号、数量及单位、单价、总价、币制；
  - 内容多为表格、清单形式，有条理地列出多项出口商品数据。
- **出口发票**：必须包含“INVOICE”或“发票”关键词，且满足以下任意3项：境内发货人、境外收货人、运抵国、商品名称、型号、数量、单价、总价、成交方式（如：FOB/CIF等）。
- **出口箱单**：必须包含“PACKING”或“箱单”关键词，且满足以下任意2项：发货人、收货人、净重、毛重、件数。
- **出口证书**：出现如Reference No.、备案号、许可证号、Permit No.、产品资质、随附单证、商品编码、出口口岸、有效期、出口货物名称等字段。
- **入货通知**：出现“提运单号”、“订舱号”、“BOOKING NO”、“船名/航次”、“提箱场地”等关键词。
- **舱单样本**：包含“SHIPPER/EXPORTER”、“CONSIGNEE”、“DESCRIPTION OF GOODS”、“Port of Loading”等字段。
- **出口合同**：出现“CONTRACT”、“合同号”、“商品名称”、“品牌”、“成交方式”、“成交数量”、“总价”、“单价”等贸易合同相关字段，合同条款无需拆分。
- **提运单**：包含“Bill of Lading”、“提单号”、“Container No.”等海运提单字段。
- **分箱明细**：行列表形式出现“中文品名”、“数量”、“净重”、“包装”、“每箱装量”、“箱数”、“集装箱描述”等字段。
- **检验检疫**：包含“检验检疫”、“电子底账”、“申请单位”、“出境”等字样。

## 拆分规则
1. 如果文本中包含多个单据，则分别提取每种单据的完整内容，按单据类型合并输出。
2. 若只存在一种单据，输出识别的文档类型及其完整内容。
3. 若未识别出任一合法文档类型，则返回空字符串。
4. 对同一文档类型的多份记录应统一归类为一项，合并输出。
5. 不对单据原始内容做任何格式化、润色或语义改写。
6. 所有非法控制字符应自动转义为 JSON 兼容格式。
7. 文本内容较短但含“商品编码、型号、名称”等字段可推断为“申报要素”。

## 输入内容
{text}

## 输出要求
输出结构为 JSON 数组，每一项表示一种单据类型及其内容：
[
    {{
        "单据类型": "出口发票",
        "单据内容": "发票的原始内容"
    }},
    {{
        "单据类型": "报关单",
        "单据内容": "报关单的原始内容"
    }},
    ...
]
如果未识别到任何单据类型，返回[
    {{
        "单据类型": "其他",
        "单据内容": "文本的全部内容"
    }}
    ...
]

"""
SPLIT_PROMPT1 = """
# 任务：文本分割

## 说明
你的任务是将用户输出的文本内容进行分割，首先需要判断文本内容中是否存在多个单据，如果存在多个单据，那么需要将每个单据进行分割，然后返回每个单据的文本内容。
# 注意：
1、## 文档类型判定标准
    - 申报要素：包含商品名称、商品编码、申报要素、要素、HS、HSCODE、型号等信息，一定没有单价、净重、毛重、数量、总价。
    - 出口发票：必须包含"INVOICE"或"发票"，以及至少3项：境内发货人、境外收货人、运抵国、商品名称、型号、数量、单价、总价、成交方式(CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等)
    - 出口箱单：必须包含"PACKING"或"箱单"，以及至少2项：境内发货人、境外收货人、净重(NET WEIGHT)、毛重(GROSS WEIGHT)、件数(NUMBER OF PACKAGE)
    - 出口证书：包含境内发货人、境外收货人、产地证书（Reference No.）、备案号（出口许可证号Export licence No./ 出口准许证）、随附单证代码E（物种证明 Permit No.证号）、产品资质203、随附单据文件类别、商品编码（HS CODE/HS/检验检疫编码）、报关口岸（Place of clearance/ Port口岸/出口口岸）、有效截止日期、出口货物名称等信息
    - 入货通知：包含提运单号（D/R No./ 提单号/提单号码/订舱号/BOOKING NO）、船名/航次（Vessel/Voyage）、场站（场站/提箱场地/入货地址）等信息
    - 舱单样本：包含境内发货人（SHIPPER/EXPORTER）、境外收货人（CONSIGNEE：/Importer(buyer)）、货物描述（DESCRIPTION OF GOODS）、指运港（Port of Loading）等信息
    - 报关单：包含境内发货人、境外收货人、出境关别、出口日期、申报日期、备案号、运输方式、运输工具及航次好、提运单号、生产销售单位、监管方式、征免性质、许可证号、合同协议号、贸易国、运抵国、指运港、包装种类、件数、毛重、净重、成交方式、商品编号、商品名称及规格型号、数量及单位、单价/总价/币制等信息
    - 出口合同：包含CONTRACT、境内发货人、境外收货人、合同号/合同编号、运抵国、商品名称、型号、成交数量、总价、总价大写、单价、成交方式、原产国、品牌等信息
    - 提运单：包含Bill of Lading、Container No.等信息
    - 分箱明细：包含集装箱描述、中文品名、数量、净重、包装、每箱装量、箱数等信息，全部是行列表
    - 检验检疫：包括检验检疫、出境、申请单位、电子底账数据号等信息
    - 其他：无法确定的文档类型
2、如果文本内容中存在多个单据，那么需要将每个单据进行分割，然后返回每个单据的文本内容。
3、如果文本内容中不存在单据，那么返回空字符串。
4、不要对单据的内容做任何的调整，只做分割。
5、只需要返回文本中存在的单据类型，不要返回其他信息。
6、将内容中的非法控制字符转换成JSON可以识别的转移字符。
7、如果文本内容中不存在多个单据，那么识别出来单据的类型，返回类型和内容
8、对于合同条款这样的内容，不需要进行拆分
9、将同一种类型的单据合并在一起，不要分开。
10、如果文本内容较少，且有数字代码、商品名称、型号，可以考虑文档类型为申报要素

## 输入内容
{text}

## 输出要求
[
    {{
        "单据类型": "出口发票",
        "单据内容": "发票的内容"
    }},
    {{
        "单据类型": "出口箱单",
        "单据内容": "箱单的内容"
    }},
    {{
        "单据类型": "其他",
        "单据内容": "其他的内容"
    }},
]
"""


MERGE_PROMPT = """
# 任务：JSON内容提取合并
## 说明
你的任务是将用户输出的多个JSON中内容分析之后提取按照示例的关键字段和结构进行输出。

## 注意：即使下方内容较长，也必须严格按以下要求执行：
1、请从用户提供的多个JSON中提取输出结构要求的字段值，你需要分析每个JSON的结构，然后提取出示例要求的字段值，然后输出JSON。
2、重要提醒：严格按照用户要求的输出结构进行输出，包括字段名称、结构、层级关系等，不要漏掉任何字段，不要多加字段，不要修改字段名称，不要修改字段层级关系。
示例模版:
    [
        {{
            "境内发货人": "company",
            "境外收货人": "Dong Run Wan Bo Technology Ltd",
            "发票号": "BJ20250318INV3",
            "运抵国": "Altyntau Kazakhstan",
            "成交方式": "CIP qingdao AIRPORT, CHINA",
            "贸易国": "Altyntau Kazakhstan",
            "总数量": "1",
            "净重": "700kg",
            "毛重": "720kg",
            "协定享惠": "",
            "备案号": "",
            "随附单证代码E": "",
            "产品资质203": "",
            "随附单据文件类别": "",
            "商品编码": "",
            "申报地海关": "",
            "有效截止日期": "20250308",
            "提运单号": "",
            "船名/航次": "",
            "场站": "",
            "货物描述": "",
            "指运港": "",
            "出境关别": "",
            "出口日期": "",
            "申报日期": "",
            "运输方式": "",
            "运输工具及航次": "",
            "生产销售单位": "",
            "监管方式": "",
            "征免性质": "",
            "许可证号": "",
            "合同协议号": "",
            "包装种类": "",
            "件数": "",
            "运费": "",
            "保费": "",
            "杂费": "",
            "商品信息": [
                {{
                    "商品名称": "",
                    "型号": "PUMP 804X V2 SC",
                    "数量": "1",
                    "单价": "EUR20,000.00",
                    "总价": "EUR20,000.00"
                }},
                {{
                    "商品名称": "",
                    "型号": "PUMP 124H V2 SC",
                    "数量": "1",
                    "单价": "EUR12,000.00",
                    "总价": "EUR12,000.00"
                }}
            ],
            "error": "错误信息/空字符串"
        }}
    ]
数据提取规则：
    1. 合并相同字段，如果字段的值一样，那么只显示一个；如果字段的值不一致，那么进行错误提醒，而不是选择其中一个进行显示
    2. 请进行语义分析，同一个字段名称在不同的JSON中可能有不同的叫法，请进行语义分析，然后进行合并。
    3. 跟踪每个文档的错误信息并汇总
    4. 如有异常文档，在error中列出所有问题
    
请合并以下结果（只需返回JSON）：
{0}
"""
def verify_json_prompt(content):
    return f"""
            # 任务：对提供的外贸单据数据进行业务逻辑校验
            #校验规则要求：
                1、毛重≥净重
                2、运输方式为"保税港区"时，运抵国必须为"中国"
                3、成交方式为C&F时，运费不能为空；成交方式为CIF时，运费和保费都不能为空。
                4、件数或总数量不得为0；包装种类为"裸装"时件数必须为1
                5、总净重≥各项商品净重之和
                6、大写总价需与数字总价完全一致
                7、发票总价= 各商品单项总价之和
                8、箱单毛重 ≥ 净重
            # 注意：
                1、校验的字段为空，或者没有提供需要校验的字段，那么不进行校验该规则
                2、如大写总价需与数字总价完全一致，没有提供大写总价，那么就不需要返回错误信息
                3、只需要校验规则要求的字段，其他字段不要校验
            ## 输入文本：
            {content}
            
            ## 输出要求：
            {{
                "error": "错误信息/空字符串"
            }}
        
            """
def verify_list_json_prompt(json_list):
    return f"""
            # 任务：对提供的外贸单据数据进行业务逻辑校验
            # 校验规则要求如下：
                1、成交方式：在不同单据中是否一致
                2、总净重 = 各项商品总净重之和
                3、总毛重 = 各项商品总毛重之和
                4、商品信息：
                    需要按照顺序逐项比对不同单据中商品的数量、单位、单价、总价、规格型号、币制是否一致
            # 注意：
                1、校验的字段为空，或者没有提供需要校验的字段，那么不进行校验该规则,如合同中没有成交方式，那么就不需要用发票中的成交方式去校验合同中的成交方式
                2、只需要对规则要求的字段进行校验，其他字段不需要进行校验
                3、校验的字段内容如果一致，没有错误，那么不要返回错误信息
                4、不要进行中文和英文之间的校验
            ## 输入文本：
            {json_list}
            ## 输出要求：
            {{
                "error": "错误信息/空字符串"
            }}
            """
def split_product_fields_prompt(text,fields_template):
    #- 材质英文对应关系：PS(聚苯乙烯)、PE(聚乙烯)、EVA(乙烯-醋酸乙烯酯共聚物)、ABS(丙烯腈-丁二烯-苯乙烯共聚物),如果输入文本中出现PS、PE、EVA、ABS，那么直接返回对应的材质，不要使用英文
    return f"""
        # 任务：从商品描述提取结构化字段,严格按照输出要求的字段提取内容，不能新增、删除、修改输出要求的字段
        请注意：1、品牌类型和出口享惠情况必须返回对应的字典值（如"0"、"1"等），而不是描述性字符串。
            品牌类型:{{"0": "无品牌","1": "境内自主品牌","2": "境内收购品牌","3": "境外品牌(贴牌生产)","4": "境外品牌(其它)"}}
            出口享惠情况:{{"0": "出口货物在最终目的国(地区)不享受优惠关税","1": "出口货物在最终目的国(地区)享受优惠关税"}}
        2、如果型号没有识别到，默认型号为无型号
        3、品牌类型、出口享惠情况如果没有在文本中，显示为空
        4、 **用途** 和 **材质** 的提取规则：
            - 如果字段格式是 `用途(XX等)` 或 `材质(XX等)`，但未明确赋值（如 `用途:家用`），则按括号内的 `XX` 部分作为默认值。 
            -  如果字段格式是 `用途(XX等)` 或 `材质(XX等)`，但有明确的赋值，则不适用括号内的值
            - 优先使用输入文本中字段的内容，如果输入文本中没有该字段，那么使用输出要求里面的信息。
            - 示例：`用途(家用等)` → 返回 `"家用"`  
            - 示例：`材质(合金钢制等)` → 返回 `"合金钢制"`  
            - 如果括号内无内容（如 `用途()`），则返回空 `""`。 
        5、输入文本中提供了申报要素的值，如果是以1|0、2|0、3|0、4|0、0|0、1|1、2|1、3|1、4|1、0|1等格式开头，那么第一个数字为品牌类型，第二个数字为出口享惠情况
        6、如果输入文本上有对应内容，直接使用输入文本中的信息，不要自由发挥  
        7、非空字段在输入文本中没有提供，该字段返回空值，不要使用非空这样的信息
        8、尺寸等带单位等值，输出的内容需要带着单位不要只有数字
        9、不要过度依赖输入文本中的分隔符、标点符号，结合上下文进行判断，提取完整信息
        10、不要省略内容信息，如输入文本中是无型号，那么型号就应该显示为无型号，而不是无。
        11、内容中出现“等”字，不代表当前内容的结束，仍需要考虑后续的内容是否跟之前的关联
        12、品牌名称如果带有“牌”字的时候，“牌”字不要省略，如Salvinelli牌
        13、输出要求中的“其他”字段，除非在输入文本中有明确给到“其他”字段的值，否则返回空

        ## 输入文本：
        {text}
        
        ## 输出要求：
        {{
            {fields_template}
        }}
        
        """
def format_by_manifest(content):
     return f"""
        # 任务：OCR文本转JSON格式化
        
        ## 说明
        你的任务是将OCR识别的文本按指定JSON结构进行完整格式化，确保所有商品信息全部输出，不省略。不要添加任何解释或附加信息。不得假设或创造不存在的信息。
 
        ## 注意：即使下方内容较长，也必须严格按以下要求执行：
        1. 严格按照示例模板进行转换，保持相同的字段名和结构
        2. 确保从OCR文本中准确提取所有可找到的信息，没有的数据请直接返回空字符串
        3. 只能提取使用OCR文本中出现的实际数据，不得假设或创造不存在的信息
        4. 模版里面字段的值为要求或者参考信息，不要直接返回模版里面的字段值
        5. 在识别字段时，需要结合前后文的内容。
        6. 部分字段的参考信息或要求参考如下：
            - 提单号：数据来源于提单或者入货通知，优先从入货通知中提取，然后没有再使用提单中的提单号，通常描述有：BILL OF LADING NO、Bill of Lading Number、BILL OF LADING、B/L NO、B/L、D/R No、提单号。
            - 场站：数据来源于入货通知/进仓通知，通常描述为：场站、场站名称、入货场站
            - 船代：数据来源于入货通知/进仓通知，通常描述为：船代、舱单船代
            - 船名航次：数据来源于提单或者入货通知.通常描述有VESSEL AND VOYAGE NO;VESSEL/VOY;Ocean Vessel Voy. No.;Mother Vessel/Voyage;VESSEL/VOYAGE;Vessel & Voyage;OCEAN VESSEL VOYAGE NO.FLAG;船名/航次
            - 发货人：数据来源于提单，公司名称。通常描述为：SHIPPER;SHIPPER/EXPORTER;Exporter / Shipped From;Consignor
            - 发货人地址：数据来源于提单，地址。
            - 发货人电话：数据来源于提单，电话。
            - 收货人：数据来源于提单，公司名称。通常描述为：CONSIGNEE;Consignee / shipped to;Consigned to order；CNEE；
            - 收货人地址：数据来源于提单，地址。
            - 收货人电话：数据来源于提单，电话。
            - 收货人国家：数据来源于提单，国家。如果提单上没有明确国家，可以从收货人地址、卸货港、目的港从获取。
            - 通知人：数据来源于提单，包括联系电话和地址。通常描述为：NOTIFY PARTIES;NOTIFY PARTY;NOTIFY;Notify address
            - 通知人国家：数据来源于提单，国家。如果提单上没有明确国家，可以从通知人地址、卸货港、目的港从获取。
            - 通知人信息：如果文本上写的是same as cnee，那么通知人信息和收货人信息一致，包括通知人、通知人电话、通知人地址、通知人国家
            - 装货港：数据来源于提单，通常描述为：PORT OF LOADING ;PLACE OF RECEIPT;PORT OF RECEIPT;PLACE OF LOADING;起运港；装货港
            - 卸货港：数据来源于提单，通常描述为：PORT OF DISCHARGE;PLACE OF DELIVER;PLACE OF DISCHARGE;PORT OF UNLOADING;卸货港
            - 目的港：数据来源于提单，与卸货港一致，通常描述为：PLACE OF DELIVER;PLACE OF DISCHARGE
            - 唛头：数据来源于提单，通常描述为：Marks;Marks and Numbers;MARKS&NUMBERS;MARKS&NOS
            - 货物描述：数据来源于提单，通常描述为：Description of Packages and Goods;DESCRIPTION OF GOODS;
            - 包装：默认PKG
            - 箱型：数据来源于提单，通常描述为：SIZE
            - 箱号：数据来源于提单，通常描述为：Container Numbers;CONTAINER NO
            - 封号：数据来源于提单，通常描述为：Seal Numbers;SEAL NO;SEAL
            - 件数：数据来源于提单，通常描述为：QUANTITY;NO 0F CONTAINER OF PACKAGES;No of containers of p'kgs;NO. OF PKGS
            - 重量：数据来源于提单，通常描述为：Gross Cargo Weight;GROSS WEIGHT;WEIGHT
            - 尺码：数据来源于提单，通常描述为：Measurement;VOLUME
            - 称重方式：默认累加
        ## 示例模板
        {{
            "提单号":"",
            "发货人":"",
            "发货人地址":"",
            "发货人电话":"",
            "收货人":"",
            "收货人地址":"",
            "收货人电话":"",
            "收货人国家":"",
            "通知人":"",
            "通知人地址":"",
            "通知人电话":"",
            "通知人国家":"",
            "船名航次":"",
            "卸货港":"",
            "目的港":"",
            "唛头":"",
            "货物描述":"",
            "场站":"",
            "船代":"",
            "船公司":"",
            "货物类型":"",
            "称重方式":"",
            "集装箱信息":[
                {{
                    "箱号":"",
                    "箱型":"",
                    "包装":"",
                    "尺码":"",
                    "重量":"",
                    "件数":"",
                    "封号":"",
                    "皮重":""
                }}
            ]
        }}
        
        ## OCR识别文本
        {content}
        
        ## 注意
        - 只返回JSON格式的结果，不要添加任何其他解释
        - 如果某些字段在OCR文本中未找到，则在JSON中忽略该字段
        """
# 进口格式化提示词
def format_by_type_I( content, doc_type):
    """第二步：根据文档类型生成特定的格式化提示"""
    # 根据文档类型选择相应的示例模板
    templates = {
        "进口发票": {
            "example": """
            {
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "",
                "消费使用单位": "",
                "发票号": "BJ20250318INV3",
                "发票日期":"返回格式要求：yyyy-mm-dd",
                "运抵国": "中文名称",
                "合同协议号":"Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                "运输工具名称": "",
                "航次号": "",
                "包装种类": "",
                "成交方式": "例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等",
                "经停港": "",
                "启运港":"",
                "启运国": "中文名称",
                "提运单号": "",
                "总价大写":"",
                "币制":"",
                "运费":"",
                "运费币制":"",
                "保费":"",
                "保费币制":"",
                "商品信息": [
                    {"物料号":"Part Number/Material no,不是顺序号","商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码","规格型号": "PUMP 804X V2 SC", "数量及单位": "1", "单价": "EUR20,000.00", "总价": "EUR20,000.00","币制":"","境内目的地":"","申报要素":"","品牌":"BRAND","原产国":""},
                    {"物料号":"Part Number/Material no,不是顺序号","商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码","规格型号": "PUMP 124H V2 SC", "数量及单位": "1", "单价": "EUR12,000.00", "总价": "EUR12,000.00","币制":"","境内目的地":"","申报要素":"","品牌":"BRAND","原产国":""}
                ]
            }
            """
        },
        "进口箱单": {
            "example": """
            {
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "",
                "消费使用单位": "",
                "总件数": "1",
                "总净重": "700kg",
                "总毛重": "720kg",
                "监管方式":"",
                "备案号": "",
                "场站": "",
                "运输工具名称": "",
                "航次号": "",
                "经停港": "",
                "启运港":"",
                "合同协议号":"Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                "提运单号": "",
                "商品信息": [
                    {"物料号":"Part Number/Material no,不是顺序号","备案序号":"","商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码", "规格型号": "PUMP 804X V2 SC", "数量及单位": "1", "净重": "700kg""毛重":"720kg","品牌":"BRAND","原产国":""},
                    {"物料号":"Part Number/Material no,不是顺序号","备案序号":"","商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码", "规格型号": "PUMP 124H V2 SC", "数量及单位": "1", "净重": "700kg","毛重":"720kg","品牌":"BRAND","原产国":""}
                ]
            }
            """
        },
        "进口证书": {
            "example": """
            {
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                "协定享惠": "",
                "备案号": "",
                "随附单证代码E": "",
                "产品资质203": "",
                "随附单据文件类别": "",
                "商品编号": "",
                "申报地海关": "",
                "有效截止日期": "",
                "商品名称": "",
                "运抵国": "中文名称",
                "电子底账数据号":"",
            }
            """
        },
        "入货通知": {
            "example": """
            {
                "提运单号": "",
                "运输工具名称": "",
                "航次号": "",
                "场站": "",
                "船代":"",
                "船公司":"",
                "经停港":"",
                "启运港":"",
                "进境关别":"",
                "集装箱信息": [
                    {
                        "集装箱号": "",
                        "集装箱规格": "",
                        "集装箱自重":"",
                        "拼箱标识":"1:是；0:否。只返回数字代码",
                        "商品项号关系":""
                    }
                ]

            }
            """
        },
        "舱单样本": {
            "example": """
            {
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                "货物描述": "",
                "经停港": ""
            }
            """
        },
        "进口合同": {
            "example": """
            {
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "",
                "合同协议号": "Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                "运抵国": "中文名称",
                "消费使用单位": "",
                "包装种类": "",
                "商品信息": [
                    {
                        "物料号":"Part Number/Material no",
                        "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                        "商品名称": "",
                        "规格型号": "",
                        "数量及单位": "",
                        "总价": "",
                        "单价": "",
                        "币制":"",
                        "净重":"",
                        "毛重":"",
                        "品牌":"BRAND",
                        "境内目的地":"",
                        "申报要素":""
                    }
                ],
                "成交方式": "例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等",
                "品牌": ""
            }
            """
        },
        "报关单": {
            "example": """
            {
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "",
                "进境关别": "QINGDAO,CHINA/青岛；RIZHAO,日照",
                "进口日期": "",
                "申报日期": "",
                "备案号": "",
                "运输方式": "例如水路运输、航空运输等",
                "运输工具名称": "",
                "航次号": "",
                "提运单号": "",
                "消费使用单位": "",
                "监管方式": "例如一般贸易",
                "征免性质": "例如一般征税",
                "许可证号": "",
                "合同协议号": "Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                "启运国": "中文名称",
                "运抵国": "中文名称",
                "经停港": "",
                "入境口岸":"",
                "包装种类": "",
                "总件数": "",
                "总毛重": "",
                "总净重": "",
                "成交方式": "",
                "运费":"",
                "运费币制":"",
                "保费":"",
                "保费币制":"",
                "杂费":"",
                "杂费币制":"",
                "项号":"",
                "特殊关系确认":"",
                "价格影响确认":"",
                "与货物有关的特许权使用费支付确认":"",
                "公式定价确认":"",
                "暂定价格确认":"",
                "备注":"",
                "商品信息": [
                    {
                    "项号":"",
                    "备案序号":"",
                    "物料号":"Part Number/Material no",
                    "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                    "商品名称":"",
                    "品牌":"",
                    "规格型号": "",
                    "品牌类型":"",
                    "进口享惠情况":"",
                    "申报要素":"",
                    "数量及单位":"",
                    "法定第一数量及单位":"",
                    "法定第二数量及单位":"",
                    "单价":"单价",
                    "总价":"总价/金额/Amount",
                    "币制":"",
                    "原产国":"中文名称",
                    "最终目的国":"中文名称",
                    "境内目的地":"",
                    "征免方式":"",
                    "净重":""
                        }
                ],
                "集装箱信息": [
                    {
                        "集装箱号": "",
                        "集装箱规格": "",
                        "集装箱自重":"",
                        "拼箱标识":"1:是；0:否。只返回数字代码",
                        "商品项号关系":""
                    }
                ]
            }
            """
        },
        "提运单": {
            "example": """
            {
                "经停港":"",
                "运输工具名称": "",
                "航次号": "",
                "提运单号": "",
                "集装箱信息": [
                    {
                        "集装箱号": "",
                        "集装箱规格": "",
                        "集装箱自重":"",
                        "拼箱标识":"1:是；0:否。只返回数字代码",
                        "商品项号关系":""
                    }
                ]
            }
            """
        },
        "分箱明细": {
            "example": """
            {
                "分箱明细": [
                    {
                        "集装箱号": "",
                        "商品名称": "",
                        "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                        "体积": "",
                        "数量": "",
                        "数量单位": "",
                        "箱数": "",
                        "毛重": "",
                        "净重": ""
                    }
                ]
            }
            """
        },
        "申报要素": {
            "example": """
            {   
                "运费":"",
                "运费币制":"",
                "保费":"",
                "保费币制":"",
                "贸易国":"",
                "申报要素": [
                    {
                        "物料号":"Part Number/Material no,不是顺序号",
                        "商品名称": "只提取商品名称，不要提取规格型号",
                        "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                        "申报要素": "",
                        "品牌": "",
                        "规格型号": "规格/型号/规格型号",
                        "品牌类型": "",
                        "进口享惠情况": "",
                        "境内目的地": "",
                        "单价":"",
                        "原产国":"COO",
                        "币制":""
                    }
                ]
            }
            """
        },
        "历史申报记录": {
            "example": """
            {   
                "历史申报记录": [
                    {
                        "物料号":"Part Number/Material no,不是顺序号",
                        "商品名称": "只提取商品名称，不要提取规格型号",
                        "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                        "申报要素": "",
                        "品牌": "",
                        "型号": "规格/型号/规格型号",
                        "品牌类型": "",
                        "进口享惠情况": "",
                        "境内目的地": "",
                        "单价":"",
                        "原产国":"COO",
                        "币制":""
                    }
                ]
            }
            """
        },
        "检验检疫": {
            "example": """
            {   
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "",
                "电子底账数据号":"",
                "商品列表": [
                    {
                        "商品名称": "只提取商品名称，不要提取规格型号",
                        "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                    }
            }
            """
        },
        "其他": {
            "example": """
            {
                
                "境内收货人": "只要企业名称，不要地址电话",
                "境外发货人": "",
                "进境关别": "QINGDAO,CHINA/青岛；RIZHAO,日照",
                "进口日期": "",
                "申报日期": "",
                "备案号": "",
                "运输方式": "例如水路运输、航空运输等",
                "运输工具名称": "",
                "航次号": "",
                "提运单号": "",
                "消费使用单位": "",
                "监管方式": "例如一般贸易",
                "征免性质": "例如一般征税",
                "许可证号": "",
                "合同协议号": "Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                "启运国": "中文名称",
                "运抵国": "中文名称",
                "经停港": "",
                "启运港": "",
                "入境口岸":"",
                "包装种类": "",
                "总件数": "",
                "总毛重": "",
                "总净重": "",
                "成交方式": "",
                "运费":"",
                "运费币制":"",
                "保费":"",
                "保费币制":"",
                "杂费":"",
                "杂费币制":"",
                "项号":"",
                "特殊关系确认":"",
                "价格影响确认":"",
                "与货物有关的特许权使用费支付确认":"",
                "公式定价确认":"",
                "暂定价格确认":"",
                "电子底账数据号":"",
            }
            """
        }
    }
    
    # 获取选定的模板
    template = templates.get(doc_type, templates["其他"])
    if doc_type == "报关单":
            doc_prompt = """
            8. 商品信息：
                下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
                重复的商品信息不要进行合并，严格按照输入文本中的项数输出。
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 品牌类型:
                    如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                    品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                - 品牌：品牌名称，从OCR文本中提取品牌名称
                - 进口享惠情况:
                    如果OCR文本中没有明确进口享惠情况，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的进口享惠情况进行返回。
                    进口享惠情况:出口货物在最终目的国(地区)不享受优惠关税、出口货物在最终目的国(地区)享受优惠关税、享惠、无享惠、不享惠
                - 申报要素：
                    在报关单中，申报要素通常对应“规格型号”或“申报要素”字段的全部内容。
                    根据海关对进口商品申报的规定，申报要素包括品牌类型、进口享惠情况、材质、用途、品牌、型号、扩展要素及特殊商品要素等，用于核实商品归类的准确性。
                    如果数据中提供了申报要素，则其值应完整呈现所有相关信息，例如："1|0||||||"。
                - 监管方式:例如一般贸易、进料加工、来料加工等
                - 备案号:备案号/备案证号/备案登记号/手(账)册号/手(账)册编号，字母（B、C、E、H、TW、L）+数字
                - 成交方式:成交方式与成交代码的对应关系,CIF/DAP/DPU/DDP/CIP:1、CFR/CPT/C&F:2、EXW:7、FCA/FOB/FAS:3.返回成交方式，不要返回成交代码。
                - 境内收货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                        4. 大部分情况境内收货人跟消费使用单位一致。
                - 境外发货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                - 总价:总价/金额，注意区分单价和总价,总价不可能比单价小。
                - 单价：如果单价是238.00/1000PCS,那么返回的单价应该是0.238。或者表头中有提到单价数量，那么也需要进行计算
                - 最终目的国:一定是国家，一定是中国。
                - 境内目的地：
                    输入数据：可能包含单独的县名（如"单县"）、省+县（如"山东博兴"）、或区名（如"朝阳区"）。
                    输出要求：
                        如果只有 县名（如"单县"），补全为 "所属地级市+县名"（如"菏泽单县"）。
                        如果已有 省+县（如"山东博兴"），补全为 "省+地级市+县"（如"山东滨州博兴"）。
                        如果只有 区名（如"朝阳区"），默认补全为 "北京+区名"（如"北京朝阳区"），除非上下文明确指向其他城市（如"长春朝阳区"）。
                        如果只有 市名（如"济宁"），直接返回原值
                        不修改原有信息，仅补充缺失的市级名称。
                        对无法确定的情况保留原样。
                        如果信息中包含开发区、保税区、物流中心、加工区，那么不处理，直接返回原值。
                    注意：境内目的地这个字段是属于商品信息的内部字段，不要放到其他位置
                - 数量及单位：
                    如标题中同时包含“数量”和“单位”，应将其合并为一个字段，格式为“数量+单位”（例如：“100 PCS”应处理为“100件”）。
                    单据中如单位既有中文又有英文，优先使用中文；如单位为英文，优先转换为中文单位，如：
                        PCS → 件 / 把；
                        SETS → 套；
                        ROLLS → 捆；
                        缺乏明确对应时，可结合商品信息判断最适合的中文单位表达。
                        如果有多个单位，默认使用第一个，如个/台，那么返回个
                    若数值中包含千分位分隔符（如 “29,822,000片”），需移除所有逗号，仅保留数字与单位，结果为“29822000片”。
                - 法定第一数量及单位 / 法定第二数量及单位：
                    若商品信息中的数量及单位包含 三个值：
                        第1个为“法定第二数量及单位”；
                        第2个为“法定第一数量及单位”；
                        第3个为“数量及单位”。
                    若包含 两个值：
                        第1个为“法定第一数量及单位”；
                        第2个为“数量及单位”。
                    若仅包含 一个值：
                        该值视为“数量及单位”。
                    字段信息可能会跟净重或者数量及单位重复，但仍然需要按照要求进行提取。
                - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                - 商品名称：
                    商品名称、品名
                    不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称。
                    请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                - 规格型号：规格型号、规格、型号，只提取规格型号的内容，不要带商品名称一起返回
                - 毛重、净重：在提取的时候需要将单位一起输出
                - 商品信息中的净重和毛重应该是该项商品的总净重和总毛重，而不是单个商品的净重和毛重
                - 总毛重：毛重、TG.W.、GW。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                - 总净重：净重、TN.W.、NW。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                - 总件数：件数、PACKED IN。若数值中包含千分位分隔符（如 “29,822,000”），需移除所有逗号，仅保留数字与单位，结果为“29822000”。
                - 币制：
                    1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                    2.RMB、人民币映射为CNY
                    3.美金映射为USD
                - 备案序号：只有当备案号字段存在的时候，备案序号才需要返回。
                - 许可证号：如果报关单中包含 "许可证号" 或类似表述，则提取其后的编号（如 `25-15-215800`）
                - 集装箱号：箱号、箱封号、集装箱号
                    集装箱号通常由以下部分组成：4 位承运人代码（字母）+ 7 位数字序号 + 1 位校验码（有时省略或用斜杠分隔）
                    按照集装箱号的规则提取，多余的部分删掉，不要返回
                - 经停港：
                    输出要求：
                        1. 经停港。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                        2. 不要从其他信息中获取。
                        3. 只显示中文名称，例如纽约
                - 包装种类：根据文本中提取的内容对应标准的包装种类/包装方式，只返回中文包装种类。标准包装种类有：散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                - 运费：只取总的运费，不要取分项的运费。
                - 保费：只取总的保费，不要取分项的保费。
                - 杂费：只取总的杂费，不要取分项的杂费。
                - 备注：提取报关单中的备注完整信息。
                - 运输工具名称、航次号：
                    需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                    中文名称和英文名称同时存在的话，只提取英文名称
                - 原产国：原产国、ULA、Country of orgin
                - 集装箱规格：根据提取到的集装箱规格信息，返回标准的海关代码
                    海关代码与集装箱规格之间的对应关系：
                    11：普通2*标准箱（L）；12：冷藏2*标准箱（L）；13：罐式2*标准箱（L）；21：普通标准箱（S）；22：冷藏标准箱（S）；23：罐式标准箱（S）；31：其他标准箱（S）；32：其他2*标准箱（L）
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如最终目的国一般为中国
            12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
        """
    elif doc_type == "进口发票":
            doc_prompt = """
            8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 品牌类型:
                    如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                    品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                - 品牌：品牌名称，从OCR文本中提取品牌名称
                - 进口享惠情况:
                    如果OCR文本中没有明确进口享惠情况，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的进口享惠情况进行返回。
                    进口享惠情况:出口货物在最终目的国(地区)不享受优惠关税、出口货物在最终目的国(地区)享受优惠关税、享惠、无享惠、不享惠
                - 申报要素:根据海关申报要素的要求设定的进进口申报条件‌，用于核实商品归类是否正确，通常申报要素可能包括品牌类型、进口享惠情况、材质、用途、品牌、型号、扩展要素、特殊商品要素‌等信息。例如1|0||||||、空。数据中如果给到了申报要素，那么申报要素的值为申报要素全部的信息。
                - 成交方式:成交方式:成交代码的对应关系,CIF/DAP/DPU/DDP/CIP:1、CFR/CPT/C&F:2、EXW:7、FCA/FOB/FAS:3.返回成交方式，不要返回成交代码。
                - 境内收货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                        4. 大部分情况境内收货人跟消费使用单位一致。
                - 境外发货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                - 总价:总价/金额，注意区分单价和总价。总价一定大于等于单价。总价不可能比单价小。
                - 单价：如果单价是238.00/1000PCS,那么返回的单价应该是0.238。或者表头中有提到单价数量，那么也需要进行计算
                - 境内目的地：
                    输入数据：可能包含单独的县名（如"单县"）、省+县（如"山东博兴"）、或区名（如"朝阳区"）。
                    输出要求：
                        如果只有 县名（如"单县"），补全为 "所属地级市+县名"（如"菏泽单县"）。
                        如果已有 省+县（如"山东博兴"），补全为 "省+地级市+县"（如"山东滨州博兴"）。
                        如果只有 区名（如"朝阳区"），默认补全为 "北京+区名"（如"北京朝阳区"），除非上下文明确指向其他城市（如"长春朝阳区"）。
                        不修改原有信息，仅补充缺失的市级名称。
                        对无法确定的情况保留原样。
                        如果信息中包含开发区、保税区、物流中心、加工区，那么不处理，直接返回原值。
                - 数量及单位：标题中有数量及单位，那么数量及单位为数量及单位，如果是分开的两个字段，需要将数量和单位合并到一起。
                - 数量及单位：单据中单位如果有中文优先使用中文。如果没有中文单位，则需要根据商品信息显示中文名称。如PCS:件/把;SETS:套;Rolls:捆等
                - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                - 商品名称：
                    OCR识别文本中有中文名称和英文名称的时候，优先使用中文名称，没有中文名称的时候，使用英文名称，不要翻译。
                    如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                    注意区分多个商品。示例：
                        30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                        200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                - 规格型号：
                    如果规格型号中包含商品名称和型号，要进行拆分。例：500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML；Semi-rotary drive DRRD-60-180-FH-Y10A-SG，拆分后规格型号为DRRD-60-180-FH-Y10A-SG
                    只提取规格型号的内容，绝对不要包含任何商品名称
                - 币制：
                    1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                    2.RMB、人民币映射为CNY
                    3.美金映射为USD
                - 经停港：
                    输出要求：
                        1. 经停港。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                        2. 不要从其他信息中获取。
                        3. 只显示中文名称，例如纽约
                - 包装种类：根据文本中提取的内容对应标准的包装种类，只返回中文包装种类。标准包装种类有：散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                - 运费：结合上下文，从输入的文本中只返回总的运费，不要重复提取。
                - 保费：只提取总的保费，不要取分项的保费。
                - 杂费：只提取总的杂费，不要取分项的杂费。
                - 运输工具名称、航次号：
                    需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                    中文名称和英文名称同时存在的话，只提取英文名称
                    若文本中出现“Shipped per S.S.”，此为运输方式（Shipping Service/Steam Ship缩写），**不是具体运输工具名称或船名**，不要将其提取为运输工具名称或航次号，**应忽略处理**。
                - 原产国：原产国、ULA、Country of orgin
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如原产国一般为中国
            12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
            13.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
        """
    elif doc_type == "进口箱单":
            doc_prompt = """
            8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
            9. 国家和港口，直接使用中文名称
            9. 部分字段的参考信息或要求参考如下：
                - 品牌类型:
                    如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                    品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                - 品牌：品牌名称，从OCR文本中提取品牌名称
                - 备案号:备案号/备案证号/备案登记号/手(账)册号/手(账)册编号，字母（B、C、E、H、TW、L）+数字
                - 境内收货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                        4. 大部分情况境内收货人跟消费使用单位一致。
                - 境外发货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                - 数量及单位：标题中有数量及单位，那么数量及单位为数量及单位，如果是分开的两个字段，需要将数量和单位合并到一起。
                - 数量及单位：单据中单位如果有中文优先使用中文。如果没有中文单位，则需要根据商品信息显示中文名称。如PCS:件/把;SETS:套;Rolls:捆等
                - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                - 商品名称：不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称。如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                - 商品名称：注意区分多个商品。
                    示例：
                    30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                    200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                - 规格型号：
                    如果规格型号中包含商品名称和型号，要进行拆分。例：500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML；Semi-rotary drive DRRD-60-180-FH-Y10A-SG，拆分后规格型号为DRRD-60-180-FH-Y10A-SG
                    只提取规格型号的内容，绝对不要包含任何商品名称
                - 毛重、净重：在提取的时候需要将单位一起输出
                - 商品信息中的净重和毛重应该是该项商品的总净重和总毛重，而不是单个商品的净重和毛重
                - 总毛重：毛重、TG.W.、GW。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                - 总净重：净重、TN.W.、NW。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                - 总件数：件数、PACKED IN。若数值中包含千分位分隔符（如 “29,822,000”），需移除所有逗号，仅保留数字与单位，结果为“29822000”。
                - 备案序号：只有当备案号字段存在的时候，备案序号才需要返回。
                - 经停港：
                    输出要求：
                        1. 经停港。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                        2. 不要从其他信息中获取。
                        3. 只显示中文名称，例如纽约
                - 包装种类：根据文本中提取的内容对应标准的包装种类，只返回中文包装种类。标准包装种类有：散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                - 运输工具名称、航次号：
                    需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                    中文名称和英文名称同时存在的话，只提取英文名称
                - 原产国：原产国、ULA、Country of orgin
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如目的国一般为中国
            12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
            13.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
        """
    elif doc_type == "进口证书":
            doc_prompt = """
            8. 国家和港口，直接使用中文名称
            9. 部分字段的参考信息或要求参考如下：
                - 备案号:备案号/备案证号/备案登记号/手(账)册号/手(账)册编号，字母（B、C、E、H、TW、L）+数字
                - 境内收货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                        4. 大部分情况境内收货人跟消费使用单位一致。
                - 境外发货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
            10. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。
        
        """
    elif doc_type == "入货通知":
            doc_prompt = """
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 集装箱号：箱号、箱封号、集装箱号
                    集装箱号通常由以下部分组成：4 位承运人代码（字母）+ 7 位数字序号 + 1 位校验码（有时省略或用斜杠分隔）
                    按照集装箱号的规则提取，多余的部分删掉，不要返回
                - 经停港：
                    输出要求：
                        1. 经停港。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                        2. 不要从其他信息中获取。
                        3. 只显示中文名称，例如纽约
                - 运输工具名称、航次号：
                    需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                    中文名称和英文名称同时存在的话，只提取英文名称
                - 集装箱规格：根据提取到的集装箱规格信息，返回标准的海关代码
                    海关代码与集装箱规格之间的对应关系：
                    11：普通2*标准箱（L）；12：冷藏2*标准箱（L）；13：罐式2*标准箱（L）；21：普通标准箱（S）；22：冷藏标准箱（S）；23：罐式标准箱（S）；31：其他标准箱（S）；32：其他2*标准箱（L）
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如目的国一般为中国
        """
    elif doc_type == "舱单样本":
            doc_prompt = """
            
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 境内收货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                        4. 大部分情况境内收货人跟消费使用单位一致。
                - 境外发货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                - 经停港：
                    输出要求：
                        1. 经停港。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                        2. 不要从其他信息中获取。
                        3. 只显示中文名称，例如纽约
                - 运输工具名称、航次号：
                    需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                    中文名称和英文名称同时存在的话，只提取英文名称
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如目的国一般为中国
        """
    elif doc_type == "进口合同":
            doc_prompt = """
            8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 成交方式:成交方式:成交代码的对应关系,CIF/DAP/DPU/DDP/CIP:1、CFR/CPT/C&F:2、EXW:7、FCA/FOB/FAS:3.返回成交方式，不要返回成交代码。
                - 境内收货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                        4. 大部分情况境内收货人跟消费使用单位一致。
                - 境外发货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                - 总价:总价/金额，注意区分单价和总价
                - 单价：如果单价是238.00/1000PCS,那么返回的单价应该是0.238。或者表头中有提到单价数量，那么也需要进行计算
                
                - 数量及单位：标题中有数量及单位，那么数量及单位为数量及单位，如果是分开的两个字段，需要将数量和单位合并到一起。
                - 数量及单位：单据中单位如果有中文优先使用中文。如果没有中文单位，则需要根据商品信息显示中文名称。如PCS:件/把;SETS:套;Rolls:捆等
                - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                - 商品名称：不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称。如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                - 商品名称：注意区分多个商品。
                    示例：
                    30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                    200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                - 规格型号：
                    如果规格型号中包含商品名称和型号，要进行拆分。例：500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML；Semi-rotary drive DRRD-60-180-FH-Y10A-SG，拆分后规格型号为DRRD-60-180-FH-Y10A-SG
                    只提取规格型号的内容，绝对不要包含任何商品名称
                - 币制：
                    1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                    2.RMB、人民币映射为CNY
                    3.美金映射为USD 
                - 包装种类：根据文本中提取的内容对应标准的包装种类，只返回中文包装种类。标准包装种类有：散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                - 运输工具名称、航次号：
                    需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                    中文名称和英文名称同时存在的话，只提取英文名称
            12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
            """
    elif doc_type == "提运单":
            doc_prompt = """
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 集装箱号：箱号、箱封号、集装箱号
                    集装箱号通常由以下部分组成：4 位承运人代码（字母）+ 7 位数字序号 + 1 位校验码（有时省略或用斜杠分隔）
                    按照集装箱号的规则提取，多余的部分删掉，不要返回
                - 经停港：
                    输出要求：
                        1. 经停港/目地港/卸货港/交货地/Port of Discharge。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                        2. 不要从其他信息中获取。
                        3. 只显示中文名称，例如纽约
                - 运输工具名称、航次号：
                    需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                    中文名称和英文名称同时存在的话，只提取英文名称
                - 集装箱规格：根据提取到的集装箱规格信息，返回标准的海关代码
                    海关代码与集装箱规格之间的对应关系：
                    11：普通2*标准箱（L）；12：冷藏2*标准箱（L）；13：罐式2*标准箱（L）；21：普通标准箱（S）；22：冷藏标准箱（S）；23：罐式标准箱（S）；31：其他标准箱（S）；32：其他2*标准箱（L）
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如目的国一般为中国
            """
    elif doc_type == "分箱明细":
            doc_prompt = """
            """
    elif doc_type == "申报要素":
            doc_prompt = """
            8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 品牌类型:
                    如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                    品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                - 品牌：品牌名称，从OCR文本中提取品牌名称
                - 进口享惠情况:
                    如果OCR文本中没有明确进口享惠情况，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的进口享惠情况进行返回。
                    进口享惠情况:出口货物在最终目的国(地区)不享受优惠关税、出口货物在最终目的国(地区)享受优惠关税、享惠、无享惠、不享惠
                - 申报要素:根据海关申报要素的要求设定的进进口申报条件‌，用于核实商品归类是否正确，通常申报要素可能包括品牌类型、进口享惠情况、材质、用途、品牌、型号、扩展要素、特殊商品要素‌等信息。例如1|0||||||、空。数据中如果给到了申报要素，那么申报要素的值为申报要素全部的信息。
                - 境内目的地：
                    输入数据：可能包含单独的县名（如"单县"）、省+县（如"山东博兴"）、或区名（如"朝阳区"）。
                    输出要求：
                        如果只有 县名（如"单县"），补全为 "所属地级市+县名"（如"菏泽单县"）。
                        如果已有 省+县（如"山东博兴"），补全为 "省+地级市+县"（如"山东滨州博兴"）。
                        如果只有 区名（如"朝阳区"），默认补全为 "北京+区名"（如"北京朝阳区"），除非上下文明确指向其他城市（如"长春朝阳区"）。
                        不修改原有信息，仅补充缺失的市级名称。
                        对无法确定的情况保留原样。
                        如果信息中包含开发区、保税区、物流中心、加工区，那么不处理，直接返回原值。
                - 商品名称：不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称。如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                - 商品名称：注意区分多个商品。
                    示例：
                    30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                    200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                - 规格型号：
                    如果规格型号中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。
                    只提取规格型号的内容，绝对不要包含任何商品名称
                - 币制：
                    1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                    2.RMB、人民币映射为CNY
                    3.美金映射为USD
                - 运费：只取总的运费，不要取分项的运费。
                - 保费：只取总的保费，不要取分项的保费。
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如最终目的国一般为中国
            12.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
        
        """
    elif doc_type == "检验检疫":
        doc_prompt = """
            8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下
                - 境内收货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                        4. 大部分情况境内人发货人跟消费使用单位一致。
                - 境外发货人:
                    输出要求：
                        1. 只要企业名称，不要地址电话。
                        2. 在提供的文本中如果有中文名称优先使用中文名称。
                        3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译
            """
    elif doc_type == "其他":
        doc_prompt = """
            """
    elif doc_type == "历史申报记录":
        doc_prompt = """
            8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
            9. 国家和港口，直接使用中文名称
            10. 部分字段的参考信息或要求参考如下：
                - 品牌类型:
                    如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                    品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                - 品牌：品牌名称，从OCR文本中提取品牌名称
                - 进口享惠情况:
                    如果OCR文本中没有明确进口享惠情况，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的进口享惠情况进行返回。
                    进口享惠情况:出口货物在最终目的国(地区)不享受优惠关税、出口货物在最终目的国(地区)享受优惠关税、享惠、无享惠、不享惠
                - 申报要素:根据海关申报要素的要求设定的进进口申报条件‌，用于核实商品归类是否正确，通常申报要素可能包括品牌类型、进口享惠情况、材质、用途、品牌、型号、扩展要素、特殊商品要素‌等信息。例如1|0||||||、空。数据中如果给到了申报要素，那么申报要素的值为申报要素全部的信息。
                - 境内目的地：
                    输入数据：可能包含单独的县名（如"单县"）、省+县（如"山东博兴"）、或区名（如"朝阳区"）。
                    输出要求：
                        如果只有 县名（如"单县"），补全为 "所属地级市+县名"（如"菏泽单县"）。
                        如果已有 省+县（如"山东博兴"），补全为 "省+地级市+县"（如"山东滨州博兴"）。
                        如果只有 区名（如"朝阳区"），默认补全为 "北京+区名"（如"北京朝阳区"），除非上下文明确指向其他城市（如"长春朝阳区"）。
                        不修改原有信息，仅补充缺失的市级名称。
                        对无法确定的情况保留原样。
                        如果信息中包含开发区、保税区、物流中心、加工区，那么不处理，直接返回原值。
                - 商品名称：不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称。如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                - 商品名称：注意区分多个商品。
                    示例：
                    30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                    200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                - 规格型号：
                    如果规格型号中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。
                    只提取规格型号的内容，绝对不要包含任何商品名称
                - 币制：
                    1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                    2.RMB、人民币映射为CNY
                    3.美金映射为USD
                - 运费：只取总的运费，不要取分项的运费。
                - 保费：只取总的保费，不要取分项的保费。
            11. 处理的单据都是进口业务涉及到的单据，请根据单据类型，按照进口业务逻辑进行处理。如最终目的国一般为中国
            12.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
        
        """
    else:
        doc_prompt = """
        """
    # ## 强制校验
    # - 在提取和清洗完成所有字段值后，必须对最终输出的JSON对象进行逐字段校验。
    # - 校验内容包括但不限于：字段值中不得含有非法字符（"、“、”、'、/、\\、\n、\r、\t）。
    # - 如果任意字段未通过校验，提示词必须强制重写输出结果，直到所有字段均合规为止。
    return f"""
    # 任务：OCR文本转JSON格式化
    
    ## 说明
    你的任务是将OCR识别的文本按指定JSON结构进行完整格式化，确保所有商品信息全部输出，不省略。不要添加任何解释或附加信息。不得假设或创造不存在的信息。该文档已识别为【{doc_type}】类型。
    ## Skills：
    - 所有字段值在提取后，必须逐项清除以下字符：双引号（"、“、”）、单引号（'）、斜杠（/）、反斜杠（\）、换行符（\n）、回车符（\r）、制表符（\t）。该步骤为**强制执行**。
    - 例如 OCR识别为：“生产件的通用零件编号后加注‘/TY’”，应清洗为：“生产件的通用零件编号后加注TY”
    - 不允许字段值中保留任何以上字符

    ## 注意：即使下方内容较长，也必须严格按以下要求执行：
    1. 严格按照示例模板进行转换，保持相同的字段名和结构
    2. 确保从OCR文本中准确提取所有可找到的信息，没有的数据请直接返回空字符串
    3. 日期格式统一转换为yyyy-mm-dd
    4. 只能提取使用OCR文本中出现的实际数据，不得假设或创造不存在的信息。
    5. 模版里面字段的值为要求或者参考信息，不要直接返回模版里面的字段值
    6.在识别字段时，需要结合前后文的内容。
    7.在识别表格内容时，要区分标题行和内容行。注意分析表头和内容的语义以及内容中的空格，表头的标题要跟内容对应上，OCR的文本中会存在信息换行或者空行的情况，请根据实际情况进行处理。
    {doc_prompt}
    ## 示例模板
    {template["example"]}
    
    ## OCR识别文本
    {content}
    
    ## 注意
    - 只返回JSON格式的结果，不要添加任何其他解释
    - 如果某些字段在OCR文本中未找到，则在JSON中忽略该字段
    """

def format_by_type( content, doc_type):
        """第二步：根据文档类型生成特定的格式化提示"""
        # 根据文档类型选择相应的示例模板
        templates = {
            "出口发票": {
                "example": """
                {
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "发票号": "BJ20250318INV3",
                    "发票日期":"返回格式要求：yyyy-mm-dd",
                    "运抵国": "中文名称",
                    "合同协议号":"Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                    "运输工具名称": "",
                    "航次号": "",
                    "包装种类": "",
                    "成交方式": "例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等",
                    "指运港": "",
                    "贸易国": "中文名称",
                    "提运单号": "",
                    "总价大写":"",
                    "总件数": "",
                    "总毛重": "",
                    "总净重": "",
                    "币制":"",
                    "运费":"",
                    "运费币制":"",
                    "保费":"",
                    "保费币制":"",
                    "商品信息": [
                        {"商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码","规格型号": "PUMP 804X V2 SC", "数量及单位": "1", "单价": "EUR20,000.00", "总价": "EUR20,000.00","币制":"","境内货源地":"","申报要素":"","品牌":"BRAND"},
                        {"商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码","规格型号": "PUMP 124H V2 SC", "数量及单位": "1", "单价": "EUR12,000.00", "总价": "EUR12,000.00","币制":"","境内货源地":"","申报要素":"","品牌":"BRAND"}
                    ]
                }
                """
            },
            "出口箱单": {
                "example": """
                {
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "总件数": "1",
                    "总净重": "700kg",
                    "总毛重": "720kg",
                    "监管方式":"",
                    "备案号": "",
                    "场站": "",
                    "运输工具名称": "",
                    "航次号": "",
                    "指运港": "",
                    "合同协议号":"Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                    "提运单号": "",
                    "商品信息": [
                        {"备案序号":"","商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码", "规格型号": "PUMP 804X V2 SC", "数量及单位": "1", "净重": "700kg""毛重":"720kg","品牌":"BRAND"},
                        {"备案序号":"","商品名称": "","商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码", "规格型号": "PUMP 124H V2 SC", "数量及单位": "1", "净重": "700kg","毛重":"720kg","品牌":"BRAND"}
                    ]
                }
                """
            },
            "出口证书": {
                "example": """
                {
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "协定享惠": "",
                    "备案号": "",
                    "随附单证代码E": "",
                    "产品资质203": "",
                    "随附单据文件类别": "",
                    "商品编号": "",
                    "申报地海关": "",
                    "有效截止日期": "",
                    "商品名称": "",
                    "运抵国": "中文名称",
                    "电子底账数据号":"",
                }
                """
            },
            "入货通知": {
                "example": """
                {
                    "提运单号": "",
                    "运输工具名称": "",
                    "航次号": "",
                    "场站": "",
                    "船代":"",
                    "船公司":"",
                    "指运港":"",
                    "出境关别":"",
                    "集装箱信息": [
                        {
                            "集装箱号": "",
                            "集装箱规格": "",
                            "集装箱自重":"",
                            "拼箱标识":"1:是；0:否。只返回数字代码",
                            "商品项号关系":""
                        }
                    ]

                }
                """
            },
            "舱单样本": {
                "example": """
                {
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "货物描述": "",
                    "指运港": ""
                }
                """
            },
            "出口合同": {
                "example": """
                {
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "合同协议号": "Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                    "运抵国": "中文名称",
                    "包装种类": "",
                    "商品信息": [
                        {
                            "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                            "商品名称": "",
                            "规格型号": "",
                            "数量及单位": "",
                            "总价": "",
                            "单价": "",
                            "币制":"",
                            "净重":"",
                            "毛重":"",
                            "品牌":"BRAND",
                            "境内货源地":"",
                            "申报要素":""
                        }
                    ],
                    "成交方式": "例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等",
                    "品牌": ""
                }
                """
            },
            "报关单": {
                "example": """
                {
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "",
                    "出境关别": "QINGDAO,CHINA/青岛；RIZHAO,日照",
                    "出口日期": "",
                    "申报日期": "",
                    "备案号": "",
                    "运输方式": "例如水路运输、航空运输等",
                    "运输工具名称": "",
                    "航次号": "",
                    "提运单号": "",
                    "生产销售单位": "",
                    "监管方式": "例如一般贸易",
                    "征免性质": "例如一般征税",
                    "许可证号": "",
                    "合同协议号": "Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                    "贸易国": "中文名称",
                    "运抵国": "中文名称",
                    "指运港": "",
                    "离境口岸":"",
                    "包装种类": "",
                    "总件数": "",
                    "总毛重": "",
                    "总净重": "",
                    "成交方式": "",
                    "运费":"",
                    "运费币制":"",
                    "保费":"",
                    "保费币制":"",
                    "杂费":"",
                    "杂费币制":"",
                    "项号":"",
                    "特殊关系确认":"",
                    "价格影响确认":"",
                    "与货物有关的特许权使用费支付确认":"",
                    "公式定价确认":"",
                    "暂定价格确认":"",
                    "备注":"",
                    "商品信息": [
                        {"项号":"",
                        "备案序号":"",
                        "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                        "商品名称":"",
                        "品牌":"",
                        "规格型号": "",
                        "品牌类型":"",
                        "出口享惠情况":"",
                        "申报要素":"",
                        "数量及单位":"",
                        "法定第一数量及单位":"",
                        "法定第二数量及单位":"",
                        "单价":"单价",
                        "总价":"总价/金额/Amount",
                        "币制":"",
                        "原产国":"中文名称",
                        "最终目的国":"中文名称",
                        "境内货源地":"",
                        "征免方式":"",
                        "净重":""
                         }
                    ],
                    "集装箱信息": [
                        {
                            "集装箱号": "",
                            "集装箱规格": "",
                            "集装箱自重":"",
                            "拼箱标识":"1:是；0:否。只返回数字代码",
                            "商品项号关系":""
                        }
                    ]
                }
                """
            },
            "提运单": {
                "example": """
                {
                    "指运港":"",
                    "运输工具名称": "",
                    "航次号": "",
                    "提运单号": "",
                    "集装箱信息": [
                        {
                            "集装箱号": "",
                            "集装箱规格": "",
                            "集装箱自重":"",
                            "拼箱标识":"1:是；0:否。只返回数字代码",
                            "商品项号关系":""
                        }
                    ]
                }
                """
            },
            "分箱明细": {
                "example": """
                {
                    "分箱明细": [
                        {
                            "集装箱号": "",
                            "商品名称": "",
                            "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                            "体积": "",
                            "数量": "",
                            "数量单位": "",
                            "箱数": "",
                            "毛重": "",
                            "净重": ""
                        }
                    ]
                }
                """
            },
            "申报要素": {
                "example": """
                {   
                    "运费":"",
                    "运费币制":"",
                    "保费":"",
                    "保费币制":"",
                    "贸易国":"",
                    "申报要素": [
                        {
                            "商品名称": "只提取商品名称，不要提取规格型号",
                            "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                            "申报要素": "",
                            "品牌": "",
                            "规格型号": "规格/型号/规格型号",
                            "品牌类型": "",
                            "出口享惠情况": "",
                            "境内货源地": ""
                        }
                    ]
                }
                """
            },
            "检验检疫": {
                "example": """
                {   
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "电子底账数据号":"",
                    "商品列表": [
                        {
                            "商品名称": "只提取商品名称，不要提取规格型号",
                            "商品编号": "商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码",
                        }
                }
                """
            },
            "随附单证信息":{
                  "example": """
                {   
                    "随附单证信息": [
                        {
                            "随附单证代码": "",
                            "单证编号": "",
                        }
                }
                """
            },
            "其他": {
                "example": """
                {
                    
                    "境内发货人": "只要企业名称，不要地址电话",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "出境关别": "QINGDAO,CHINA/青岛；RIZHAO,日照",
                    "出口日期": "",
                    "申报日期": "",
                    "备案号": "",
                    "运输方式": "例如水路运输、航空运输等",
                    "运输工具名称": "",
                    "航次号": "",
                    "提运单号": "",
                    "监管方式": "例如一般贸易",
                    "征免性质": "例如一般征税",
                    "许可证号": "",
                    "合同协议号": "Contract No:/No/INVOICE NO/合同编号/合同号协议号/合同协议号",
                    "贸易国": "中文名称",
                    "运抵国": "中文名称",
                    "指运港": "",
                    "离境口岸":"",
                    "包装种类": "",
                    "总件数": "",
                    "总毛重": "",
                    "总净重": "",
                    "成交方式": "",
                    "运费":"",
                    "运费币制":"",
                    "保费":"",
                    "保费币制":"",
                    "杂费":"",
                    "杂费币制":"",
                    "项号":"",
                    "特殊关系确认":"",
                    "价格影响确认":"",
                    "与货物有关的特许权使用费支付确认":"",
                    "公式定价确认":"",
                    "暂定价格确认":"",
                    "电子底账数据号":"",
                }
                """
            }
        }
        
        # 获取选定的模板
        template = templates.get(doc_type, templates["其他"])
        # 出口发票、出口箱单、出口证书、入货通知、舱单样本、报关单、出口合同、提运单、分箱明细、申报要素、检验检疫、其他
        # - 数量及单位：标题中有数量及单位，那么数量及单位为数量及单位，如果是分开的两个字段，需要将数量和单位合并到一起。
        # - 数量及单位：单据中单位如果有中文优先使用中文。如果没有中文单位，则需要根据商品信息显示中文名称。如PCS:件/把;SETS:套;Rolls:捆等
        # - 数量及单位：对于包含千分位分隔符的数值（如 29,822,000片），请删除所有千分位的逗号，仅保留连续数字与单位。输出格式示例：29822000片。
        # - 法定第一数量及单位、法定第二数量及单位：只有报关单中的数量及单位有多个值的时候才需要进行解析，其他类型的单据都不需要返回
        # - 法定第一数量及单位：报告单的商品信息中数量及单位如果有三个值，那么第一个值为法定第二数量，第二个值为法定第一数量，第三个值为数量及单位；如果有两个值，那么第一个值为法定第一数量，第二个值为数量及单位；如果只有一个值，那么该值为数量及单位。
        # - 法定第二数量及单位：报告单的商品信息中数量及单位如果有三个值，那么第一个值为法定第二数量，第二个值为法定第一数量，第三个值为数量及单位；如果有两个值，那么第一个值为法定第二数量，第二个值为数量及单位；如果只有一个值，那么该值为数量及单位。
        

        if doc_type == "报关单":
             doc_prompt = """
                8. 商品信息：
                    下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
                    重复的商品信息不要进行合并，严格按照输入文本中的项数输出。
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下：
                    - 品牌类型:
                        如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                        品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                    - 品牌：品牌名称，从OCR文本中提取品牌名称
                    - 出口享惠情况:
                        如果OCR文本中没有明确进口享惠情况，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的进口享惠情况进行返回
                        出口享惠情况:出口货物在最终目的国(地区)不享受优惠关税、出口货物在最终目的国(地区)享受优惠关税、享惠、无享惠、不享惠
                    - 申报要素：
                        在报关单中，申报要素通常对应“规格型号”或“申报要素”字段的全部内容
                        根据海关对进出口商品申报的规定，申报要素包括品牌类型、出口享惠情况、材质、用途、品牌、型号、扩展要素及特殊商品要素等，用于核实商品归类的准确性
                        如果数据中提供了申报要素，则其值应完整呈现所有相关信息，例如："1|0||||||"或1.品名：传感器组件2.用途等
                        在提取申报要素内容时，要完整的提取文本中提供的内容，不能省略。错误示例：无型号返回无，无品牌返回无，应该完整返回无型号、无品牌
                    - 监管方式:例如一般贸易、进料加工、来料加工等
                    - 备案号:备案号/备案证号/备案登记号/手(账)册号/手(账)册编号，字母（B、C、E、H、TW、L）+数字
                    - 成交方式:成交方式与成交代码的对应关系,CIF/DAP/DPU/DDP/CIP:1、CFR/CPT/C&F:2、EXW:7、FCA/FOB/FAS:3.返回成交方式，不要返回成交代码。
                    - 境内发货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                            4. 大部分情况境内人发货人跟生产销售单位一致。
                    - 境外收货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话，不要进行中文翻译
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回单据中的名称,不要进行翻译。
                            4. 境外收货人为企业名称，一定不是城市或者港口，没有企业名称可以返回为空
                            5. To字段是港口或者国家的情况下，应该将这个值给指运港
                    - 生产销售单位：只能生产销售单位这个字段取值
                    - 总价:总价/金额，注意区分单价和总价,总价不可能比单价小。
                    - 单价：如果单价是238.00/1000PCS,那么返回的单价应该是0.238。或者表头中有提到单价数量，那么也需要进行计算
                    - 最终目的国:一定是国家，一定不是中国。跟运抵国一致。
                    - 境内货源地：
                        输入数据：可能包含单独的县名（如"单县"）、省+县（如"山东博兴"）、或区名（如"朝阳区"）。
                        输出要求：
                            如果只有 县名（如"单县"），补全为 "所属地级市+县名"（如"菏泽单县"）。
                            如果已有 省+县（如"山东博兴"），补全为 "省+地级市+县"（如"山东滨州博兴"）。
                            如果只有 区名（如"朝阳区"），默认补全为 "北京+区名"（如"北京朝阳区"），除非上下文明确指向其他城市（如"长春朝阳区"）。
                            如果只有 市名（如"济宁"），直接返回原值
                            不修改原有信息，仅补充缺失的市级名称。
                            对无法确定的情况保留原样。
                            如果信息中包含开发区、保税区、物流中心、加工区，那么不处理，直接返回原值。
                        注意：境内货源地这个字段是属于商品信息的内部字段，不要放到其他位置
                    - 数量及单位：
                        如标题中同时包含“数量”和“单位”，应将其合并为一个字段，格式为“数量+单位”（例如：“100 PCS”应处理为“100件”）。
                        单据中如单位既有中文又有英文，优先使用中文；如单位为英文，优先转换为中文单位，如：
                            PCS → 件 / 把；
                            SETS → 套；
                            ROLLS → 捆；
                            缺乏明确对应时，可结合商品信息判断最适合的中文单位表达。
                            如果有多个单位，默认使用第一个，如个/台，那么返回个
                        若数值中包含千分位分隔符（如 “29,822,000片”），需移除所有逗号，仅保留数字与单位，结果为“29822000片”。
                        数量及单位只有一组数量及单位（只输出一个数量和一个最相关的单位）
                    - 法定第一数量及单位 / 法定第二数量及单位：
                        若商品信息中的数量及单位包含 三个值：
                            第1个为“法定第二数量及单位”；
                            第2个为“法定第一数量及单位”；
                            第3个为“数量及单位”。
                        若包含 两个值：
                            第1个为“法定第一数量及单位”；
                            第2个为“数量及单位”。
                        若仅包含 一个值：
                            该值视为“数量及单位”。
                        字段信息可能会跟净重或者数量及单位重复，但仍然需要按照要求进行提取。
                    - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                    - 商品名称：
                        商品名称字段通常为“品名”或“商品名称”项中提取的内容，请仅提取该字段中明确的商品名称部分。
                        如果商品名称中出现“1.品名；XXX 2.用途；YYY”等格式，请**仅提取1.品名后的内容作为商品名称字段值**，忽略后续用途、品牌、型号等内容。
                        不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称
                        请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中
                    - 规格型号：规格型号、规格、型号，只提取规格型号的内容，不要带商品名称一起返回
                    - 毛重、净重：在提取的时候需要将单位一起输出
                    - 商品信息中的净重和毛重应该是该项商品的总净重和总毛重，而不是单个商品的净重和毛重
                    - 总毛重：合计、毛重、TG.W.、GW、Total。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                    - 总净重：合计、净重、TN.W.、NW、Total。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                    - 总件数：合计、件数、PACKED IN、Total、数量。若数值中包含千分位分隔符（如 “29,822,000”），需移除所有逗号，仅保留数字与单位，结果为“29822000”。
                    - 总毛重、总净重、总件数：
                        在识别表格文件内容时，如果表中没有明确的字段对应，可以查看商品项的最后一行，取合集、ToTal的对应值
                    - 币制：
                        1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                        2.RMB、人民币、¥ 映射为CNY
                        3.美金、$映射为USD
                    - 备案序号：只有当备案号字段存在的时候，备案序号才需要返回。
                    - 许可证号：如果报关单中包含 "许可证号" 或类似表述，则提取其后的编号（如 `25-15-215800`）
                    - 集装箱号：箱号、箱封号、集装箱号
                        集装箱号通常由以下部分组成：4 位承运人代码（字母）+ 7 位数字序号 + 1 位校验码（有时省略或用斜杠分隔）
                        按照集装箱号的规则提取，多余的部分删掉，不要返回
                    - 指运港：
                        输出要求：
                            1. 指运港/目地港/卸货港/交货地/Port of Discharge。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                            2. 不要从其他信息中获取。
                            3. 只显示中文名称，例如纽约
                    - 包装种类：根据文本中提取的内容对应标准的包装种类，只返回中文包装种类。标准包装种类有：散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                    - 运费：只取总的运费，不要取分项的运费。
                    - 保费：只取总的保费，不要取分项的保费。
                    - 杂费：只取总的杂费，不要取分项的杂费。
                    - 备注：提取报关单中的备注完整信息。
                    - 运输工具名称、航次号：
                        需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                        中文名称和英文名称同时存在的话，只提取英文名称
                    - 商品编号：商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码。一般出现在商品信息或者申报要素中
                    - 运输方式：例如水路运输、航空运输等，中英对照关系（航空运输：BY AIR、海运：BY SEA、公路运输：BY TRUCK、铁路运输：BY RAIL、快递：BY COURIER、邮政：BY POST），返回对应的中文运输方式
                    - 集装箱规格：根据提取到的集装箱规格信息，返回标准的海关代码
                        海关代码与集装箱规格之间的对应关系：
                        11：普通2*标准箱（L）；12：冷藏2*标准箱（L）；13：罐式2*标准箱（L）；21：普通标准箱（S）；22：冷藏标准箱（S）；23：罐式标准箱（S）；31：其他标准箱（S）；32：其他2*标准箱（L）
                11. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。如原产国一般为中国
                12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
                13.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
                14.在识别报关单时，如果标题是数量及单位并且有三个值，那么第一个值为法定第二数量，第二个值为法定第一数量，第三个值为数量及单位；如果有两个值，那么第一个值为法定第一数量，第二个值为数量及单位；如果只有一个值，那么该值为数量及单位。
            """
        elif doc_type == "出口发票":
             doc_prompt = """
                8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下：
                    - 品牌类型:
                        如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                        品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                    - 品牌：品牌名称，从OCR文本中提取品牌名称
                    - 出口享惠情况:
                        如果OCR文本中没有明确进口享惠情况，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的进口享惠情况进行返回。
                        出口享惠情况:出口货物在最终目的国(地区)不享受优惠关税、出口货物在最终目的国(地区)享受优惠关税、享惠、无享惠、不享惠
                    - 申报要素:根据海关申报要素的要求设定的进出口申报条件‌，用于核实商品归类是否正确，通常申报要素可能包括品牌类型、出口享惠情况、材质、用途、品牌、型号、扩展要素、特殊商品要素等信息。例如1|0||||||、空。数据中如果给到了申报要素，那么申报要素的值为申报要素全部的信息。
                    - 成交方式:成交方式:成交代码的对应关系,CIF/DAP/DPU/DDP/CIP:1、CFR/CPT/C&F:2、EXW:7、FCA/FOB/FAS:3.返回成交方式，不要返回成交代码。
                    - 境内发货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                            4. 大部分情况境内人发货人跟生产销售单位一致。
                    - 境外收货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话，不要进行中文翻译。
                            2. 在提供的文本中如果有中文名称优先返回中文名称。
                            3. 如果文本中没有中文名称，直接返回单据中的名称,不要进行翻译。
                    - 总价:总价/金额，注意区分单价和总价。总价一定大于等于单价。总价不可能比单价小。
                    - 单价：如果单价是238.00/1000PCS,那么返回的单价应该是0.238。或者表头中有提到单价数量，那么也需要进行计算
                    - 境内货源地：
                        输入数据：可能包含单独的县名（如"单县"）、省+县（如"山东博兴"）、或区名（如"朝阳区"）。
                        输出要求：
                            如果只有 县名（如"单县"），补全为 "所属地级市+县名"（如"菏泽单县"）。
                            如果已有 省+县（如"山东博兴"），补全为 "省+地级市+县"（如"山东滨州博兴"）。
                            如果只有 区名（如"朝阳区"），默认补全为 "北京+区名"（如"北京朝阳区"），除非上下文明确指向其他城市（如"长春朝阳区"）。
                            不修改原有信息，仅补充缺失的市级名称。
                            对无法确定的情况保留原样。
                            如果信息中包含开发区、保税区、物流中心、加工区，那么不处理，直接返回原值。
                    - 数量及单位：
                        标题中有数量及单位，那么数量及单位为数量及单位，如果是分开的两个字段，需要将数量和单位合并到一起。
                        单据中单位如果有中文优先使用中文。如果没有中文单位，则需要根据商品信息显示中文名称。如PCS:件/把;SETS:套;Rolls:捆等
                    - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                    - 商品名称：
                        OCR识别文本中有中文名称和英文名称的时候，优先使用中文名称，没有中文名称的时候，使用英文名称，不要翻译。
                        如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                    - 规格型号：
                        如果规格型号中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。
                        只提取规格型号的内容，绝对不要包含任何商品名称
                    - 币制：
                        1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                        2.RMB、人民币映射为CNY
                        3.美金映射为USD
                    - 指运港：
                        输出要求：
                            1. 指运港/目地港/卸货港/交货地/Port of Discharge。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                            2. 不要从其他信息中获取。
                            3. 只显示中文名称，例如纽约
                    - 包装种类：根据文本中提取的内容对应标准的包装种类，只返回中文包装种类。标准包装种类有：散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                    - 运费：结合上下文，从输入的文本中只返回总的运费，不要重复提取，运费/OCEAN FREIGHT
                    - 保费：只提取总的保费，不要取分项的保费，保费/INSURANCE PREMIUM
                    - 杂费：只提取总的杂费，不要取分项的杂费。
                    - 运输工具名称、航次号：
                        需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                        中文名称和英文名称同时存在的话，只提取英文名称
                        若文本中出现“Shipped per S.S.”，此为运输方式（Shipping Service/Steam Ship缩写），**不是具体运输工具名称或船名**，不要将其提取为运输工具名称或航次号，**应忽略处理**。
                    - 商品编号：商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码。一般出现在商品信息或者申报要素中
                11. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。如原产国一般为中国
                12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
                13.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
            """
        elif doc_type == "出口箱单":
             doc_prompt = """
                8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
                9. 部分字段的参考信息或要求参考如下：
                    - 品牌类型:
                        如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                        品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                    - 品牌：品牌名称，从OCR文本中提取品牌名称
                    - 备案号:备案号/备案证号/备案登记号/手(账)册号/手(账)册编号，字母（B、C、E、H、TW、L）+数字
                    - 境内发货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                            4. 大部分情况境内人发货人跟生产销售单位一致。
                    - 境外收货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话，不要进行中文翻译。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回单据中的名称,不要进行翻译。
                    - 数量及单位：标题中有数量及单位，那么数量及单位为数量及单位，如果是分开的两个字段，需要将数量和单位合并到一起。
                    - 数量及单位：单据中单位如果有中文优先使用中文。如果没有中文单位，则需要根据商品信息显示中文名称。如PCS:件/把;SETS:套;Rolls:捆等
                    - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                    - 商品名称：不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称。如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                    - 商品名称：注意区分多个商品。
                        示例：
                        30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                        200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                    - 规格型号：
                        如果规格型号中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。
                        只提取规格型号的内容，绝对不要包含任何商品名称
                    - 毛重、净重：在提取的时候需要将单位一起输出
                    - 商品信息中的净重和毛重应该是该项商品的总净重和总毛重，而不是单个商品的净重和毛重
                    - 总毛重：毛重、TG.W.、GW。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                    - 总净重：净重、TN.W.、NW。若数值中包含千分位分隔符（如 “29,822,000KG”），需移除所有逗号，仅保留数字与单位，结果为“29822000KG”。
                    - 总件数：件数、PACKED IN。若数值中包含千分位分隔符（如 “29,822,000”），需移除所有逗号，仅保留数字与单位，结果为“29822000”。
                    - 备案序号:只有当备案号字段存在且有值的情况下，备案序号才需要提取，取NO MARK对应的值。
                    - 指运港：
                        输出要求：
                            1. 指运港/目地港/卸货港/交货地/Port of Discharge。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                            2. 不要从其他信息中获取。
                            3. 只显示中文名称，例如纽约
                    - 包装种类：
                        根据文本中提取的内容对应标准的包装种类/包装方式（名称或代码），只返回中文包装种类名称。
                        如果提取的是包装种类代码，那么根据代码返回包装种类名称，如代码99返回其他包装。
                        标准包装种类名称及代码：
                            散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                    - 运输工具名称、航次号：
                        需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                        中文名称和英文名称同时存在的话，只提取英文名称
                    - 商品编号：商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码。一般出现在商品信息或者申报要素中
                11. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。如原产国一般为中国
                12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
                13.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
            """
        elif doc_type == "出口证书":
             doc_prompt = """
                8. 国家和港口，直接使用中文名称
                9. 部分字段的参考信息或要求参考如下：
                    - 备案号:备案号/备案证号/备案登记号/手(账)册号/手(账)册编号，字母（B、C、E、H、TW、L）+数字
                    - 境内发货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                            4. 大部分情况境内人发货人跟生产销售单位一致。
                    - 境外收货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                10. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。
           
            """
        elif doc_type == "入货通知":
             doc_prompt = """
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下：
                    - 集装箱号：箱号、箱封号、集装箱号
                        集装箱号通常由以下部分组成：4 位承运人代码（字母）+ 7 位数字序号 + 1 位校验码（有时省略或用斜杠分隔）
                        按照集装箱号的规则提取，多余的部分删掉，不要返回
                    - 指运港：
                        输出要求：
                            1. 指运港/目地港/卸货港/交货地/Port of Discharge。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                            2. 不要从其他信息中获取。
                            3. 只显示中文名称，例如纽约
                    - 运输工具名称、航次号：
                        需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                        中文名称和英文名称同时存在的话，只提取英文名称
                    - 集装箱规格：根据提取到的集装箱规格信息，返回标准的海关代码
                        海关代码与集装箱规格之间的对应关系：
                        11：普通2*标准箱（L）；12：冷藏2*标准箱（L）；13：罐式2*标准箱（L）；21：普通标准箱（S）；22：冷藏标准箱（S）；23：罐式标准箱（S）；31：其他标准箱（S）；32：其他2*标准箱（L）
                11. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。如原产国一般为中国
            """
        elif doc_type == "舱单样本":
             doc_prompt = """
                
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下：
                    - 境内发货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                            4. 大部分情况境内人发货人跟生产销售单位一致。
                    - 境外收货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                    - 指运港：
                        输出要求：
                            1. 指运港/目地港/卸货港/交货地/Port of Discharge。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                            2. 不要从其他信息中获取。
                            3. 只显示中文名称，例如纽约
                    - 运输工具名称、航次号：
                        需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                        中文名称和英文名称同时存在的话，只提取英文名称
                11. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。如原产国一般为中国
            """
        elif doc_type == "出口合同":
             doc_prompt = """
                8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下：
                    - 成交方式:成交方式:成交代码的对应关系,CIF/DAP/DPU/DDP/CIP:1、CFR/CPT/C&F:2、EXW:7、FCA/FOB/FAS:3.返回成交方式，不要返回成交代码。
                    - 境内发货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                            4. 大部分情况境内人发货人跟生产销售单位一致。
                    - 境外收货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                    - 总价:总价/金额，注意区分单价和总价
                    - 单价：如果单价是238.00/1000PCS,那么返回的单价应该是0.238。或者表头中有提到单价数量，那么也需要进行计算
                    
                    - 数量及单位：标题中有数量及单位，那么数量及单位为数量及单位，如果是分开的两个字段，需要将数量和单位合并到一起。
                    - 数量及单位：单据中单位如果有中文优先使用中文。如果没有中文单位，则需要根据商品信息显示中文名称。如PCS:件/把;SETS:套;Rolls:捆等
                    - 单位字典：001:台;002:座;003:辆;004:艘;005:架;006:套;007:个;008:只;009:头;010:张;011:件;012:支;013:枝;014:根;015:条;016:把;017:块;018:卷;019:副;020:片;021:组;022:份;023:幅;025:双;026:对;027:棵;028:株;029:井;030:米;031:盘;032:平方米;033:立方米;034:筒;035:千克;036:克;037:盆;038:万个;039:具;040:百副;041:百支;042:百把;043:百个;044:百片;045:刀;046:疋;047:公担;048:扇;049:百枝;050:千只;051:千块;052:千盒;053:千枝;054:千个;055:亿支;056:亿个;057:万套;058:千张;059:万张;060:千伏安;061:千瓦;062:千瓦时;063:千升;067:英尺;070:吨;071:长吨;072:短吨;073:司马担;074:司马斤;075:斤;076:磅;077:担;078:英担;079:短担;080:两;081:市担;083:盎司;084:克拉;085:市尺;086:码;088:英寸;089:寸;095:升;096:毫升;097:英加仑;098:美加仑;099:立方英尺;101:立方尺;110:平方码;111:平方英尺;112:平方尺;115:英制马力;116:公制马力;118:令;120:箱;121:批;122:罐;123:桶;124:扎;125:包;126:箩;127:打;128:筐;129:罗;130:匹;131:册;132:本;133:发;134:枚;135:捆;136:袋;139:粒;140:盒;141:合;142:瓶;143:千支;144:万双;145:万粒;146:千粒;147:千米;148:千英尺;149:百万贝可;163:部;164:亿株;170:人份
                    - 商品名称：不要进行英文翻译。OCR识别文本中有中文名称的时候，优先使用中文名称。如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                    - 商品名称：注意区分多个商品。
                        示例：
                        30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                        200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                    - 规格型号：
                        如果规格型号中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。
                        只提取规格型号的内容，绝对不要包含任何商品名称
                    - 币制：
                        1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                        2.RMB、人民币映射为CNY
                        3.美金映射为USD 
                    - 包装种类：根据文本中提取的内容对应标准的包装种类，只返回中文包装种类。标准包装种类有：散装（00）、裸装（01）、球状罐类（04）、包/袋（06）、纸制或纤维板制盒/箱（22）、制或竹藤等植物性材料制盒/箱（23）、其他材料制盒/箱（29）、纸制或纤维板制桶（32）、木制或竹藤等植物性材料制桶（33）、其他材料制桶（39）、中型散装容器（41）、便携式罐体（42）、可移动罐柜（43）、再生木托（92）、天然木托（93）、植物性铺垫材料（98）、其他包装（99）
                12. 在读取商品信息时，根据商品列表的表头信息，确定商品的单价是单个商品还是多个商品，如果是多个商品，需要进行单价计算,不考虑单位。如238.00/1000PCS,那么返回的单价应该是0.238。
             """
        elif doc_type == "提运单":
             doc_prompt = """
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下：
                    - 集装箱号：箱号、箱封号、集装箱号
                        集装箱号通常由以下部分组成：4 位承运人代码（字母）+ 7 位数字序号 + 1 位校验码（有时省略或用斜杠分隔）
                        按照集装箱号的规则提取，多余的部分删掉，不要返回
                    - 指运港：
                        输出要求：
                            1. 指运港/目地港/卸货港/交货地/Port of Discharge。仅当字段标题完全匹配上述关键词时，才提取紧随其后的港口中文名称；
                            2. 不要从其他信息中获取。
                            3. 只显示中文名称，例如纽约
                    - 运输工具名称、航次号：
                        需要进行拆分，运输工具名称和航次号之间一般使用空格或者斜线分割。
                        中文名称和英文名称同时存在的话，只提取英文名称
                    - 集装箱规格：根据提取到的集装箱规格信息，返回标准的海关代码
                        海关代码与集装箱规格之间的对应关系：
                        11：普通2*标准箱（L）；12：冷藏2*标准箱（L）；13：罐式2*标准箱（L）；21：普通标准箱（S）；22：冷藏标准箱（S）；23：罐式标准箱（S）；31：其他标准箱（S）；32：其他2*标准箱（L）
                11. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。如原产国一般为中国
             """
        elif doc_type == "分箱明细":
             doc_prompt = """
                """
        elif doc_type == "申报要素":
             doc_prompt = """
                8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下：
                    - 品牌类型:
                        如果OCR文本中没有明确品牌类型，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的品牌类型进行返回。
                        品牌类型:无品牌、境内自主品牌、境内收购品牌、境外品牌(贴牌生产)、境外品牌(其它)
                    - 品牌：品牌名称，从OCR文本中提取品牌名称
                    - 出口享惠情况:
                        如果OCR文本中没有明确进口享惠情况，那么请返回空。不要根据OCR文本中的描述进行判断，直接根据OCR文本中的进口享惠情况进行返回。
                        出口享惠情况:出口货物在最终目的国(地区)不享受优惠关税、出口货物在最终目的国(地区)享受优惠关税、享惠、无享惠、不享惠
                    - 申报要素:根据海关申报要素的要求设定的进出口申报条件‌，用于核实商品归类是否正确，通常申报要素可能包括品牌类型、出口享惠情况、材质、用途、品牌、型号、扩展要素、特殊商品要素‌等信息。例如1|0||||||、空。数据中如果给到了申报要素，那么申报要素的值为申报要素全部的信息。
                    - 境内货源地：
                        输入数据：可能包含单独的县名（如"单县"）、省+县（如"山东博兴"）、或区名（如"朝阳区"）。
                        输出要求：
                            如果只有 县名（如"单县"），补全为 "所属地级市+县名"（如"菏泽单县"）。
                            如果已有 省+县（如"山东博兴"），补全为 "省+地级市+县"（如"山东滨州博兴"）。
                            如果只有 区名（如"朝阳区"），默认补全为 "北京+区名"（如"北京朝阳区"），除非上下文明确指向其他城市（如"长春朝阳区"）。
                            不修改原有信息，仅补充缺失的市级名称。
                            对无法确定的情况保留原样。
                            如果信息中包含开发区、保税区、物流中心、加工区，那么不处理，直接返回原值。
                            不能根据公司名称或者公司地址进行返回，如果没有可以返回空值。
                    - 商品名称：
                        OCR识别文本中有中文名称和英文名称的时候，优先使用中文名称，没有中文名称的时候，使用英文名称，不要翻译。
                        如果商品名称中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。请注意在提取的时候Total Amount、FREIGHT 是总价、运费，不要提取到商品名称中。
                        出现多个单位值（如15ML、5ML）紧跟商品名时，必须拆解为多个商品，如“15ML 5ML玻璃瓶”应识别为两条商品，商品名称均为“玻璃瓶”，规格型号分别为“15ML”、“5ML”。
                        30ML玻璃瓶，60ML玻璃瓶。那么应该是两个商品，商品名称都是玻璃瓶，规格型号为30ML和60ML。
                        200ML 250ML 300ML  玻璃瓶，那么应该是三个商品，商品名称都是玻璃瓶，规格型号为200ML、250ML、300ML。
                        结合上下文提取完整的商品名称，OCR在识别的文本可能会存在换行、错行的情况。
                    - 规格型号：
                        如果规格型号中包含商品名称和型号，要进行拆分。如500ML 玻璃瓶，拆分后商品名称为玻璃瓶，规格型号为500ML。
                        只提取规格型号的内容，绝对不要包含任何商品名称
                    - 币制：
                        1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                        2.RMB、人民币映射为CNY
                        3.美金映射为USD
                    - 运费：只取总的运费，不要取分项的运费。
                    - 保费：只取总的保费，不要取分项的保费。
                    - 商品编号：商品编码/H.S编码/HS CODE/税号/HS 码/HS 编码，8～10位纯数字代码。一般出现在商品信息或者申报要素中
                11. 处理的单据都是出口业务涉及到的单据，请根据单据类型，按照出口业务逻辑进行处理。如原产国一般为中国
                12.识别到的数据中如果只有县的名称或者是省+县，添加上市的名称。如山东博兴，返回山东滨州博兴；朝阳区，返回北京朝阳区;单县，返回菏泽单县。注意只添加市，不要修改原来的其他信息。
            
          """
        elif doc_type == "检验检疫":
            doc_prompt = """
                8. 下面包含若干条商品信息，我需要你针对全部商品信息进行处理，将全部处理后的结果完整返回，每条商品信息都需要按照示例要求进行格式化。
                9. 国家和港口，直接使用中文名称
                10. 部分字段的参考信息或要求参考如下
                    - 境内发货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译。
                            4. 大部分情况境内人发货人跟生产销售单位一致。
                    - 境外收货人:
                        输出要求：
                            1. 只要企业名称，不要地址电话。
                            2. 在提供的文本中如果有中文名称优先使用中文名称。
                            3. 如果文本中没有中文名称，直接返回英文名称,不要进行翻译
                """
        elif doc_type == "其他":
            doc_prompt = """
                1、部分字段的参考信息或要求参考如下
                    - 币制：
                        1.使用标准币制三字码，如AUD、CAD、CHF、CNY、DKK、EUR、GBP、HKD、IDR、JPY、KRW、MOP、MYR、NOK、NZD、PHP、RUB、SEK、SGD、THB、TWD、USD等
                        2.RMB、人民币映射为CNY
                        3.美金映射为USD
                """
        # ## 强制校验
        # - 在提取和清洗完成所有字段值后，必须对最终输出的JSON对象进行逐字段校验。
        # - 校验内容包括但不限于：字段值中不得含有非法字符（"、“、”、'、/、\\、\n、\r、\t）。
        # - 如果任意字段未通过校验，提示词必须强制重写输出结果，直到所有字段均合规为止。
        return f"""
        # 任务：OCR文本转JSON格式化
        
        ## 说明
        你的任务是将OCR识别的文本按指定JSON结构进行完整格式化，确保所有商品信息全部输出，不省略。不要添加任何解释或附加信息。不得假设或创造不存在的信息。该文档已识别为【{doc_type}】类型。
        ## Skills：
        - 所有字段值在提取后，必须逐项清除以下字符：双引号（"、“、”）、单引号（'）、斜杠（/）、反斜杠（\）、换行符（\n）、回车符（\r）、制表符（\t）。该步骤为**强制执行**。
        - 例如 OCR识别为：“生产件的通用零件编号后加注‘/TY’”，应清洗为：“生产件的通用零件编号后加注TY”
        - 不允许字段值中保留任何以上字符

        ## 注意：即使下方内容较长，也必须严格按以下要求执行：
        1. 严格按照示例模板进行转换，保持相同的字段名和结构
        2. 确保从OCR文本中准确提取所有可找到的信息，没有的数据请直接返回空字符串
        3. 日期格式统一转换为yyyy-mm-dd
        4. 只能提取使用OCR文本中出现的实际数据，不得假设或创造不存在的信息。
        5. 模版里面字段的值为要求或者参考信息，不要直接返回模版里面的字段值
        6. 在识别字段时，需要结合前后文的内容，不要太依赖内容中的标点符号。
        7. 字段后跟随的内容为“占位符”，表示无值，应保留为空，不得从其他字段推断或填补。
        8. 在识别表格内容时，要区分标题行和内容行。注意分析表头和内容的语义以及内容中的空格，表头的标题要跟内容对应上，OCR的文本中会存在信息换行或者空行的情况，请根据实际情况进行处理。
        {doc_prompt}
        ## 示例模板
        {template["example"]}
        
        ## OCR识别文本
        {content}
        
        ## 注意
        - 只返回JSON格式的结果，不要添加任何其他解释
        - 如果某些字段在OCR文本中未找到，则在JSON中忽略该字段
        """

# 报关单校验提示词
def verify_customs_declaration_prompt(content):
    return f"""
    # 任务：报关单校验

    ## 说明
    你的任务是校验报关单是否符合要求。
    ## 字段校验规则
        - 申报地海关：
            1、与运输方式相关联，水陆运输：申报地海关为4301/4258；航空运输：申报地海关为4302/0101/2233，铁路运输：申报地海关为4301，公路运输：申报地海关为4301/4302，超出范围即提醒。
            2、与入境口岸、运输工具名称相对应：如运输工具名称项填写内容为：@+16位转关号，则申报地海关与进境关别必不一致。
            3、与出口许可证关联，商品编码涉及出口许可证是，申报地海关必须为4301.
        - 进/出境关别：
            1. 与舱单口岸一致
            2. 进境关别与入境口岸、运输工具名称相对应（见申报地海关），出境关别与离境口岸相对应：4258/4218对应黄岛；4302对应济南机场海关；0609对应满洲里铁路等。
            3.与运输方式相关联，运输方式为其他运输，进出境关别需要与申报地海关一致。
            4.进/出境关别与入/离境口岸相对应
        - 备案号：
            1.与征免性质对应，如征免性质为：901-913、915-919、417、799、789、408、503、502则此项不为空
            2.与随附单证代码相对应：如代码为a,则此项不为空
        - 进/出口日期：
            1.进口日期：无舱单申报，此项应为申报当天日期,点击申报时必须提醒，进口日期比具体申报日期早14天，此项标红；
            2.出口日期：为空,当出口日期不为空时，系统自动删除出口日期。
        - 运输方式：
            1.与舱单信息一致
            2.与运输工具名称、航次号对应
        - 运输工具名称：
            1.与舱单信息一致；
            2.与运输方式对应：运输方式为空运，则此项为航班号或@+16位转关号；运输方式为海运：此项应为船舶英文名称；运输方式为铁路：车厢号，出口转关提前报关模式该栏为空。
            3. 与入境口岸、申报地海关相对应（见申报地海关）
        - 航次号：
            1.与舱单信息一致
            2.与运输方式对应：空运为空；海运填报船舶航次号；铁路填报班列进境日期；
        - 提运单号：
            1.与舱单信息一致；
            2.空运：主单_分单或主单；海运：提单号；铁路：运单号；无舱单申报为空；转关提前报关为空 
            3.不允许有空格_等特殊字符
        - 监管方式：
            1.备案号、随附单证代码、征免性质、征免方式对应
            2.与备案号对应，见备案号
        - 许可证号：
            1.与商品编码关联，出口报关单：商品编号对应的监管条件有3/4/5时，提示必须填写出口许可证号；进口报关单：商品编号对应的监管条件有1/2时，提示必须填写进口许可证号。（见随附单据栏监管代码表）
        - 启运国(地区)/运抵国 (地区)：
            1. 启运国(地区)：空运与启运港对应：启运港应为启运国城市或相同
            2. 运抵国 (地区) ：与最终目的国对应，必须一致，不一致暂存失败。
        - 成交方式：
            1.与运保杂相对应；
            2.进口报关单成交方式为EXW必须填报运保杂、FOB必填运保费，杂费可填可不填、C&F运费必为空，保费必填，杂费可填可不填、CIF运保必为空，杂费可填可不填。
            3.出口报关单成交方式为EXW必须填报杂费，FOB运保费必须为空，C&F时填写运费，CIF必须填写运保费。杂费可填可不填。
        - 件数：
            1. 与舱单信息一致
            2. 不得填报为零，裸装货物填报为“1”。
        - 包装种类：
            1.如涉及木质包装/植物性铺垫材料需触发检疫项
        - 毛重：
            1.与舱单信息一致
            2.≥表头净重
            3.不足一千克的填报为“1”。
        - 净重：
            1. 与表体净重总和对应：表头净重=表体净重总和，不足一千克的填报为“1”。
        - 入/离境口岸：
            1.与入/出境关别对应（见入/出境关别）
        - 启运港/指运港：
            1. 启运港与启运国对应（见启运国）
            2. 空运指运港与运抵国(地区)对应，指运港应为运抵国(地区)城市或相同
        - 报关单类型：
            1.与监管方式、征免方式对应：征免方式为保函、保金，则此项应为有纸报关
        - 商品编号：
            1.与商品名称对应，通过税则清单表商品编码带出的商品名称与实际录入的商品名称比对，如果出现矛盾项，则进行提醒：例如8482101000调心球轴承，如果商品品名录入传动轴，则提醒错误；再例如：6202129020棉制女式连风帽派克大衣等，如果商品名称录入化纤制***，则提醒错误
            2.与检验检疫填制项对应，见涉检项对应情况；
        - 商品名称：
            1.与商品编码对应（见商品编码）
            2.申报品名与数据库比对一下，如果存在与数据库品名一致，商品编码不一致的进行提醒，同时可以设置弹窗出现数据库列表，可以直接选择更新。
        - 成交数量、成交单位、法定第一、二数量/单位：
            1. 当成交单位=法定第一/第二单位时，成交数量=法定第一/第二数量。如果等式不成立，则提醒
            2. 成交数量与法定第一/第二数量的换算校验：个与千个，克与千克，米与千米，磅、盎司与千克，升与千克（不同产品不同换算关系），吨与千克。1千个=1000个，1千克=1000克，1千米=1000米，1磅=16盎司=0.454千克，润滑油/脂：1千克=1.126L，1吨=1000千克。如果换算后等式不成立，成交数量和法定数量第一/第二需要提醒
            3.与监管方式关联，若监管方式为修理物品、退运货物、无代价抵偿、暂时进出，系统提示校验原来关联报关单净重一致。
            注意：
            表头净重与表体重量之和的关系，表头净重≥表体重量之和（表体各项全需报重量时，表头净重=表体重量之和；表体净重之和小于1的除外）。表体重量之和为各项商品单位为千克、克、吨时的重量之和，克、吨要先换算成千克再求和。
            如果出现不符合以上条件的地方，需提醒  
        - 单价，总价，币制：
            1.单价=总价/成交数量。需注意，如果总价/数量为不能除尽，那校验时，应注意单价为保留小数点后四位的值。如果校验等式不成立，需提醒。 
            2. 表体币制不一致时提醒，不同于其他项的币制应标红。
            3. 单价、总价、重量不能为0。如出现0，此项应标红并弹窗提醒。
            4．表体某项超大金额提醒。出现超大金额，需在此项总价处标红。超大金额设置条件可以自由设定，包含但不仅限于：客户名称，商品名称，型号，hs编码。  
        - 最终目的国(地区)：
            1. 进口报关单，必须为中国
            2.出口报关单，与运抵国一致。
        - 原产国(地区)：
            1. 与协定享惠对应：如果原产国（地区）属于优惠贸易协定国家地区范围，则提醒是否要填制该项内容；
            2.与启运国对应：如果要享受协定享惠，则启运国与原产国必须一致，若不一致需提醒，且不能暂存；
            3.与优惠贸易协定代码对应：录入的原产国（地区）应属于该优惠贸易协定代码项下的成员国或者所属国；
            4.与优惠贸易协定项下原产地对应：两项必须一致；
            注意：
            如果原产国(地区) 原产国（地区）属于优惠贸易协定国家地区范围，且与启运国一致，协定享惠情况未录入的情况下，需单独再次提醒，是否要享惠。
            5.出口原产国(地区)，应为中国（142），若不为中国时提醒再次核对。
        - 协定享惠：
            1.与启运国，原产国，优惠贸易协定代码对应对应，见（原产国（地区））
            2．原产地证明编号校验：若该原产地证明编号已经使用过，那么提醒该证明编号已被占用，不能录入
            3. 原产地证明编号与原产地证明类型对应：如果原产地证明类型为D,即原产地声明，则长度必须为28位，格式：原产地声明序列号仅可使用数字或字母，由经核准出口商注册号（5位）和商业单证的签发日期（8位，4位年＋2位月＋2位日）以及商业单证号码（10位，不足10位的在前加0补充至足位）组成
            4.如果原产国(地区) 不属于优惠贸易协定国家地区范围，则该项下内容必须为空
        - 境内目的地/境内货源地：
            1. 境内目的地与消费使用单位、目的地检疫机关对应，
            2.出口报关单，生产销售单位与境内货源地的对应。对应关系：后者=前者的前五位代码。若不一致时，提醒再次核对。
        - 征免方式：
            1.与监管方式、征免性质对应
        - 随附单证：
            1．与监管方式对应，见监管方式，清单内监管方式，必须填a；
            2.与HS编码对应对应，监管条件代码和名称如下，根据税则商品编码对应的监管条件，当录入商品编码时，应提醒该商品编码对应的监管条件代码及监管条件名称。
        - 关联报关单号：
            1.与监管方式对应，若监管方式为：暂时进出境货物，退运货物，修理物品，无代价抵偿，其他，租赁征税，则提醒该项需录入。
        - 关联备案号：
            1.与监管方式对应，若进口监管方式为无代价抵偿货物，则提醒是否录入该项。
        - 集装箱信息：
            1. 与舱单信息一致。集装箱体信息填报集装箱号（在集装箱箱体上标示的全球唯一编号）、集装箱规格、集装箱商品项号关系（单个集装箱对应的商品项号，半角逗号分隔）、集装箱自重（集装箱箱体自重）。
        - 备注： 
            1. 受外商投资企业委托代理其进口投资设备、物品的进出口企业名称
            2. 监管方式为直接退运（4500）的，填报“直接退运”字样+“<ZT”+“海关审核联系单号或者《海关责令进口货物直接退运通知书》编号”+“>”。
            3. 当监管方式为“暂时进出货物”（代码2600）和“展览品”（代码2700）时，填报要求如下：
            1. 暂时进出货物需填写暂进项目号、复运出境日期，上述内容依次填报，项目间用“/”分隔，前后均不加空格
            2. 复运进境或者复运出境的：需填写暂时进/出境复出/进境，办理过延期的，根据《管理办法》填报《货物暂时进/出境延期办理单》的海关回执编号，如：<ZS海关回执编号>，其中英文为大写字母；无此项目的，无需填报
            4. 货物自境外进入境内特殊监管区（区内物流货物）或者保税仓库（保税仓库货物）的，填报“保税入库”或者“境外入区”字样。
            5. 海关特殊监管区域与境内区外之间采用分送集报方式进出的货物，填报“分送集报”字样。
            6. 与HS编码对应：申报 HS为3821000000、3002300000的，属于下列情况的，填报要求为：属于培养基的，填报“培养基”字样；属于化学试剂的，填报“化学试剂”字样；不含动物源性成分的，填报“不含动物源性”字样；
            7. 监管方式为修理物品（1300）的，填报“修理物品”字样。
            8. 申报HS为3006300000、3504009000、3507909010、3507909090、3822001000、3822009000，不属于“特殊物品”的，填报“非特殊物品”字样。
            9. 与涉检项对应：见涉检项内容，填报“应检商品”字样。
            10. 监管条件（6）：第X项商品为全新。
            11. 压力容器类：430不适用特种设备许可或430不属于锅炉压力容器。
            商品编码包含：8481100090,8481201000,8481202000,8481300000,8481400000,8481802190,8481802990,8481803990,8481804090。
            12. 特种设备类：“429受理批准型式试验样品”。（需提供具体的商品编码供软件校验）
            ---429不适用特种设备许可.430不适用特种设备许可。应在录入时先提醒属于特种设备，需填写证书编号，然后判断产品资质里的是否填写证书编号，如已填写备注无需添加。
            费斯托需求：所有不涉及429\430证书类的涉检报关单均填制：429不适用特种设备许可.430不适用特种设备许可，不再区分压力容器类、特种设备类。
            13. 医疗器械类：没有医疗器械许可证的，备注“非医疗器械”。（需提供具体的商品编码供软件校验）-或612/629提供药监部门证明文件。应在录入时先提醒属于医疗设备，需填写注册证编号，然后判断产品资质里的是否填写证书编号，如已填写备注无需添加。
            14. 品名带有“塑料”“钢”：“非医疗器械”。-如是医疗器械则不应添加。
            15. 品名带有“民用航空器用钢铁制螺栓” “民用航空器用钢铁制垫片”：“民航维修技术手册”。
            16. 监管条件（3）：带有“商安管批”字样。
            17. 以上备注如有多个，中间用“;”字符隔开。
        - 涉检项对应：
            1、与表头包装种类对应，当表头包装种类填为23（木制或竹藤等植物性材料制盒/箱）和33（木制或竹藤等植物性材料制桶）和93（天然木托）时，检查表头“检验检疫受理机关、企业资质、领证机关、口岸检验检疫机关、启运日期、目的地检验检疫机关”是否录入内容，表体第一项“检验检疫名称、货物属性、用途”是否录入内容，如未录入，需提醒；
            2、与商品编码对应，商品编码涉检的【分别满足监管条件（A）、法检类型（L、M）】，检查表头“检验检疫受理机关、企业资质、领证机关、口岸检验检疫机关、启运日期、使用人、目的地检验检疫机关”是否录入，商品编码涉检的【同时满足监管条件（A）和法检类型（L/M）】，检查表头“检验检疫受理机关、企业资质、领证机关、口岸检验检疫机关、启运日期、使用人、目的地检验检疫机关、检验检疫签证申报要素”是否录入；以上两种类型,检查表体涉检项“检验检疫名称、货物属性、用途”是否录入，如未录入，需提醒，哪项未录哪项标红，且商品名称标红。涉L的，货物属性要检查是否选择“3C目录外/免办3C/3C目录内”。
            3.商品编码涉检的【监管条件包含（A）】，备注有：430不适用特种设备许可字样的，检查报关单表体用途是否为“其他”，如不是，表体用途项标红。
            4.进口报关单，涉及到特殊物品的，比如商品编码3821，3822，3002，检查报关单表体货物属性是否选择“A/B/C/D级特殊物品,非特殊物品”；若选择“非特殊物品”的，检查备注栏是否填写“非特殊物品”；产品资质栏是否录入许可证资质“出入境特殊物品卫生检疫审批”。
            5 检验检疫受理机关，与申报地海关对应
            6. 口岸检验检疫机关
            与申报地海关、进境关别相对应：先校验运输工具名称的填写，没有“@”的，口岸检疫机关与进境关别一致；有“@”的，口岸检疫机关与申报地海关一致。
        
    """
"""AI模型提示词管理"""
import json
# 文档分类提示词
CLASSIFY_PROMPT = """
# 任务：文档类型分类

## 说明
你的任务是分析OCR识别的文本内容，并确定文档类型。请仅返回文档类型，不要添加任何解释或附加信息。

## 文档类型
- 出口发票：包含境内发货人、境外收货人、发票号（INVOICE NO.）、运抵国、商品名称、型号、数量、单价、总价、成交方式（例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB）等信息
- 出口箱单：包含境内发货人、境外收货人、数量（QUANTITY:/QTY/ CARTONS/ Pieces/ Package Qty）、净重（NET WEIGHT:/ N.W.）、毛重（GROSS WEIGHT:/ G.W）、件数（NUMBER OF PACKAGE/PKGS/ Total Packages）等信息
- 出口证书：包含境内发货人、境外收货人、产地证书（Reference No.）、备案号（出口许可证号Export licence No./ 出口准许证）、随附单证代码E（物种证明 Permit No.证号）、产品资质203、随附单据文件类别、商品编码（HS CODE/HS/检验检疫编码）、报关口岸（Place of clearance/ Port口岸/出口口岸）、有效截止日期、出口货物名称等信息
- 入货通知：包含提运单号（D/R No./ 提单号/提单号码/订舱号/BOOKING NO）、船名/航次（Vessel/Voyage）、场站（场站/提箱场地/入货地址）等信息
- 舱单样本：包含境内发货人（SHIPPER/EXPORTER）、境外收货人（CONSIGNEE：/Importer(buyer)）、货物描述（DESCRIPTION OF GOODS）、指运港（Port of Loading）等信息
- 报关单：包含境内发货人、境外收货人、出境关别、出口日期、申报日期、备案号、运输方式、运输工具及航次好、提运单号、生产销售单位、监管方式、征免性质、许可证号、合同协议号、贸易国、运抵国、指运港、离境口岸、包装种类、件数、毛重、净重、成交方式、商品编号、商品名称及规格型号、数量及单位、单价/总价/币制等信息
- 出口合同：包含境内发货人、境外收货人、合同号/合同编号、运抵国、商品名称、型号、成交数量、总价、总价大写、单价、成交方式、原产国、品牌等信息
- 其他：无法确定的文档类型

## 输入内容
{text}

## 输出要求
只返回以下选项之一：出口发票、出口箱单、出口证书、入货通知、舱单样本、报关单、出口合同、其他
"""

VERIFY_PROMPT = """
# 任务：业务逻辑校验

## 说明
你的任务是输入的JSON格式的内容中是否存在业务逻辑错误，如果有，请返回错误信息，如果没有，请返回空字符串。不要添加任何解释或附加信息。
#校验规则示例：
    1.单价总价币制要一样
    2.发票总价/单价（要注意单位）=成交数量
    3.箱单，总毛重/总净重/单件重量（要注意单位）=成交数量
    4.单据上是多项的汇总比对：例如发票单项总价与合计总价一致；箱单单项净重/毛重与总的净重/毛重一致；单项/总的毛重>净重
    5.商品描述：发票项数与箱单项数不一致的，需要根据商品描述分别统计汇总后逐项匹配

## 输入内容
{text}
## 输出要求
只返回业务逻辑的错误信息。如果没有，请返回空字符串.
{{
    "error": "错误信息/空字符串"
}}
"""

MERGE_PROMPT = """
# 任务：JSON内容提取合并
## 说明
你的任务是将用户输出的多个JSON中内容分析之后提取按照示例的关键字段和结构进行输出。

## 注意：即使下方内容较长，也必须严格按以下要求执行：
1、请从用户提供的多个JSON中提取输出结构要求的字段值，你需要分析每个JSON的结构，然后提取出示例要求的字段值，然后输出JSON。
2、重要提醒：严格按照用户要求的输出结构进行输出，包括字段名称、结构、层级关系等，不要漏掉任何字段，不要多加字段，不要修改字段名称，不要修改字段层级关系。
示例模版:
    [
        {{
            "境内发货人": "company",
            "境外收货人": "Dong Run Wan Bo Technology Ltd",
            "发票号": "BJ20250318INV3",
            "运抵国": "Altyntau Kazakhstan",
            "成交方式": "CIP qingdao AIRPORT, CHINA",
            "贸易国": "Altyntau Kazakhstan",
            "总数量": "1",
            "净重": "700kg",
            "毛重": "720kg",
            "协定享惠": "",
            "备案号": "",
            "随附单证代码E": "",
            "产品资质203": "",
            "随附单据文件类别": "",
            "商品编码": "",
            "申报地海关": "",
            "有效截止日期": "20250308",
            "提运单号": "",
            "船名/航次": "",
            "场站": "",
            "货物描述": "",
            "指运港": "",
            "出境关别": "",
            "出口日期": "",
            "申报日期": "",
            "运输方式": "",
            "运输工具及航次": "",
            "生产销售单位": "",
            "监管方式": "",
            "征免性质": "",
            "许可证号": "",
            "合同协议号": "",
            "离境口岸": "",
            "包装种类": "",
            "件数": "",
            "运费": "",
            "保费": "",
            "杂费": "",
            "商品信息": [
                {{
                    "商品名称": "Dry pumps",
                    "型号": "PUMP 804X V2 SC",
                    "数量": "1",
                    "单价": "EUR20,000.00",
                    "总价": "EUR20,000.00"
                }},
                {{
                    "商品名称": "Dry pumps",
                    "型号": "PUMP 124H V2 SC",
                    "数量": "1",
                    "单价": "EUR12,000.00",
                    "总价": "EUR12,000.00"
                }}
            ],
            "error": "错误信息/空字符串"
        }}
    ]
数据提取规则：
    1. 合并相同字段，如果字段的值一样，那么只显示一个；如果字段的值不一致，那么进行错误提醒，而不是选择其中一个进行显示
    2. 请进行语义分析，同一个字段名称在不同的JSON中可能有不同的叫法，请进行语义分析，然后进行合并。
    3. 跟踪每个文档的错误信息并汇总
    4. 如有异常文档，在error中列出所有问题
    
请合并以下结果（只需返回JSON）：
{0}
"""

def format_by_type( content, doc_type):
        """第二步：根据文档类型生成特定的格式化提示"""
        # 根据文档类型选择相应的示例模板
        templates = {
            "出口发票": {
                "example": """
                {
                    "境内发货人": "",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "发票号": "BJ20250318INV3",
                    "运抵国": "Altyntau Kazakhstan",
                    "原产国":"ORIGIN/COUNTRY OF ORIGIN/",
                    "合同号":"Contract No:/No/INVOICE NO/合同编号",
                    "品牌":"BRAND",
                    "成交方式": "例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等",
                    "贸易国": "Altyntau Kazakhstan",
                    "总价大写":"",
                    "商品信息": [
                        {"商品名称": "Dry pumps", "型号": "PUMP 804X V2 SC", "数量": "1", "单价": "EUR20,000.00", "总价": "EUR20,000.00"},
                        {"商品名称": "Dry pumps", "型号": "PUMP 124H V2 SC", "数量": "1", "单价": "EUR12,000.00", "总价": "EUR12,000.00"}
                    ]
                }
                """
            },
            "出口箱单": {
                "example": """
                {
                    "境内发货人": "",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "总数量": "1",
                    "净重": "700kg",
                    "毛重": "720kg",
                    "原产国":"ORIGIN/COUNTRY OF ORIGIN/",
                    "合同号":"Contract No:/No/INVOICE NO/合同编号",
                    "品牌":"BRAND",
                    "商品信息": [
                        {"商品名称": "Dry pumps", "规格型号": "PUMP 804X V2 SC", "数量": "1", "单价": "EUR20,000.00", "总价": "EUR20,000.00"},
                        {"商品名称": "Dry pumps", "规格型号": "PUMP 124H V2 SC", "数量": "1", "单价": "EUR12,000.00", "总价": "EUR12,000.00"}
                    ]
                }
                """
            },
            "出口证书": {
                "example": """
                {
                    "境内发货人": "",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "协定享惠": "",
                    "备案号": "",
                    "随附单证代码E": "",
                    "产品资质203": "",
                    "随附单据文件类别": "",
                    "商品编码": "",
                    "申报地海关": "",
                    "有效截止日期": "20250308",
                    "商品名称": "Dry pumps",
                    "运抵国": "Altyntau Kazakhstan",
                }
                """
            },
            "入货通知": {
                "example": """
                {
                    "提运单号": "",
                    "船名/航次": "",
                    "场站": "",
                    "出境关别":""
                }
                """
            },
            "舱单样本": {
                "example": """
                {
                    "境内发货人": "",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "货物描述": "",
                    "指运港": ""
                }
                """
            },
            "出口合同": {
                "example": """
                {
                    "境内发货人": "",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "合同号": "Contract No:/No/INVOICE NO/合同编号",
                    "运抵国": "",
                    "商品名称": "",
                    "规格型号": "",
                    "成交数量": "",
                    "总价": "",
                    "总价大写": "",
                    "单价": "",
                    "成交方式": "例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等",
                    "原产国": "ORIGIN/COUNTRY OF ORIGIN/",
                    "品牌": ""
                }
                """
            },
            "报关单": {
                "example": """
                {
                    "境内发货人": "",
                    "境外收货人": "Sold to: / CONSIGNEE：/ To: Messrs/ Receiver :/ Importer (buyer)/To/",
                    "出境关别": "",
                    "出口日期": "",
                    "申报日期": "",
                    "备案号": "",
                    "运输方式": "例如水路运输、航空运输等",
                    "运输工具及航次": "",
                    "提运单号": "",
                    "生产销售单位": "",
                    "监管方式": "",
                    "征免性质": "",
                    "许可证号": "",
                    "合同协议号": "",
                    "贸易国": "",
                    "运抵国": "",
                    "指运港": "",
                    "离境口岸": "",
                    "包装种类": "",
                    "件数": "",
                    "毛重": "",
                    "净重": "",
                    "成交方式": "例如CIF/CIP/CPT/CFR/DAP/DDP/EXW/FCA/FOB等",
                    "运费":"",
                    "保费":"",
                    "杂费":"",
                    "商品信息": [
                        {"商品编号":"",
                         "商品名称":"",
                         "规格型号":"",
                         "数量及单位":"",
                         "单价/总价/币制":"",
                         "原产国":"",
                         "最终目的国":"",
                         "境内货源地":"",
                         "征免":""
                         }
                    ]
                }
                """
            },
            "其他": {
                "example": """
                {
                    "文件名": "未知文档",
                    "内容概述": "简要描述文档内容",
                    "关键信息": {
                        "日期": "2025-03-18",
                        "相关方": ["公司A", "公司B"],
                        "编号": "如有编号信息"
                    }
                }
                """
            }
        }
        
        # 获取选定的模板
        template = templates.get(doc_type, templates["其他"])
        
        return f"""
        # 任务：OCR文本转JSON格式化
        
        ## 说明
        你的任务是将OCR识别的文本内容转换为结构化JSON格式。不要添加任何解释或附加信息。该文档已识别为【{doc_type}】类型。
        
        ## 注意：即使下方内容较长，也必须严格按以下要求执行：
        1. 严格按照示例模板进行转换，保持相同的字段名和结构
        2. 确保从OCR文本中准确提取所有可找到的信息
        3. 日期格式统一转换为yyyy-mm-dd
        4. 仅使用OCR文本中出现的实际数据，不得假设或创造不存在的信息
        5. 如果商品名称是英文，将商品名称翻译成中文
        6. 境内外发货人不要提取地址信息
        
        ## 示例模板
        {template["example"]}
        
        ## OCR识别文本
        {content}
        
        ## 注意
        - 只返回JSON格式的结果，不要添加任何其他解释
        - 如果某些字段在OCR文本中未找到，则在JSON中忽略该字段
        """


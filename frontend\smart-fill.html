<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>智能商品字段提取</title>
    <link href="/static/js/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/js/all.min.css">
    <script src="/static/js/crypto-js.min.js" defer></script>
    <style>
        :root {
            --primary-color: #4a6baf;
            --secondary-color: #5a6268;
        }
        body {
            background-color: #f5f8fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .smart-card .card-header {
            background: linear-gradient(135deg, var(--primary-color), #2c3e50);
            color: white;
        }
        .smart-card .card-body {
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.95rem;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-magic me-2"></i>智能商品字段提取</h4>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <textarea id="inputText" class="form-control" rows="4" 
                              placeholder="输入商品描述文本，例如：ABS塑料颗粒，型号GH-203，CAS编号123-45-6，用于汽车零部件制造..."></textarea>
                    <textarea id="inputText2" class="form-control" rows="4" 
                              placeholder="0:品牌类型;1:出口享惠情况;2:用途（家用等）;3:材质（合金钢制等）;4:是否成套;5:品牌（中文或外文名称）;6:型号;7:GTIN;8:CAS;9:其他"></textarea>
                    <textarea id="inputText3" class="form-control" rows="4" 
                              placeholder="1111010"></textarea>
                </div>
                <button id="analyzeBtn" class="btn btn-success w-100">
                    <i class="fas fa-wand-magic-sparkles me-2"></i>智能分析
                </button>
                
                <div id="resultArea" class="mt-4" style="display:none;">
                    <div class="row row-cols-1 row-cols-md-1 row-cols-lg-1 g-1">
                        <!-- 动态生成字段卡片 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.getElementById('analyzeBtn').addEventListener('click', async function() {
        const input = document.getElementById('inputText').value.trim();
        const input2 = document.getElementById('inputText2').value.trim();
        const input3 = document.getElementById('inputText3').value.trim();
        if (!input) return;

        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>分析中...';
        const API_KEY = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
        // 获取当前时间戳
        const timestamp = Date.now().toString();
        // 计算签名
        const stringSignTemp = API_KEY + timestamp;
        const hash = CryptoJS.MD5(stringSignTemp);
        const sign = hash.toString(CryptoJS.enc.Hex).toUpperCase();

        // 将时间戳和签名添加到请求头中
        const headers = new Headers();
        headers.append("timestamp", timestamp);
        headers.append("sign", sign);
        try {
            const response = await fetch('/api/smart-fill', {
                method: 'POST',
                headers: {'Content-Type': 'application/json',"timestamp":timestamp,"sign":sign},
                body: JSON.stringify({ text: input ,split_rules: input2,sbysRequired: input3})
            });
            //用途(家用等) 材质(合金钢制) 是否成套：是 品牌(无) 型号：无，品牌类型：0
            const data = await response.json();
            console.log(data);
            if (data.error) throw new Error(data.error);

            updateResultDisplay(data);
            document.getElementById('resultArea').style.display = 'block';
        } catch (e) {
            alert('分析失败: ' + e.message);
        } finally {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-wand-magic-sparkles me-2"></i>智能分析';
        }
    });

    function updateResultDisplay(data) {
        // 动态获取所有字段（排除error字段）
        const fields = Object.keys(data)
            .filter(key => key !== "error")
            .map(key => ({
                id: key,
                name: key.replace(/([A-Z])/g, " $1").replace(/^./, str => str.toUpperCase())
            }));

        const container = document.querySelector('#resultArea .row');
        container.innerHTML = fields.map((field, index) => `
            <div class="col-12 mb-2">
                <div class="card">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <span class="text-muted">${index + 1}.</span>
                        <div class="flex-grow-1 mx-3">
                            <h6 class="mb-0">${field.name}</h6>
                            <div class="text-break">${data[field.id] || '未识别'}</div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    </script>
</body>
</html> 
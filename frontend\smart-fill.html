<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>智能商品字段提取</title>
    <link href="/static/js/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/js/all.min.css">
    <style>
        :root {
            --primary-color: #4a6baf;
            --secondary-color: #5a6268;
        }
        body {
            background-color: #f5f8fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .smart-card .card-header {
            background: linear-gradient(135deg, var(--primary-color), #2c3e50);
            color: white;
        }
        .smart-card .card-body {
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.95rem;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-magic me-2"></i>智能商品字段提取</h4>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <textarea id="inputText" class="form-control" rows="4" 
                              placeholder="输入商品描述文本，例如：ABS塑料颗粒，型号GH-203，CAS编号123-45-6，用于汽车零部件制造..."></textarea>
                </div>
                <button id="analyzeBtn" class="btn btn-success w-100">
                    <i class="fas fa-wand-magic-sparkles me-2"></i>智能分析
                </button>
                
                <div id="resultArea" class="mt-4" style="display:none;">
                    <div class="row row-cols-1 row-cols-md-1 row-cols-lg-1 g-1">
                        <!-- 动态生成字段卡片 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.getElementById('analyzeBtn').addEventListener('click', async function() {
        const input = document.getElementById('inputText').value.trim();
        if (!input) return;

        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>分析中...';

        try {
            const response = await fetch('/api/smart-fill', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({ text: input })
            });
            
            const data = await response.json();
            if (data.error) throw new Error(data.error);

            updateResultDisplay(data);
            document.getElementById('resultArea').style.display = 'block';
        } catch (e) {
            alert('分析失败: ' + e.message);
        } finally {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-wand-magic-sparkles me-2"></i>智能分析';
        }
    });

    function updateResultDisplay(data) {
        const fields = [
            {id: '品牌类型', name: '品牌类型'},
            {id: '出口享惠情况', name: '出口享惠情况'},
            {id: '用途', name: '用途'},
            {id: '材质', name: '材质（塑料品种）'},
            {id: '品牌', name: '品牌名称'},
            {id: '型号', name: '产品型号'},
            {id: 'GTIN', name: 'GTIN'},
            {id: 'CAS', name: 'CAS编号'},
            {id: '其他', name: '其他信息'}
        ];

        const container = document.querySelector('#resultArea .row');
        container.innerHTML = fields.map((field,index) => `
            <div class="">
                    <div class="card-body">${index}、${field.name}:${data[field.id] || ''}</div>
            </div>
        `).join('');
    }
    </script>
</body>
</html> 
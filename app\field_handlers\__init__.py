import asyncio
# 用于字段处理
from .shipping import *
from .port import *
from .region import *
from .country import *
from .customs import *
from .freeNature import *
from .supervisionMode import *  
from .packsKinds import *
from ..api_client import *
from ..content_processor import *
from .unit import *
from .station import *
from .shipping_agent import *
from .exemptionmethod import *
from .transmode import *
from .customsPort import *
from decimal import Decimal, ROUND_HALF_UP

content_processor = ContentProcessor(None)

def public_field_handler(merged,ie_flag,opt_unit_name):
    # 如果前端传过来了境内收发货人，那么优先使用前端传过来的
    if opt_unit_name:
        if ie_flag == "E":
            merged["content"]["境内发货人"] = opt_unit_name
        if ie_flag == "I":
            merged["content"]["境内收货人"] = opt_unit_name
    #处理生产销售单位和消费使用单位
    if ie_flag == "E" and "生产销售单位" not in merged["content"]:
        merged["content"]["生产销售单位"] = merged["content"]["境内发货人"]
    if ie_flag == "I" and "消费使用单位" not in merged["content"]:
        merged["content"]["消费使用单位"] = merged["content"]["境内收货人"]

    #0428 对申报要素进行处理
    product_info = merged["content"].get("商品信息",[])
    declare_info = merged["content"].get("申报要素",[])
    doc_type = merged["content"].get("单据类型","")
    product_list = merged["content"].get("商品列表",[])
    product_name_mapping = {
        item["商品编号"]: item["商品名称"] 
        for item in product_list 
        if isinstance(item, dict) and "商品编号" in item and "商品名称" in item
    }
    if len(product_info) == len(product_name_mapping) and len(product_info) == 1:
        product_info_item = product_info[0] if product_info else {}
        mapping_code, mapping_name = next(iter(product_name_mapping.items()), ("", "") if not product_name_mapping else next(iter(product_name_mapping.items())))
        if isinstance(product_info_item, dict):
            # 如果商品信息中没有商品编号，但映射表中有
            if "商品编号" not in product_info_item and mapping_code:
                # 将映射表中的商品编号添加到商品信息中
                product_info_item["商品编号"] = mapping_code
                # 同时也可以考虑添加商品名称
                if "商品名称" not in product_info_item:
                    product_info_item["商品名称"] = mapping_name
             # 额外检查：如果商品信息中有商品编号但为空，也可以考虑用映射表的编码替换
            elif product_info_item.get("商品编号") in (None, "") and mapping_code:
                product_info_item["商品编号"] = mapping_code                              

    # if len(product_info) == len(declare_info):
    for i in range(min(len(product_info),len(declare_info))):
        
        if "申报要素" in declare_info[i] and declare_info[i]["申报要素"] :
            merged["content"]["商品信息"][i]["申报要素"] = merged["content"]["申报要素"][i]["申报要素"]
        if doc_type!="报关单":
            if "商品名称" in declare_info[i] and declare_info[i]["商品名称"] :
                merged["content"]["商品信息"][i]["商品名称"] = merged["content"]["申报要素"][i]["商品名称"]
        if "商品编号" in declare_info[i] and declare_info[i]["商品编号"] :
            merged["content"]["商品信息"][i]["商品编号"] = merged["content"]["申报要素"][i]["商品编号"]
        if "规格型号" in declare_info[i] and declare_info[i]["规格型号"] :
            merged["content"]["商品信息"][i]["规格型号"] = merged["content"]["申报要素"][i]["规格型号"]
        if "境内货源地" in declare_info[i] and declare_info[i]["境内货源地"] :
            merged["content"]["商品信息"][i]["境内货源地"] = merged["content"]["申报要素"][i]["境内货源地"]
        if "境内目的地" in declare_info[i] and declare_info[i]["境内目的地"] :
            merged["content"]["商品信息"][i]["境内目的地"] = merged["content"]["申报要素"][i]["境内目的地"]
        if "品牌" not in product_info[i] or not product_info[i]["品牌"]:
            if "品牌" in declare_info[i] and declare_info[i]["品牌"] :
                merged["content"]["商品信息"][i]["品牌"] = merged["content"]["申报要素"][i]["品牌"]
        if "品牌类型" not in product_info[i] or not product_info[i]["品牌类型"]:
            if "品牌类型" in declare_info[i] and declare_info[i]["品牌类型"] :
                merged["content"]["商品信息"][i]["品牌类型"] = merged["content"]["申报要素"][i]["品牌类型"]
        if "出口享惠情况" not in product_info[i] or not product_info[i]["出口享惠情况"]:
            if "出口享惠情况" in declare_info[i] and declare_info[i]["出口享惠情况"] :
                merged["content"]["商品信息"][i]["出口享惠情况"] = merged["content"]["申报要素"][i]["出口享惠情况"]
        if "境内货源地" not in product_info[i] or not product_info[i]["境内货源地"]:
            if "境内货源地" in declare_info[i] and declare_info[i]["境内货源地"] :
                merged["content"]["商品信息"][i]["境内货源地"] = merged["content"]["申报要素"][i]["境内货源地"]
        if "境内目的地" not in product_info[i] or not product_info[i]["境内目的地"]:
            if "境内目的地" in declare_info[i] and declare_info[i]["境内目的地"] :
                merged["content"]["商品信息"][i]["境内目的地"] = merged["content"]["申报要素"][i]["境内目的地"]
        if "商品编号" not in product_info[i] or not product_info[i]["商品编号"]:
            if "商品编号" in declare_info[i] and declare_info[i]["商品编号"] :
                merged["content"]["商品信息"][i]["商品编号"] = merged["content"]["申报要素"][i]["商品编号"]
        if "商品名称" not in product_info[i] or not product_info[i]["商品名称"]:
            if "商品名称" in declare_info[i] and declare_info[i]["商品名称"] :
                merged["content"]["商品信息"][i]["商品名称"] = merged["content"]["申报要素"][i]["商品名称"]
        if "规格型号" not in product_info[i] or not product_info[i]["规格型号"]:
            if "规格型号" in declare_info[i] and declare_info[i]["规格型号"] :
                merged["content"]["商品信息"][i]["规格型号"] = merged["content"]["申报要素"][i]["规格型号"]
        if "最终目的国" not in product_info[i] or not product_info[i]["最终目的国"]:
            if "运抵国" in merged["content"] and merged["content"]["运抵国"]:
                merged["content"]["商品信息"][i]["最终目的国"] = merged["content"]["运抵国"]
    for product in product_info:
        # 处理境内货源地
        if "境内货源地" in product:
            region_code,region_name = region_handler(product["境内货源地"])
            if region_code:
                product["境内货源地代码"] = region_code
            if region_name:
                product["境内货源地"] = region_name
        if "境内目的地" in product:
            region_code,region_name = region_handler(product["境内目的地"])
            if region_code:
                product["境内目的地代码"] = region_code
            if region_name:
                product["境内目的地"] = region_name
        # 处理最终目的国
        if "最终目的国" in product and product["最终目的国"]:
            country = country_handler(product["最终目的国"])
            if country:
                if country[0]:
                    product["最终目的国代码"] = country[0]
                if country[1]:
                    product["最终目的国"] = country[1]
        # 如果运输方式为保税港区/保税仓库/保税区,最终目的国:中国
        if "运输方式" in merged["content"] and merged["content"]["运输方式"]:
            if merged["content"]["运输方式"] in ["保税港区","保税仓库","保税区"]:
                product["最终目的国"] = "中国"
                product["最终目的国代码"] = "CHN"
        # 0530 增加品牌字段处理，如果没有品牌字段或品牌字段为空，那么默认为无品牌
        if "品牌" not in product or not product["品牌"] or product["品牌"] == "无":
            product["品牌"] = "无品牌"
        else:
            #判断品牌的最后一个字是否是“牌”字，如果不是，添加上
            if not product["品牌"].endswith("牌"):
                product["品牌"] += "牌"
        # 处理原产国
        if "原产国" in product and product["原产国"]:
            country = country_handler(product["原产国"])
            if country:
                if country[0]:
                    product["原产国代码"] = country[0]
                if country[1]:
                    product["原产国"] = country[1]
        else:
            if ie_flag == "E":
                # 如果原产国不存在，那么默认为中国
                product["原产国"] = "中国"
                product["原产国代码"] = "CHN"
        if ie_flag == "E":
            if "原产国代码" not in product or not product["原产国代码"]:
                product["原产国"] = "中国"
                product["原产国代码"] = "CHN"
        # 处理征免方式
        if "征免方式" in product and product["征免方式"]:
            exemption = exemption_method_handler(product["征免方式"])
            if exemption:
                if exemption[0]:
                    product["征免方式代码"] = exemption[0]
                if exemption[1]:
                    product["征免方式"] = exemption[1]
        # 0707 增加按照商品编号找检验检疫单中的商品名称
        if "商品编号" in product and product["商品编号"]:
            product_code = product["商品编号"]
            if product_code in product_name_mapping:
                product["商品名称"] = product_name_mapping[product_code]
            
    # 0603 处理指运港
    if "指运港" in merged["content"] and merged["content"]["指运港"]:
        port_code, port_name = port_handler(merged["content"]["指运港"])
        if port_code is not None:
            merged["content"]["指运港代码"] = port_code.upper()
        merged["content"]["指运港"] = port_name
    if "经停港" in merged["content"] and merged["content"]["经停港"]:
        port_code, port_name = port_handler(merged["content"]["经停港"])
        merged["content"]["经停港代码"] = port_code
        merged["content"]["经停港"] = port_name
    if "启运港" in merged["content"] and merged["content"]["启运港"]:
        port_code, port_name = port_handler(merged["content"]["启运港"])
        merged["content"]["启运港代码"] = port_code
        merged["content"]["启运港"] = port_name
    # 0605 处理运输方式、国别
    if "运输方式" in merged["content"] and merged["content"]["运输方式"]:
        shipping = shipping_handler(merged["content"]["运输方式"])
        if shipping:
            if shipping[0]:
                merged["content"]["运输方式代码"] = shipping[0]
            if shipping[1]:
                merged["content"]["运输方式"] = shipping[1]
    else:
        # 如果不存在运输方式，但存在运输工具或航次号，那么运输方式默认为海运
        if "运输工具名称" in merged["content"] or "航次号" in merged["content"]:
             merged["content"]["运输方式"] = "水路运输"
             merged["content"]["运输方式代码"] = "2"
    # 如果运输方式是空运，那么需要处理提运单号，- 替换 空； /替换 _ 
    if "运输方式代码" in merged["content"] and merged["content"]["运输方式代码"] and merged["content"]["运输方式代码"] == "5":
        if "提运单号" in merged["content"] and merged["content"]["提运单号"]:
            merged["content"]["提运单号"] = merged["content"]["提运单号"].replace("-","").replace("/","_")
    if "运抵国" in merged["content"] and merged["content"]["运抵国"]:
        country = country_handler(merged["content"]["运抵国"])
        if country:
            if country[0]:
                merged["content"]["运抵国代码"] = country[0]
            if country[1]:
                merged["content"]["运抵国"] = country[1]
    if "贸易国" in merged["content"] and merged["content"]["贸易国"]:
        country = country_handler(merged["content"]["贸易国"])
        if country:
            if country[0]:
                merged["content"]["贸易国代码"] = country[0]
            if country[1]:
                merged["content"]["贸易国"] = country[1]
    else:
        if "运抵国" in merged["content"] and merged["content"]["运抵国"]:
            merged["content"]["贸易国"] = merged["content"]["运抵国"]
        if "运抵国代码" in merged["content"] and merged["content"]["运抵国代码"]:
            merged["content"]["贸易国代码"] = merged["content"]["运抵国代码"]   
    # 获取指运港代码，如果没有则尝试用贸易国代码或运抵国代码替代
    if not merged["content"].get("指运港代码"):
        # 使用贸易国代码作为第一选择
        if merged["content"].get("贸易国代码"):
            merged["content"]["指运港"] = merged["content"]["贸易国"]
            merged["content"]["指运港代码"] = merged["content"]["贸易国代码"]+"000"
        # 如果没有贸易国代码，使用运抵国代码作为第二选择
        elif merged["content"].get("运抵国代码"):
            merged["content"]["指运港"] = merged["content"]["运抵国"]
            merged["content"]["指运港代码"] = merged["content"]["运抵国代码"]+"000"
    if "启运国" in merged["content"] and merged["content"]["启运国"]:
        country = country_handler(merged["content"]["启运国"])
        if country:
            if country[0]:
                merged["content"]["启运国代码"] = country[0]
            if country[1]:
                merged["content"]["启运国"] = country[1]
    if "出境关别" in merged["content"] and merged["content"]["出境关别"]:
        customs_code,customs_name = customs_handler(merged["content"]["出境关别"])
        if customs_code:
            merged["content"]["出境关别代码"] = customs_code
        if customs_name:
            merged["content"]["出境关别"] = customs_name
    if "入境关别" in merged["content"] and merged["content"]["入境关别"]:
        customs_code,customs_name = customs_handler(merged["content"]["入境关别"])
        if customs_code:
            merged["content"]["入境关别代码"] = customs_code
        if customs_name:
            merged["content"]["入境关别"] = customs_name
    if "申报地海关" in merged["content"] and merged["content"]["申报地海关"]:
        customs_code,customs_name = customs_handler(merged["content"]["申报地海关"])
        if customs_code:
            merged["content"]["申报地海关代码"] = customs_code
        if customs_name:
            merged["content"]["申报地海关"] = customs_name
    if "离境口岸" in merged["content"] and merged["content"]["离境口岸"]:
        customs_port_code,customs_port_name = customs_port_handler(merged["content"]["离境口岸"])
        if customs_port_code:
            merged["content"]["离境口岸代码"] = customs_port_code
        if customs_port_name:
            merged["content"]["离境口岸"] = customs_port_name
    if "入境口岸" in merged["content"] and merged["content"]["入境口岸"]:
        customs_port_code,customs_port_name = customs_port_handler(merged["content"]["入境口岸"])
        if customs_port_code:
            merged["content"]["入境口岸代码"] = customs_code
        if customs_port_name:
            merged["content"]["入境口岸"] = customs_port_name

    # 处理总件数，提取纯数字
    if "总件数" in merged["content"] and merged["content"]["总件数"]:
        match = re.search(r'\d+', merged["content"]["总件数"])  # 只匹配第一个整数
        if match:
            merged["content"]["总件数"] = match.group()
    # 处理总净重，提取纯数字
    if "总净重" in merged["content"] and merged["content"]["总净重"]:
        match = re.search(r'\d+\.?\d*', merged["content"]["总净重"])  # 只匹配第一个整数
        if match:
            merged["content"]["总净重"] = match.group()
    # 处理总毛重，提取纯数字
    if "总毛重" in merged["content"] and merged["content"]["总毛重"]:
        match = re.search(r'\d+\.?\d*', merged["content"]["总毛重"])  # 只匹配第一个整数
        if match:
            merged["content"]["总毛重"] = match.group()
    # 处理征免性质
    if "征免性质" in merged["content"] and merged["content"]["征免性质"]:
        code,nature = free_nature_handler(merged["content"]["征免性质"])
        if code:
            merged["content"]["征免性质代码"] = code
        if nature:
            merged["content"]["征免性质"] = nature
    # 处理监管方式
    if "监管方式" in merged["content"] and merged["content"]["监管方式"]:
        code,mode = supervision_mode_handler(merged["content"]["监管方式"])
        if code:
            merged["content"]["监管方式代码"] = code
        if mode:
            merged["content"]["监管方式"] = mode
    else: 
        # 如果监管方式不存在，则根据备案号判断
        # 如果备案号存在，并且首字母是C，那么监管方式为进料对口
        if "备案号" in merged["content"] and merged["content"]["备案号"]:
            if merged["content"]["备案号"][0] == "C":
                merged["content"]["监管方式"] = "进料对口"
                merged["content"]["监管方式代码"] = "0615"
        else:
            # 备案号为空，一般贸易
            merged["content"]["监管方式"] = "一般贸易"
            merged["content"]["监管方式代码"] = "0110"
    # 处理包装种类
    if "包装种类" in merged["content"] and merged["content"]["包装种类"]:
        code,name = packs_kinds_handler(merged["content"]["包装种类"])
        if code:
            merged["content"]["包装种类代码"] = code
        if name:
            merged["content"]["包装种类"] = name
    else:
        # 如果包装种类不存在，默认纸制或纤维板制盒/箱
        merged["content"]["包装种类"] = "纸制或纤维板制盒/箱"
        merged["content"]["包装种类代码"] = "22"
    # 处理成交方式
    if "成交方式" in merged["content"] and merged["content"]["成交方式"]:
        code,transMode = trans_mode_handler(merged["content"]["成交方式"])
        if code:
            merged["content"]["成交方式代码"] = code
        if transMode:
            merged["content"]["成交方式"] = transMode
    # 处理运输工具名称和航次号
    if "运输工具名称" in merged["content"] and merged["content"]["运输工具名称"]:
        merged["content"]["运输工具名称"] = merged["content"]["运输工具名称"].replace("MV.","")
    if "航次号" in merged["content"] and merged["content"]["航次号"]:
        merged["content"]["航次号"] = merged["content"]["航次号"].replace("V.","")

    # 处理运费、保费、杂费
    if "运费" not in merged["content"] and "运费币制" in merged["content"]:
        del merged["content"]["运费币制"]
    if "保费" not in merged["content"] and "保费币制" in merged["content"]:
        del merged["content"]["保费币制"]
    if "杂费" not in merged["content"] and "杂费币制" in merged["content"]:
        del merged["content"]["杂费币制"]
    return merged


def is_pure_number(s):
    return bool(re.fullmatch(r'^[+-]?\d*\.?\d+$', s))  # 匹配整数/小数
def is_not_blank(value):
    """检查字符串是否非空"""
    return value is not None and value.strip() != ""
def extract_number_and_currency(text):
    # 使用正则表达式提取字母和数字部分
    match = re.match(r"([A-Za-z]+)(\d+)|(\d+)([A-Za-z]+)", text)

    if match:
        # 检查哪个组匹配
        if match.group(1):  # 字母在前
            currency = match.group(1)
            number = match.group(2)
        else:  # 数字在前
            currency = match.group(4)
            number = match.group(3)
        
        return currency, number
    else:
        return None, None  # 如果没有匹配，返回None

def handle_unit_conversion(dcl_unitcd: str, legal_unit: str, dcl_qty: Decimal) -> Decimal:
    dcl_qty = dcl_qty if dcl_qty is not None else Decimal('0')
    lega_qty = None
    
    if dcl_unitcd == "007" and legal_unit == "054":  # 个→千个
        lega_qty = dcl_qty * Decimal('0.001')
    elif dcl_unitcd == "054" and legal_unit == "007":  # 千个→个
        lega_qty = dcl_qty * Decimal('1000')
    elif dcl_unitcd == "007" and legal_unit == "044":  # 个→百片
        lega_qty = dcl_qty * Decimal('0.01')
    elif dcl_unitcd == "044" and legal_unit == "007":  # 百片→个
        lega_qty = dcl_qty * Decimal('100')
    elif dcl_unitcd == "011" and legal_unit == "054":  # 件→千个
        lega_qty = dcl_qty * Decimal('0.001')
    elif dcl_unitcd == "054" and legal_unit == "011":  # 千个→件
        lega_qty = dcl_qty * Decimal('1000')
    elif dcl_unitcd == "035" and legal_unit == "036":  # 千克→克
        lega_qty = dcl_qty * Decimal('1000')
    elif dcl_unitcd == "036" and legal_unit == "035":  # 克→千克
        lega_qty = dcl_qty * Decimal('0.001')
    elif dcl_unitcd == "070" and legal_unit == "035":  # 吨→千克
        lega_qty = dcl_qty * Decimal('1000')
    elif dcl_unitcd == "035" and legal_unit == "070":  # 千克→吨
        lega_qty = dcl_qty * Decimal('0.001')
    elif dcl_unitcd == "147" and legal_unit == "030":  # 千米→米
        lega_qty = dcl_qty * Decimal('1000')
    elif dcl_unitcd == "030" and legal_unit == "147":  # 米→千米
        lega_qty = dcl_qty * Decimal('0.001')
    elif dcl_unitcd == "076" and legal_unit == "083":  # 磅→盎司
        lega_qty = dcl_qty * Decimal('16')
    elif dcl_unitcd == "083" and legal_unit == "076":  # 盎司→磅
        lega_qty = (dcl_qty * Decimal('0.0625')).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    elif dcl_unitcd == "076" and legal_unit == "035":  # 磅→千克
        lega_qty = (dcl_qty * Decimal('0.454')).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    elif dcl_unitcd == "035" and legal_unit == "076":  # 千克→磅
        lega_qty = (dcl_qty * Decimal('2.20462')).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    elif dcl_unitcd == "083" and legal_unit == "035":  # 盎司→千克
        lega_qty = (dcl_qty * Decimal('0.0283495')).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    elif dcl_unitcd == "035" and legal_unit == "083":  # 千克→盎司
        lega_qty = (dcl_qty * Decimal('35.274')).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    elif dcl_unitcd == "095" and legal_unit == "035":  # 升→千克
        lega_qty = (dcl_qty * Decimal('0.888')).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    elif dcl_unitcd == "035" and legal_unit == "095":  # 千克→升
        lega_qty = (dcl_qty * Decimal('1.126')).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    elif dcl_unitcd == "095" and legal_unit == "096":  # 升→毫升
        lega_qty = dcl_qty * Decimal('1000')
    elif dcl_unitcd == "096" and legal_unit == "095":  # 毫升→升
        lega_qty = dcl_qty * Decimal('0.001')
    
    if lega_qty:
        return str(lega_qty)
    return lega_qty
def build_text(sbysu, hsmodel, pplx, ckxhqk, pp):
    """构建文本"""
    text = ""
    if is_not_blank(sbysu):
        text += "申报要素：" + sbysu + "。"
    if is_not_blank(hsmodel) and sbysu != hsmodel:
        text += "规格型号：" + hsmodel
    if is_not_blank(pplx):
        text += "。品牌类型：" + pplx
    if is_not_blank(ckxhqk):
        text += "。出口享惠情况：" + ckxhqk
    if is_not_blank(pp):
        text += "。品牌：" + pp
    return text
async def handle_product(product):
    print("⚠️ 处理商品信息：",product)
    qtyAndUnit = None
    nwAndUnit = None
    # 处理数量及单位
    if "数量及单位" in product and product["数量及单位"]:
        qtyAndUnit =  "".join(product["数量及单位"].split())
        if is_pure_number(qtyAndUnit):
            product["成交数量"] = qtyAndUnit
            product["成交计量单位"] = "件"
            product["成交计量单位代码"] = "011"
        elif "/" in qtyAndUnit:
            parts = qtyAndUnit.split("/")
            if len(parts) == 2: # 如果数量及单位中包含/,进行拆分，第一部分为成交数量，第二部分为法一
                match = re.match(r'(\d+\.?\d*)(.*)',  parts[0])
                if match:
                    product["成交数量"] = match.group(1)
                    product["成交计量单位"] = match.group(2)
                    product["成交计量单位代码"] = unit_handler(match.group(2))[0]
                if "法定第一数量及单位" not in product or not product["法定第一数量及单位"]:
                    product["法定第一数量及单位"] = parts[1]
            if len(parts) == 3: # 如果数量及单位中包含/,进行拆分，第一部分为成交数量，第二部分为法一，第三部分为法二
                match = re.match(r'(\d+\.?\d*)(.*)',  parts[0])
                if match:
                    product["成交数量"] = match.group(1)
                    product["成交计量单位"] = match.group(2)
                    product["成交计量单位代码"] = unit_handler(match.group(2))[0]
                if "法定第一数量及单位" not in product or not product["法定第一数量及单位"]:
                    product["法定第一数量及单位"] = parts[1]
                if "法定第二数量及单位" not in product or not product["法定第二数量及单位"]:
                    product["法定第二数量及单位"] = parts[2]
        else:
            # 如果数量及单位不是纯数字，那么需要拆分,数据部分为数量，其余为单位
            # 使用正则表达式匹配数字和非数字部分
            match = re.match(r'(\d+\.?\d*)(.*)', qtyAndUnit)
            if match:
                product["成交数量"] = match.group(1)
                product["成交计量单位"] = match.group(2)
                product["成交计量单位代码"] = unit_handler(match.group(2))[0]
            else:
                qtyAndUnit = None
    # 处理总价
    if "总价" in product and product["总价"]:
        total = "".join(product["总价"].split())
        match = re.match(r'\d+\.?\d*',  total)
        if match:
            product["总价"] = match.group()
    # 处理单价
    # 单价的处理逻辑为总价/数量。保留四位小数
    if "总价" in product and product["总价"] and "数量" in product and product["数量"]:
        price = Decimal(product["总价"]) / Decimal(product["数量"])
        product["单价"] = price.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    # 处理净重及单位
    if "净重" in product and product["净重"]:
        nwAndUnit = "".join(product["净重"].split())
        match = re.search(r'\d+\.?\d*', nwAndUnit) 
        if match:
            product["净重"] = match.group()
    # 处理毛重及单位
    if "毛重" in product and product["毛重"]:
        mwAndUnit = "".join(product["毛重"].split())
        match = re.search(r'\d+\.?\d*', mwAndUnit)
        if match:
            product["毛重"] = match.group()

    if "商品编号" in product and product["商品编号"]:
        print("商品编号：",product["商品编号"])
        product_code = product["商品编号"].replace(".", "").replace(" ", "")
        # 如果不足10位，用0补齐
        if len(product_code) < 10:
            product_code = product_code.ljust(10, '0')
         # 如果大于10位，那么取前10位
        if len(product_code) > 10:
            product_code = product_code[:10]
        product["商品编号"] = product_code
        hs_result = get_hs_code(product_code)
        print("接口调用的返回值",hs_result)
        if hs_result:
            if hs_result["code"] == 200 and hs_result["data"]:
                print("接口调用的返回值",hs_result)
                hs_data = hs_result["data"]
                # 必须是有效的税则才进行后续逻辑的处理
                if "isEnable" in hs_data and hs_data["isEnable"] == 1:
                    print("接口调用的返回值",hs_data)
                    product["商品编号"] = hs_data["hscode"]
                    # 处理申报要素
                    text = build_text(
                        product.get("申报要素", ""),  # 使用get方法提供默认值
                        product.get("规格型号", ""),
                        product.get("品牌类型", ""),
                        product.get("出口享惠情况", ""),
                        product.get("品牌", "")
                    )
                    if text :
                        split_rules = hs_data["sbys"]
                        sbys_required = hs_data["sbys_required"]
                        print("申报要素输入信息：",text,"\n",split_rules,"\n",sbys_required)
                        sbys = await content_processor.split_product_fields(text, split_rules,sbys_required)
                        print("申报要素输出信息：",sbys)
                        sbys_arrary = [sbys.get("result", ""),sbys.get("split_result", {})]
                        product.pop("申报要素", None)
                        product["申报要素"] = sbys_arrary
                        product["GModel"] = sbys.get("result", "")

                        #0707 增加原始值和申报要素集合
                        product["申报要素原始信息"] = text
                        product["申报要素集合"] = sbys_arrary
                    # 处理法一
                    # 1、如果法一存在，那么判断模型获取到的法一单位跟税则的单位是否一致，不一致需要进行换算，换算后还不一致，下一步
                    # 2、判断净重单位是否跟税则的单位一致，不一致进行换算，换算后还不一致，下一步
                    # 3、判断成交数量单位是否跟税则的单位一致，不一致进行换算，换算后还不一致，下一步
                    # 4、默认跟成交数量及单位一样
                    if "unit1" in hs_data and hs_data["unit1"]:
                        unit1 = hs_data["unit1"] # 税则中的法一单位代码
                        # print("========我正在处理法一=================")
                        # print(unit1,is_weight_unit(unit1),nwAndUnit,is_pure_number(nwAndUnit))
                        # print("========我正在处理法一=================")
                        #如果发一是重量单位,直接使用净重换算
                        if is_weight_unit(unit1):
                            if nwAndUnit:
                                # 如果是重量单位，并且净重是纯数字，那么使用净重进行处理
                                if is_pure_number(nwAndUnit) :
                                    product["法定第一数量"] = nwAndUnit
                                    product["法定第一计量单位"] = unit1
                                else:
                                    match = re.match(r'(\d+\.?\d*)(.*)', nwAndUnit)
                                    if match:
                                        ai_nw = match.group(1)
                                        ai_nw_unit = match.group(2)
                                        ai_nw_unit_code,ai_nw_unit = unit_handler(ai_nw_unit)
                                        if ai_nw_unit_code:
                                            if ai_nw_unit_code == unit1:
                                                product["法定第一数量"] = ai_nw
                                                product["法定第一计量单位"] = unit1
                                            else:
                                                # 如果单位不一致，那么需要进行换算
                                                converted_qty1 = handle_unit_conversion(ai_nw_unit_code, unit1, Decimal(ai_nw))
                                                if converted_qty1:
                                                    product["法定第一数量"] = converted_qty1
                                                    product["法定第一计量单位"] = unit1
                                                else:
                                                    product["法定第一数量"] = ai_nw
                                            product["法定第一计量单位"] = unit1
                                        else:
                                            product["法定第一数量"] = ai_nw
                                            product["法定第一计量单位"] = unit1

                        if "法定第一数量及单位" in product and product["法定第一数量及单位"]:
                            qty1AndUnit1 = "".join(product["法定第一数量及单位"].split())
                            if is_pure_number(qty1AndUnit1):
                                product["法定第一数量"] = qty1AndUnit1
                                product["法定第一计量单位"] = unit1
                            else:
                                # 如果法一单位不是纯数字，那么需要拆分,数据部分为数量，其余为单位
                                match = re.match(r'(\d+\.?\d*)(.*)', qty1AndUnit1)
                                if match:
                                    ai_qty1 = match.group(1)
                                    ai_unit1_code,ai_unit1 = unit_handler(match.group(2))
                                    if ai_unit1_code:
                                        if ai_unit1_code == unit1:
                                            product["法定第一数量"] = ai_qty1
                                            product["法定第一计量单位"] = unit1
                                        else:
                                            # 如果单位不一致，那么需要进行换算
                                            converted_qty1 = handle_unit_conversion(ai_unit1_code, unit1, Decimal(ai_qty1))
                                            if converted_qty1:
                                                product["法定第一数量"] = converted_qty1
                                                product["法定第一计量单位"] = unit1
                        #如果使用模型返回的法一没有处理完成，那么使用净重进行处理
                        if ("法定第一数量" not in product or not product["法定第一数量"]) and nwAndUnit:
                            # 如果是重量单位，并且净重是纯数字，那么使用净重进行处理
                            if is_pure_number(nwAndUnit) and is_weight_unit(unit1):
                                product["法定第一数量"] = nwAndUnit
                                product["法定第一计量单位"] = unit1
                            else:
                                match = re.match(r'(\d+\.?\d*)(.*)', nwAndUnit)
                                if match:
                                    ai_nw = match.group(1)
                                    ai_nw_unit = match.group(2)
                                    ai_nw_unit_code,ai_nw_unit = unit_handler(ai_nw_unit)
                                    if ai_nw_unit_code:
                                        if ai_nw_unit_code == unit1:
                                            product["法定第一数量"] = ai_nw
                                            product["法定第一计量单位"] = unit1
                                        else:
                                            # 如果单位不一致，那么需要进行换算
                                            converted_qty1 = handle_unit_conversion(ai_nw_unit_code, unit1, Decimal(ai_nw))
                                            if converted_qty1:
                                                product["法定第一数量"] = converted_qty1
                                                product["法定第一计量单位"] = unit1
                        # 如果使用模型返回的法一没有处理完成，那么在使用成交数量进行换算                        
                        if ("法定第一数量" not in product or not product["法定第一数量"]) and qtyAndUnit:
                            # 如果是重量单位，并且净重是纯数字，那么使用净重进行处理
                            if is_pure_number(qtyAndUnit):
                                product["法定第一数量"] = qtyAndUnit
                                product["法定第一计量单位"] = unit1
                            else:
                                if "成交计量单位代码" in product and product["成交计量单位代码"] == unit1:
                                    product["法定第一数量"] = product["成交数量"]
                                    product["法定第一计量单位"] = unit1
                                else:
                                    converted_qty1 = handle_unit_conversion(product["成交计量单位代码"], unit1, Decimal(product["成交数量"]))
                                    if converted_qty1:
                                        product["法定第一数量"] = converted_qty1
                                        product["法定第一计量单位"] = unit1
                                    else:
                                        product["法定第一数量"] = product["成交数量"]
                                        product["法定第一计量单位"] = unit1
                                            
                    # 处理法二,，逻辑同法一
                    if "unit2" in hs_data and hs_data["unit2"]:
                        unit2 = hs_data["unit2"] # 税则中的法二单位代码
                        #如果法二是重量单位，直接使用净重换算
                        if is_weight_unit(unit2):
                            if nwAndUnit:
                                if is_pure_number(nwAndUnit):
                                    product["法定第二数量"] = nwAndUnit
                                    product["法定第二计量单位"] = unit2
                                else:
                                    match = re.match(r'(\d+\.?\d*)(.*)', nwAndUnit)
                                    if match:
                                        ai_nw = match.group(1)
                                        ai_nw_unit = match.group(2)
                                        ai_nw_unit_code,ai_nw_unit = unit_handler(ai_nw_unit)
                                        if ai_nw_unit_code:
                                            if ai_nw_unit_code == unit2:
                                                product["法定第二数量"] = ai_nw
                                                product["法定第二计量单位"] = unit2
                                            else:
                                                # 如果单位不一致，那么需要进行换算
                                                converted_qty2 = handle_unit_conversion(ai_nw_unit_code, unit2, Decimal(ai_nw))
                                                if converted_qty2:
                                                    product["法定第二数量"] = converted_qty2
                                                    product["法定第二计量单位"] = unit2
                                                else:
                                                    product["法定第二数量"] = ai_nw
                                                    product["法定第二计量单位"] = unit2
                                        else:
                                            product["法定第二数量"] = ai_nw
                                            product["法定第二计量单位"] = unit2

                        if "法定第二数量及单位" in product and product["法定第二数量及单位"]:
                            qty2AndUnit2 = "".join(product["法定第二数量及单位"].split())
                            if is_pure_number(qty2AndUnit2):
                                product["法定第二数量"] = qty2AndUnit2
                                product["法定第二计量单位"] = unit2
                            else:
                                match = re.match(r'(\d+\.?\d*)(.*)', qty2AndUnit2)
                                if match:
                                    ai_qty2 = match.group(1)
                                    ai_qty2_unit = match.group(2)
                                    ai_qty2_unit_code,ai_qty2_unit = unit_handler(ai_qty2_unit)
                                    if ai_qty2_unit_code:
                                        if ai_qty2_unit_code == unit2:
                                            product["法定第二数量"] = ai_qty2
                                            product["法定第二计量单位"] = unit2
                                        else:
                                            # 如果单位不一致，那么需要进行换算
                                            converted_qty2 = handle_unit_conversion(ai_qty2_unit_code, unit2, Decimal(ai_qty2))
                                            if converted_qty2:
                                                product["法定第二数量"] = converted_qty2
                                                product["法定第二计量单位"] = unit2
                        #如果使用模型返回的法二没有处理完成，那么使用净重进行处理
                        if ("法定第二数量" not in product or not product["法定第二数量"]) and nwAndUnit:
                            # 如果是重量单位，并且净重是纯数字，那么使用净重进行处理
                            if is_pure_number(nwAndUnit) and is_weight_unit(unit2):
                                product["法定第二数量"] = nwAndUnit
                                product["法定第二计量单位"] = unit2
                            else:
                                match = re.match(r'(\d+\.?\d*)(.*)', nwAndUnit)
                                if match:
                                    ai_nw = match.group(1)
                                    ai_nw_unit = match.group(2)
                                    ai_nw_unit_code,ai_nw_unit = unit_handler(ai_nw_unit)
                                    if ai_nw_unit_code:
                                        if ai_nw_unit_code == unit2:
                                            product["法定第二数量"] = ai_nw
                                            product["法定第二计量单位"] = unit2
                                        else:
                                            # 如果单位不一致，那么需要进行换算
                                            converted_qty2 = handle_unit_conversion(ai_nw_unit_code, unit2, Decimal(ai_nw))
                                            if converted_qty2:
                                                product["法定第二数量"] = converted_qty2
                                                product["法定第二计量单位"] = unit2
                        # 如果使用模型返回的法二没有处理完成，那么使用成交数量进行处理
                        if ("法定第二数量" not in product or not product["法定第二数量"]) and qtyAndUnit:
                            # 如果是重量单位，并且净重是纯数字，那么使用净重进行处理
                            if is_pure_number(qtyAndUnit):
                                product["法定第二数量"] = qtyAndUnit
                                product["法定第二计量单位"] = unit2
                            else:
                                if "成交计量单位代码" in product and product["成交计量单位代码"] == unit2:
                                    product["法定第二数量"] = product["成交数量"]
                                    product["法定第二计量单位"] = unit2
                                else:
                                    converted_qty2 = handle_unit_conversion(product["成交计量单位代码"], unit2, Decimal(product["成交数量"]))
                                    if converted_qty2:
                                        product["法定第二数量"] = converted_qty2
                                        product["法定第二计量单位"] = unit2
                if "controlmark" in hs_data and hs_data["controlmark"]:
                    product["商检标识"] = hs_data["controlmark"].strip()
                                            
    # del product["申报要素"],product["规格型号"],product["品牌类型"],product["出口享惠情况"],product["品牌"]
    keys_to_remove = [ "规格型号", "品牌类型", "出口享惠情况", "品牌","法定第一数量及单位","法定第二数量及单位","数量及单位","成交计量单位","申报要素","商品列表"]
    for key in keys_to_remove:
        product.pop(key, None)  # 第二个参数 None 表示键不存在时不报错                         
    return product

#处理商品信息
async def process_product_info(merged):
    if "content" in merged and "商品信息" in merged["content"]:
        tasks = [handle_product(product) for product in merged["content"]["商品信息"]]
        all_results = await asyncio.gather(*tasks)
        merged["content"]["商品信息"] = all_results

        # 所有商品处理完成之后，按照商检标识进行排序，如果商检标识有值且包含B的，排序在前面
        merged["content"]["商品信息"] = sorted(merged["content"]["商品信息"], key=lambda x: x.get("商检标识", "").startswith("B"), reverse=True)
        # 按照新的排序，重新生成项号
        for index, product in enumerate(merged["content"]["商品信息"]):
            product["项号"] = str(index + 1)
            product.pop("商检标识", None)
# 集装箱信息
def process_container_info(merged):
    print(merged["content"])
# 处理海关代码字段
def process_customs_data(merged):
    # 境内货源地、境内目的地、最终目的国、运输方式、原产国、指运港、经停港、启运港、运输方式、贸易国、运抵国、启运国、离境口岸、入境口岸、征免方式、监管方式、包装种类
    field_pairs = [
        ("运输方式", "运输方式代码"),
        ("指运港", "指运港代码"),
        ("经停港", "经停港代码"),
        ("启运港", "启运港代码"),
        ("贸易国", "贸易国代码"),
        ("运抵国", "运抵国代码"),
        ("启运国", "启运国代码"),
        ("离境口岸", "离境口岸代码"),
        ("入境口岸", "入境口岸代码"),
        ("征免性质", "征免性质代码"),
        ("监管方式", "监管方式代码"),
        ("包装种类", "包装种类代码"),
        ("原产国" ,"原产国代码"),
        ("境内货源地" ,"境内货源地代码"),
        ("最终目的国" ,"最终目的国代码"),
        ("征免方式" ,"征免方式代码"),
        ("成交方式" ,"成交方式代码"),
        ("出境关别","出境关别代码"),
        ("入境关别","入境关别代码"),
        ("申报地海关","申报地海关代码")
    ]
    data = merged["content"]
    for name_field, code_field in field_pairs:
        if name_field in data:
            data[name_field] = f"{data[name_field]}（{data.get(code_field, '')}）"
            data.pop(code_field, None)  # 安全移除代码字段
    product_info = merged["content"].get("商品信息",[])
    for product in product_info:
        for name_field, code_field in field_pairs:
            if name_field in product:
                product[name_field] = f"{product[name_field]}（{product.get(code_field, '')}）"
                product.pop(code_field, None)  # 安全移除代码字段
    return merged
# 更新字段名称，使用海关标准字段
def translate_json_keys(json):
    field_mapping = {
        "境内发货人":"TradeName",
        "境外收货人":"OverseasConsignorCname",
        "申报单位代码": "AgentCode",
        "申报单位名称": "AgentName",
        "批准文号": "ApprNo",
        "数据中心统一编号": "SeqNo",
        "进出口标志": "IEFlag",
        "单据类型": "Type",
        "提运单号": "BillNo",
        "合同协议号": "ContrNo",
        "录入单位代码": "CopCode",
        "录入单位名称": "CopName",
        "申报地海关": "CustomMaster",
        "征免性质": "CutMode",
        "经停港": "DistinatePort",
        "抵运港": "DistinatePort",
        "指运港":"DistinatePort",
        "监管方式":"TradeMode",
        "运费币制": "FeeCurr",
        "运费标记": "FeeMark",
        "运费": "FeeRate",
        "总毛重": "GrossWet",
        "出口日期": "IEDate",
        "进口日期": "IEDate",
        "入境关别": "IEPort",
        "出境关别": "IEPort",
        "入境口岸":"EntyPortCode",
        "出境口岸":"EntyPortCode",
        "保费币制": "InsurCurr",
        "保费标记": "InsurMark",
        "保费": "InsurRate",
        "许可证号": "LicenseNo",
        "备案号": "ManualNo",
        "总净重": "NetWt",
        "备注": "NoteS",
        "杂费币制": "OtherCurr",
        "杂费标志": "OtherMark",
        "杂费": "OtherRate",
        "消费使用单位": "OwnerName",
        "消费使用单位代码": "OwnerCode",
        "生产销售单位": "OwnerName",
        "生产销售单位代码": "OwnerCode",
        "总件数": "PackNo",
        "运抵国": "TradeCountry",
        "启运国": "TradeCountry",
        "贸易方式": "TradeMode",
        "运输方式": "TrafMode",
        "运输工具名称": "TrafName",
        "航次号":"VoyNo",
        "成交方式": "TransMode",
        "包装种类": "WrapType",
        "海关编号": "EntryId",
        "预录入编号": "PreEntryId",
        "报关标志": "EdiId",
        "风险评估参数": "Risk",
        "报关单类型": "EntryType",
        "打印日期": "PDate",
        "录入员IC卡号": "TypistNo",
        "录入员名称": "InputerName",
        "申报人标识": "PartenerID",
        "关联单据号": "TgdNo",
        "报关/转关关系标志": "DeclTrnRel",
        "担保验放标志": "ChkSurety",
        "备案清单类型": "BillType",
        "录入单位统一编码": "CopCodeScc",
        "价格说明": "PromiseItmes",
        "贸易国": "TradeAreaCode",
        "标记及号码": "MarkNo",
        "启运港代码": "DespPortCode",
        "入境口岸代码": "EntyPortCode",
        "存放地点": "GoodsPlace",
        "B/L号": "BLNo",
        "特种业务标识": "SpecDeclFlag",
        "目的地海关": "PurpOrgCode",
        "启运日期": "DespDate",
        "卸毕日期": "CmplDschrgDt",
        "关联理由": "CorrelationReasonFlag",
        "原集装箱标识": "OrigBoxFlag",
        "申报人员姓名": "DeclareName",
        "无其他包装": "NoOtherPack",
        "境外发货人代码": "OverseasConsignorCode",
        "境外收发货人名称": "OverseasConsignorCname",
        "境外发货人名称（外文）": "OverseasConsignorEname",
        "境外收发货人地址": "OverseasConsignorAddr",
        "境外收货人编码": "OverseasConsigneeCode",
        "境外收货人名称(外文)": "OverseasConsigneeEname",
        "境内收发货人名称（外文）": "DomesticConsigneeEname",
        "关联号码": "CorrelationNo",
        "境内收发货人检验检疫编码": "TradeCiqCode",
        "生产/消费使用单位检验检疫编码": "OwnerCiqCode",
        "申报单位检验检疫编码": "DeclCiqCode",
        "发票号":"InvoiceNo",
        "发票日期":"InvoiceDate",
        "离境口岸":"EntyPortCode",

        "特殊关系确认":"promiseItem1",
        "价格影响确认":"promiseItem2",
        "与货物有关的特许权使用费支付确认":"promiseItem3",
        "公式定价确认":"promiseItem4",
        "暂定价格确认":"promiseItem5",

        "商品信息":"DecList",
        "归类标志": "ClassMark",
        "商品编号": "CodeTS",
        "备案序号": "ContrItem",
        "单价": "DeclPrice",
        "征免方式": "DutyMode",
        "申报计量单位与法定单位比例因子": "Factor",
        "商品规格、型号": "GModel",
        "商品名称": "GName",
        "项号": "GNo",
        "原产国": "OriginCountry",
        "币制": "TradeCurr",
        "总价": "DeclTotal",
        "成交数量": "GQty",
        "法定第一数量": "FirstQty",
        "法定第二数量": "SecondQty",
        "成交计量单位代码": "GUnit",
        "法定第一计量单位": "FirstUnit",
        "法定第二计量单位": "SecondUnit",
        "用途/生产厂家": "UseTo",
        "工缴费": "WorkUsd",
        "货号": "ExgNo",
        "版本号": "ExgVersion",
        "最终目的国": "DestinationCountry",
        "监管类别名称": "CiqCode",
        "申报货物名称（外文）": "DeclGoodsEname",
        "原产地区代码": "OrigPlaceCode",
        "用途代码": "Purpose",
        "产品有效期": "ProdValidDt",
        "产品保质期": "ProdQgp",
        "货物属性代码": "GoodsAttr",
        "成份/原料/组份": "Stuff",
        "UN编码": "Uncode",
        "危险货物名称": "DangName",
        "危包类别": "DangPackType",
        "危包规格": "DangPackSpec",
        "境外生产企业名称": "EngManEntCnm",
        "非危险化学品": "NoDangFlag",
        "目的地代码": "DestCode",
        "检验检疫货物规格": "GoodsSpec",
        "货物型号": "GoodsModel",
        "货物品牌": "GoodsBrand",
        "生产日期": "ProduceDate",
        "生产批号": "ProdBatchNo",
        "生产单位注册号": "MnufctrRegNo",
        "生产单位名称": "MnufctrRegName",
        "境内货源地": "DistrictCode",
        "集装箱信息":"DecContainer",
        "集装箱号":"ContainerId",
        "集装箱规格":"ContainerMd",
        "集装箱自重":"ContainerWt" ,
        "拼箱标识":"LclFlag",
        "商品项号关系":"GoodsNo",
        "净重":"Nw",
        "毛重":"Gw",
        "分箱明细":"listWithContainer",
        "体积": "volume",
        "数量": "quantity",
        "数量单位": "quantityUnit",
        "箱数": "numberOfCases",

        "随附单证信息":"DecLicenseDocus",
        "随附单证代码":"DocuCode",
        "单证编号":"CertCode",
    }
    if isinstance(json, dict):
        return {field_mapping.get(k, k): translate_json_keys(v) for k, v in json.items()}
    elif isinstance(json, list):
        return [translate_json_keys(item) for item in json]
    else:
        return json
async def field_handler(merged):
    # 处理运费
    if "运费" in merged["content"] and merged["content"]["运费"]:
        # 先去掉字符串中的空格
        fee ="".join(merged["content"]["运费"].split())
        fee_array= ["","","3"] #定义运费代码数组，包含币制、运费、代码
        if "/" in fee:
            parts = merged["content"]["运费"].split("/")
            if len(parts) == 3:  
                fee_array[0] = parts[0]
                fee_array[1] = parts[1]
                fee_array[2] = parts[2]
        else:
            if is_pure_number(fee):
                fee_array[1] = fee
            else:
                # 使用正则匹配
                fee_currency, fee_number = extract_number_and_currency(fee)
                fee_array[0] = fee_currency
                fee_array[1] = fee_number
        
        if "运费币制" in merged["content"] and merged["content"]["运费币制"]:
            fee_array[0] = merged["content"]["运费币制"]
        merged["content"]["运费币制"] = fee_array[0]
        merged["content"]["运费"] = fee_array[1]
        merged["content"]["运费标记"] = fee_array[2]

    # 处理保费
    if "保费" in merged["content"] and merged["content"]["保费"]:
        insurance = "".join(merged["content"]["保费"].split())
        insurance_array = ["","","3"] #定义保费代码数组，包含币制、保费、代码
        if "/" in insurance:
            parts = merged["content"]["保费"].split("/")
            if len(parts) == 3:
                insurance_array[0] = parts[0]
                insurance_array[1] = parts[1]
                insurance_array[2] = parts[2]
        else:
            if is_pure_number(insurance):
                insurance_array[1] = insurance
            else:
                # 使用正则匹配
                ins_currency, ins_number = extract_number_and_currency(insurance)
                insurance_array[0] = ins_currency
                insurance_array[1] = ins_number
        
        if "保费币制" in merged["content"] and merged["content"]["保费币制"]:
            insurance_array[0] = merged["content"]["保费币制"]
        merged["content"]["保费币制"] = insurance_array[0]
        merged["content"]["保费"] = insurance_array[1]
        merged["content"]["保费标记"] = insurance_array[2]
    # 处理杂费
    if "杂费" in merged["content"] and merged["content"]["杂费"]:
        extras = "".join(merged["content"]["杂费"].split())
        extras_array = ["","","3"] #定义杂费代码数组，包含币制、杂费、代码
        if "/" in extras:
            parts = merged["content"]["杂费"].split("/")
            if len(parts) == 3:
                extras_array[0] = parts[0]
                extras_array[1] = parts[1]
                extras_array[2] = parts[2]
        else:
            if is_pure_number(extras):
                extras_array[1] = extras
            else:
                # 使用正则匹配
                ext_currency, ext_number = extract_number_and_currency(insurance)
                extras_array[0] = ext_currency
                extras_array[1] = ext_number
        
        if "杂费币制" in merged["content"] and merged["content"]["杂费币制"]:
            extras_array[0] = merged["content"]["杂费币制"]
        merged["content"]["杂费币制"] = extras_array[0]
        merged["content"]["杂费"] = extras_array[1]
        merged["content"]["杂费标记"] = extras_array[2]
    # 处理合同协议号
    if "合同协议号" in merged["content"] and merged["content"]["合同协议号"]:
        if len(merged["content"]["合同协议号"])>32:
            contrNo = merged["content"]["合同协议号"]
            merged["content"]["合同协议号"] = contrNo[:30]+"等"
            remark = merged["content"].get("备注","")
            remark = remark+" 完整合同号："+contrNo
            merged["content"]["备注"] = remark
    # 处理电子底账数据号
    if "电子底账数据号" in merged["content"] and merged["content"]["电子底账数据号"] and "随附单证信息" not in merged["content"]:
        DecLicenseDocu = {
            "DocuCode":"B",
            "CertCode":merged["content"]["电子底账数据号"]
        }
        merged["content"]["DecLicenseDocus"]=[DecLicenseDocu]
        merged["content"].pop("电子底账数据号", None)
    # 删除不要的字段
    keys_to_remove = [ "申报要素", "单据类型", "总价大写", "场站","船代","分箱明细"]
    for key in keys_to_remove:
        merged["content"].pop(key, None) 
    # 处理商品信息
    await process_product_info(merged)
    #处理集装箱信息
    process_container_info(merged)
    merged = process_customs_data(merged)
    merged = translate_json_keys(merged)

    # 根据口岸和关别的关系进行映射
    if ("IEPort" in merged["content"] and merged["content"]["IEPort"]) or ("EntyPortCode" in merged["content"] and merged["content"]["EntyPortCode"]):
        IEPort,EntyPortCode = customs_to_port_handler(merged["content"].get("IEPort",""),merged["content"].get("EntyPortCode",""))
        if IEPort:
            merged["content"]["IEPort"] = IEPort
        if EntyPortCode:
            merged["content"]["EntyPortCode"] = EntyPortCode

    # 通过API接口获取备案企业信息

    scCode,regCiqCode,regCusCode = get_enter_info(merged["content"]["TradeName"])
    if scCode and regCiqCode and regCusCode:
        merged["content"]["scCode"] = scCode
        merged["content"]["regCiqCode"] = regCiqCode
        merged["content"]["regCusCode"] = regCusCode
        #如果境内收发货人跟生产销售单位一致，那么直接使用收发货人的代码
        if merged["content"]["TradeName"] == merged["content"]["OwnerName"]:
            merged["content"]["scCode1"] = scCode
            merged["content"]["regCiqCode1"] = regCiqCode
            merged["content"]["regCusCode1"] = regCusCode
        else:
            scCode1,regCiqCode1,regCusCode1 = get_enter_info(merged["content"]["OwnerName"])
            if scCode1 and regCiqCode1 and regCusCode1:
                merged["content"]["scCode1"] = scCode1
                merged["content"]["regCiqCode1"] = regCiqCode1
                merged["content"]["regCusCode1"] = regCusCode1
        #处理境内货源地，如果没有境内货源地使用海关代码前五位    
        regCusCode_5 = regCusCode[:5] # 获取海关代码的前五位
        product_info = merged["content"].get("DecList",[])
        for product in product_info:
            if "DistrictCode" not in product:
                region_code,region_name = region_handler(regCusCode_5)
                if region_code and region_name:
                    product["DistrictCode"] = f"{region_name}（{region_code}）"
    return merged

# 处理舱单字段
def manifest_field(merged):
    if "场站" in merged["content"] and merged["content"]["场站"]:
        station = station_handler(merged["content"]["场站"])
        merged["content"]["场站"] = station
    if "船代" in merged["content"] and merged["content"]["船代"]:
        shipping_agent = shipping_agent_handler(merged["content"]["船代"])
        merged["content"]["船代"] = shipping_agent
    return merged

from collections import defaultdict

def build_validation_rules(history_data):
    rules = defaultdict(lambda: {
        "单价范围": [float('inf'), float('-inf')],  # 初始化为无效范围
        "允许币制": set(),
        "允许原产国": set(),
        "允许型号": set()
    })
    
    for item in history_data:
        material_id = item.get("物料号")
        if not material_id:
            continue
        
        rule = rules[material_id]
        
        # 处理单价（严格跳过无效值）
        if "单价" in item:
            try:
                unit_price = float(item["单价"])
                rule["单价范围"][0] = min(rule["单价范围"][0], unit_price)
                rule["单价范围"][1] = max(rule["单价范围"][1], unit_price)
            except (TypeError, ValueError):
                pass  # 静默跳过无效单价
        
        # 其他字段处理（保持不变）
        if "币制" in item:
            rule["允许币制"].add(item["币制"])
        if "原产国" in item:
            rule["允许原产国"].add(item["原产国"])
        if "型号" in item:
            rule["允许型号"].add(item["型号"])
    
    # 转换集合为列表
    for material_id in rules:
        # 如果所有单价均无效，则重置范围为[0,0]（或根据业务需求调整）
        if rules[material_id]["单价范围"] == [float('inf'), float('-inf')]:
            rules[material_id]["单价范围"] = [0, 0]  # 或设置为None
            
        rules[material_id]["允许币制"] = list(rules[material_id]["允许币制"])
        rules[material_id]["允许原产国"] = list(rules[material_id]["允许原产国"])
        rules[material_id]["允许型号"] = list(rules[material_id]["允许型号"])
    
    return dict(rules)
#特殊企业内容处理
async def special_field_handler(merged,sys_rules):
    print("sys_rules:",sys_rules)
    #1、根据境内发货人去查找有无特定规则
    TradeName = merged["content"].get("TradeName","")
    if sys_rules:
        result = await content_processor.special_field_handler(sys_rules,merged)
        print("模型处理完成之后的结果是：",result)
        # 重新整理申报要素
        product_info = result["content"].get("DecList",[])
        for product in product_info:
            sbys = product.get("申报要素集合",[])
            if sbys:
                sbys_arry = sbys[1]
                sbys_str = "|".join(sbys_arry.values()).rstrip("|")
                product["GModel"] = sbys_str

        print("==========================================")
        print(result)

        #2、有特定规则进行模型处理
        #3、无特定规则的直接返回原始数据

        return result
    else:
        return merged
async def field_handler_I(merged):
    results = []
    product_info = merged["content"].get("商品信息",[])
    history_declare_info = merged["content"].get("历史申报记录",[])
    if product_info and history_declare_info:
        validation_rules = build_validation_rules(history_declare_info)
        print("验证规则：",validation_rules)
        for item in product_info:
            if "物料号" in item and item["物料号"]:
                material_id = item["物料号"]
                rule = validation_rules.get(material_id, None)
                errors = []
                if rule:
                    if "单价" in item and "单价范围" in rule:
                        unit_price = float(item["单价"])
                        # 检查单价
                        min_price, max_price = rule["单价范围"]
                        if not (min_price <= unit_price <= max_price):
                            errors.append(f"单价 {unit_price} 超出历史范围 [{min_price}, {max_price}]")
                    
                    # 检查币制
                    if "币制" in item and "允许币制" in rule:
                        if item["币制"] not in rule["允许币制"]:
                            errors.append(f"币制 {item['币制']} 不在历史允许范围内 {rule['允许币制']}")
                    
                    # 检查原产国
                    if "原产国" in item and "允许原产国" in rule:
                        if item["原产国"] not in rule["允许原产国"]:
                            errors.append(f"原产国 {item['原产国']} 不在历史允许范围内 {rule['允许原产国']}")
                    
                    # 检查规格型号（模糊匹配）
                    if "规格型号" in item and "允许型号" in rule:
                        model_ok = any(
                            allowed_model in item["规格型号"]
                            for allowed_model in rule["允许型号"]
                        )
                        if not model_ok:
                            errors.append(f"规格型号 {item['规格型号']} 不匹配历史允许的型号 {rule['允许型号']}")
                    
                    status = "通过" if not errors else "失败"
                    if errors: 
                        results.append({
                            "物料号": material_id,
                            "商品名称": item["商品名称"],
                            "状态": status,
                            "错误": errors
                        })
        merged["content"]["验证结果"] = results
    return merged
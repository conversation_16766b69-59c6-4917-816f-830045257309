# 类似于DeepSeek处理器的实现 

import logging
import json
import os
import requests
import asyncio
import aiohttp
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3.util.retry import Retry
from typing import Dict, Any, List, Optional
from .base import BaseAIModel

class VllmHandler(BaseAIModel):
    """Vllm模型处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_url = config["api_url"]
        self.model = config["model"]
        
        # 配置重试策略
        self.session = aiohttp.ClientSession()  # 创建异步会话
    async def close(self):
        """关闭模型"""
        await self.session.close()
    async def chat(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """调用Vllm API (最终修正版)"""
        try:
            payload = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.1,
                "top_p": 0.9,
                "stream": False
            }
            async with self.session.post(
                f"{self.api_url}/v1/chat/completions",
                json=payload,
                timeout=200
            ) as response:
                response_text = await response.text()

                if response.status != 200:
                    logging.error(f"API错误 [{response.status}]: {response_text[:500]}")
                    return None

                try:
                    result = json.loads(response_text)
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败 | 错误: {e} | 响应内容: {response_text[:200]}...")
                    return None

                choices = result.get("choices")
                if not choices or not isinstance(choices, list):
                    logging.error("响应结构错误，未找到 choices 字段")
                    return None

                return choices[0]["message"]["content"]

        except aiohttp.ClientError as e:
            logging.error(f"网络请求失败: {str(e)}")
        except asyncio.TimeoutError:
            logging.error("API请求超时")
        except Exception as e:
            logging.error(f"未知错误: {repr(e)}")

        return None
    
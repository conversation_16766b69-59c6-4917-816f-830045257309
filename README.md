# 外贸单据处理系统

智能识别、自动分类、结构化输出外贸单据的一体化处理平台

## 功能特性

- **多格式支持**
  支持 PDF/Word/Excel/图片/ZIP等常见格式
- **智能分类**
  自动识别出口发票、箱单、证书等7大类单据
- **结构化提取**
  精准提取50+关键业务字段（境内发货人、境外收货人等）
- **智能校验**
  自动校验单价总价一致性等10+业务规则
- **多模型支持**
  Ollama/DeepSeek/豆包AI 多引擎切换

## 技术栈

### 核心框架

- FastAPI (Python 3.10+)

### 文档处理

- PaddleOCR 2.10 (文字识别)
- PDF2Image (PDF解析)
- OpenCV 4.11 (图像处理)
- Openpyxl/Xlrd (Excel处理)
- Python-docx(docx处理)

### AI集成

- Ollama (本地大模型)
- DeepSeek API
- 豆包AI Vision

### 前端

- Bootstrap 5.1
- FontAwesome 6
- 响应式设计

## 快速开始

### 环境要求

### - Python 3.10+

- Poppler (PDF处理)
- CUDA 11.8 (GPU加速可选)

### Ubuntu安装GPU驱动

```
#更新系统
sudo apt update && sudo apt upgrade -y
#查看推荐驱动版本
ubuntu-drivers devices
#安装推荐驱动
sudo apt install nvidia-driver-550
#重启系统
sudo reboot
```

### 部署模型

```
#查看驱动是否安装成功
nvidia-smi
#安装ollama
curl -fsSL https://ollama.com/install.sh | sh
#下载运行模型
ollama run qwen2.5:72B
```

### 安装步骤

```bash
# 克隆仓库
git clone http://************:3000/JG/OCR-PY.git
#安装依赖
conda env create -f environment.yml
#切换环境
conda activate api
# 启动服务
python run.py
```

### 配置说明

1. **模型配置**
   修改`app/config.py`：

   ```python
   MODEL_CONFIGS = {
       ModelProvider.OLLAMA: {
           "api_url": "http://localhost:11434",
           "model": "qwen2.5:72b"
       }
   }
   ```
2. **环境变量**

   ```bash
   # DeepSeek
   export DEEPSEEK_API_KEY="your_api_key"
   # 豆包AI 
   export DOUBAO_API_KEY="your_api_key"
   ```

## 接口文档

### 主要API端点


| 端点          | 方法 | 说明           |
| ------------- | ---- | -------------- |
| `/upload`     | POST | 文件上传入口   |
| `/api/models` | GET  | 获取可用模型** |

请求示例：

```bash
curl -X POST -F "files=@/path/to/file.pdf" http://localhost:8000/upload
```

## 使用说明

1. 访问 `http://localhost:8000`
2. 上传单据文件(PDF/Word/Excel等)
3. 选择处理模型
4. 查看结构化结果
5. 系统界面

   ![1744335954372](images/README/1744335954372.png)

## 开发指南

### 项目结构

```text
.
├── app/                      # 后端核心模块
│   ├── ai_models/            # AI模型处理器
│   │   ├── deepseek_handler.py   # DeepSeek接口
│   │   ├── doubao_handler.py     # 豆包AI接口
│   │   ├── ollama_handler.py     # 本地Ollama接口
│   │   └── prompts.py           # 提示词管理
│   ├── file_processors/      # 文件处理器
│   │   ├── base.py           # 处理器基类
│   │   ├── pdf_processor.py  # PDF处理器
│   │   ├── office_processor.py # Office文档处理器
│   │   ├── image_processor.py  # 图片处理器
│   │   └── zip_processor.py   # 压缩文件处理器
│   ├── ocr/                  # OCR处理模块
│   │   └── ocr_processor.py  # PaddleOCR封装
│   ├── config.py             # 配置管理
│   ├── content_processor.py  # 内容处理流水线
│   └── main.py               # FastAPI主入口
├── frontend/                 # 前端界面
│   ├── index.html            # 主页面
│   └── js/
│       └── models.js         # 前端交互逻辑
├── model/                    # OCR模型文件
│   ├── table-transformer-detection/ # 表格识别模型
│   └── v3.0/                 # OCR文本模型
├── environment.yml           # Python依赖
└── run.py                    # 服务启动入口
```

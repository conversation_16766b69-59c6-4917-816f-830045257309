aiohappyeyeballs==2.6.1
aiohttp==3.11.12
aiosignal==1.3.2
albucore==0.0.23
albumentations==2.0.5
annotated-types==0.7.0
anyio==4.9.0
appnope==0.1.4
astor==0.8.1
asttokens==3.0.0
attrs==25.3.0
beautifulsoup4==4.13.3
certifi==2025.1.31
chardet==3.0.4
charset-normalizer==3.4.1
click==8.1.8
comm==0.2.2
compressed-rtf==1.0.7
Cython==3.0.12
debugpy==1.8.14
decorator==5.2.1
ebcdic==1.1.1
et_xmlfile==2.0.0
exceptiongroup==1.2.2
executing==2.1.0
extract-msg==0.29.0
fastapi==0.115.12
filelock==3.18.0
fire==0.7.0
fonttools==4.56.0
frozenlist==1.5.0
fsspec==2025.3.2
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.30.1
idna==3.10
imageio==2.37.0
IMAPClient==2.1.0
importlib_metadata==8.6.1
ipykernel==6.29.5
jedi==0.19.2
Jinja2==3.1.6
jupyter_client==8.6.3
jupyter_core==5.7.2
lazy_loader==0.4
lmdb==1.6.2
lxml==5.3.1
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
mpmath==1.3.0
multidict==6.2.0
nest_asyncio==1.6.0
networkx==3.4.2
numpy==1.24.4
olefile==0.47
opencv-contrib-python==*********
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
opt-einsum==3.3.0
packaging==24.2
paddleocr==2.10.0
paddlepaddle==3.0.0
parso==0.8.4
pdf2image==1.17.0
pexpect==4.9.0
pickleshare==0.7.5
pillow==11.1.0
pip==25.0
platformdirs==4.3.7
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==6.30.2
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyclipper==1.3.0.post6
pydantic==2.11.1
pydantic_core==2.33.0
Pygments==2.19.1
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-magic==0.4.27
python-multipart==0.0.20
PyYAML==6.0.2
pyzmq==26.4.0
RapidFuzz==3.12.2
rarfile==4.2
regex==2024.11.6
requests==2.32.3
safetensors==0.5.3
scikit-image==0.25.2
scipy==1.15.2
setuptools==75.8.0
shapely==2.0.7
simsimd==6.2.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
stack_data==0.6.3
starlette==0.46.1
stringzilla==3.12.3
sympy==1.13.3
termcolor==3.0.1
tifffile==2025.3.30
timm==1.0.15
tokenizers==0.21.1
torch==2.2.2
torchvision==0.17.2
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.49.0
typing_extensions==4.13.2
typing-inspection==0.4.0
tzlocal==5.3.1
urllib3==1.26.20
uvicorn==0.34.0
wcwidth==0.2.13
wheel==0.45.1
xlrd==1.2.0
yarl==1.18.3
zipp==3.21.0

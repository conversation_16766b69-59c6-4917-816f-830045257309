import requests
import json
import time
import random
import hashlib
import base64
import hmac
from urllib.parse import urlencode
from .config import secretKey, hs_code_api_url,enter_info_api_url

def generate_nonce(length=8):
    """生成随机数"""
    return ''.join([str(random.randint(0, 9)) for _ in range(length)])

def gen_sign(timestamp, secret):
    # 拼接timestamp和secret
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()
    # 对结果进行base64处理
    sign = base64.b64encode(hmac_code).decode('utf-8')
    return sign

def call_api(payload,api_url):
    """调用API接口"""
    # # 准备请求参数
    # timestamp = str(int(time.time()))  # 精确到秒的时间戳
    
    
    # # 生成请求签名 (使用HMAC-SHA256 + Base64)
    # req_sign = gen_sign(timestamp, secretKey)
    
    # # 准备请求体
    # payload = {
    #     "timestamp": timestamp,
    #     "sign": req_sign,
    #     "code": hs_code
    # }
    
    # 设置请求头
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
    }
    
    try:
        # 在发送请求前打印最终请求数据
        print("Request URL:", api_url)
        print("Request Headers:", headers)
        print("Request Payload:", urlencode(payload))
        # 发送POST请求
        response = requests.post(api_url, 
                               headers=headers, 
                               data=urlencode(payload),
                               verify=False,
                               timeout=30 
                               )
        
        # 检查响应状态
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {
            "error": f"API请求失败: {str(e)}",
            "status_code": getattr(e.response, 'status_code', None),
            "response": getattr(e.response, 'text', None)
        }

# 获取税则信息
def get_hs_code(hs_code):
    # 准备请求参数
    timestamp = str(int(time.time()))  # 精确到秒的时间戳
    
    # 生成请求签名 (使用HMAC-SHA256 + Base64)
    req_sign = gen_sign(timestamp, secretKey)
    
    # 准备请求体
    payload = {
        "timestamp": timestamp,
        "sign": req_sign,
        "code": hs_code
    }
    return call_api(payload,hs_code_api_url)

#获取海关企业备案信息
def get_enter_info(tradeName):
    # 准备请求参数
    timestamp = str(int(time.time()))  # 精确到秒的时间戳
    
    # 生成请求签名 (使用HMAC-SHA256 + Base64)
    req_sign = gen_sign(timestamp, secretKey)
    
    # 准备请求体
    payload = {
        "timestamp": timestamp,
        "sign": req_sign,
        "name": tradeName
    }
    enter_info_result = call_api(payload,enter_info_api_url)
    if enter_info_result:
        if enter_info_result["code"] == 200 and enter_info_result["data"]["data"]:
            # print("接口调用的返回值",enter_info_result)
            try:
                enter_inf_data = enter_info_result["data"]["data"]
                copInfo = enter_inf_data["copInfo"][0]
                print(copInfo)
                scCode = copInfo["scCode"] # 统一社会信用代码
                regCiqCode = copInfo["ciqInfoList"][0]["regCiqCode"] # 商检编码
                regCusCode = copInfo["cusInfoList"][0]["regCusCode"] # 海关编码
                # ciqNameSaic1 // 企业名称
                # regCusCode1 // 海关编码
                # regCiqCode1  // 
                print(scCode,regCiqCode,regCusCode)
                return scCode,regCiqCode,regCusCode
            except Exception as e:
                return None,None,None
        else:
            return None,None,None
    else:
        return None,None,None

# 使用示例
if __name__ == "__main__":
    # 配置参数
    HS_CODE = "8708309500"                       # 10位税号
    # 调用API
    result = get_hs_code(HS_CODE)

    scCode,regCiqCode,regCusCode = get_enter_info("芜湖通亚国际贸易有限公司")
    print(scCode,regCiqCode,regCusCode)

    # print("API响应结果:", json.dumps(result, indent=2, ensure_ascii=False))


    
// 检查登录状态
function checkLogin() {
    const token = localStorage.getItem('token');
    if (!token) {
        // 获取当前页面路径
        const currentPath = window.location.pathname;
        // 保存当前页面路径到localStorage
        localStorage.setItem('redirect_after_login', currentPath);
        // 跳转到登录页
        window.location.href = '/static/login.html';
        return false;
    }
    return true;
}

// 添加token到请求头
function addAuthHeader(headers = {}) {
    const token = localStorage.getItem('token');
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    return headers;
}

// 登出
function logout() {
    localStorage.removeItem('token');
    window.location.href = '/static/login.html';
}

// 修改登录成功后的跳转逻辑
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        if (data.error) throw new Error(data.error);
        
        // 保存token
        localStorage.setItem('token', data.token);
        
        // 获取保存的跳转路径
        const redirectPath = localStorage.getItem('redirect_after_login') || '/static/index.html';
        localStorage.removeItem('redirect_after_login');
        
        // 跳转到目标页面
        window.location.href = redirectPath;
    } catch (e) {
        alert('登录失败: ' + e.message);
    }
}); 
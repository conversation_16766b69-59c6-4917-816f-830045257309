import os
from enum import Enum
from typing import Dict, Any

class ModelProvider(str, Enum):
    """支持的AI模型提供商"""
    DEEPSEEK = "deepseek"
    DOUBAO = "doubao"
    OLLAMA = "ollama"
    VLLM = "vllm"
    BAILIAN = "bailian"
# 全局配置
GLOBAL_CONFIG = {
    "default_model_provider": ModelProvider.DOUBAO,
    "max_retry_attempts": 3,
    "timeout_seconds": 90
}
# 设置全局变量，是否启用文档检查
ENABLE_DOCUMENT_CHECK = False

# 模型特定配置
MODEL_CONFIGS = {
    ModelProvider.DEEPSEEK: {
        "api_key": os.environ.get("DEEPSEEK_API_KEY", "***********************************"),
        "api_url": os.environ.get("DEEPSEEK_API_URL", "https://api.deepseek.com/v1/chat/completions"),
        "model": "deepseek-chat",
        "max_tokens": 8000,
        "handler_class": "app.ai_models.deepseek_handler.DeepSeekHandler",
        "description": "DeepSeek模型，提供高质量的中文理解和文档处理能力"
    },
    
    ModelProvider.DOUBAO: {
        # "api_key": os.environ.get("DOUBAO_API_KEY", "1abbdddf-92fe-43bb-a1ae-febe5f0b67f8"),
        "api_key": os.environ.get("DOUBAO_API_KEY", "0d1d8e22-a5b2-4ddd-aa49-f1b7916617e1"),
        "api_url": os.environ.get("DOUBAO_API_ENDPOINT", "https://ark.cn-beijing.volces.com/api/v3"),
        "model": os.environ.get("DOUBAO_MODEL_ID", "doubao-1-5-pro-256k-250115"),
        # "model": os.environ.get("DOUBAO_MODEL_ID", "doubao-pro-256k-241115"),
        "max_tokens": 12000,
        "handler_class": "app.ai_models.doubao_handler.DoubaoHandler",
        "description": "豆包AI Vision Pro模型，支持图文理解和长文档处理"
    },
    
    ModelProvider.OLLAMA: {
        "api_url": "http://************:11434",
        "model": "qwen2.5:72b",
        "max_tokens": 15000,
        "handler_class": "app.ai_models.ollama_handler.OllamaHandler",
        "description": "本地部署的Ollama模型 (Qwen 2.5)，支持离线处理"
    },
    ModelProvider.VLLM: {
        "api_url": "http://127.0.0.1:8300",
        "model": "qwen1.5-14b-chat",
        "max_tokens": 15000,
        "handler_class": "app.ai_models.vllm_handler.VllmHandler",
        "description": "本地部署的VLLM模型 (Qwen 2.5)，支持离线处理"
    },
    ModelProvider.BAILIAN: {
        "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "api_key": os.environ.get("BAILIAN_API_KEY", "sk-69d6f6a5648f4f8b9124cd329599a3da"),
        #"model": "qwen-plus",
        "model": "qwen-turbo-latest",
        "model_max": "qwen-max-latest",
        "model_plus": "qwen-plus-latest",
        "model_long": "qwen-long-latest",
        "max_tokens": 15000,
        "handler_class": "app.ai_models.bailian_handler.BailianHandler",
        "description": "阿里云百炼模型，支持离线处理"
    }
}

def get_model_config(provider: str) -> Dict[str, Any]:
    """获取特定模型的配置"""
    try:
        provider_enum = ModelProvider(provider.lower())
    except Exception:
        provider_enum = GLOBAL_CONFIG["default_model_provider"]
    return MODEL_CONFIGS.get(provider_enum, MODEL_CONFIGS[GLOBAL_CONFIG["default_model_provider"]])

def get_available_models():
    """获取所有可用模型的信息"""
    return {
        provider.value: {
            "name": provider.value,
            "description": config.get("description", ""),
            "max_tokens": config.get("max_tokens", 0)
        }
        for provider, config in MODEL_CONFIGS.items()
    }

"""MySQL数据库配置"""
    
# 基础配置
MYSQL_HOST: str = os.getenv('MYSQL_HOST', 'localhost')
MYSQL_PORT: int = int(os.getenv('MYSQL_PORT', '3306'))
MYSQL_USER: str = os.getenv('MYSQL_USER', 'root')
MYSQL_PASSWORD: str = os.getenv('MYSQL_PASSWORD', '1qaz@WSX')
MYSQL_DATABASE: str = os.getenv('MYSQL_DATABASE', 'carben_db')

# 连接池配置
POOL_NAME: str = "mysql_pool"
POOL_SIZE: int = 5
POOL_RESET_SESSION: bool = True
def to_dict() -> Dict[str, Any]:
    """转换为连接参数字典"""
    return {
        'host': MYSQL_HOST,
        'port': MYSQL_PORT,
        'user': MYSQL_USER,
        'password': MYSQL_PASSWORD,
        'database': MYSQL_DATABASE,
        'pool_name': POOL_NAME,
        'pool_size': POOL_SIZE,
        'pool_reset_session': POOL_RESET_SESSION
    }

# API 地址信息
secretKey = "lUHr6SfPghIg6H8Fyftf"
hs_code_api_url = "https://api.jgsoft.com.cn:15555/open-api/out/getHscode" #根据商品编码获取税则信息
enter_info_api_url = "https://api.jgsoft.com.cn:15555/open-api/out/getEnterInfo"
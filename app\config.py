import os
from enum import Enum
from typing import Dict, Any

class ModelProvider(str, Enum):
    """支持的AI模型提供商"""
    DEEPSEEK = "deepseek"
    DOUBAO = "doubao"
    OLLAMA = "ollama"
    VLLM = "vllm"
    BAILIAN = "bailian"
# 全局配置
GLOBAL_CONFIG = {
    "default_model_provider": ModelProvider.DOUBAO,
    "max_retry_attempts": 3,
    "timeout_seconds": 90
}
# 设置全局变量，是否启用文档检查
ENABLE_DOCUMENT_CHECK = False

# 模型特定配置
MODEL_CONFIGS = {
    ModelProvider.DEEPSEEK: {
        "api_key": os.environ.get("DEEPSEEK_API_KEY", "***********************************"),
        "api_url": os.environ.get("DEEPSEEK_API_URL", "https://api.deepseek.com/v1/chat/completions"),
        "model": "deepseek-chat",
        "max_tokens": 8000,
        "handler_class": "app.ai_models.deepseek_handler.DeepSeekHandler",
        "description": "DeepSeek模型，提供高质量的中文理解和文档处理能力"
    },
    
    ModelProvider.DOUBAO: {
        # "api_key": os.environ.get("DOUBAO_API_KEY", "1abbdddf-92fe-43bb-a1ae-febe5f0b67f8"),
        "api_key": os.environ.get("DOUBAO_API_KEY", "0d1d8e22-a5b2-4ddd-aa49-f1b7916617e1"),
        "api_url": os.environ.get("DOUBAO_API_ENDPOINT", "https://ark.cn-beijing.volces.com/api/v3"),
        "model": os.environ.get("DOUBAO_MODEL_ID", "doubao-1.5-pro-32k-250115"),
        # "model": os.environ.get("DOUBAO_MODEL_ID", "ep-20250411090447-m2z57"),
        "max_tokens": 10000,
        "handler_class": "app.ai_models.doubao_handler.DoubaoHandler",
        "description": "豆包AI Vision Pro模型，支持图文理解和长文档处理"
    },
    
    ModelProvider.OLLAMA: {
        "api_url": "http://************:11434",
        "model": "qwen2.5:72b",
        "max_tokens": 15000,
        "handler_class": "app.ai_models.ollama_handler.OllamaHandler",
        "description": "本地部署的Ollama模型 (Qwen 2.5)，支持离线处理"
    },
    ModelProvider.VLLM: {
        "api_url": "http://127.0.0.1:8300",
        "model": "qwen1.5-14b-chat",
        "max_tokens": 15000,
        "handler_class": "app.ai_models.vllm_handler.VllmHandler",
        "description": "本地部署的VLLM模型 (Qwen 2.5)，支持离线处理"
    },
    ModelProvider.BAILIAN: {
        "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "api_key": os.environ.get("BAILIAN_API_KEY", "sk-07fc2fd833014c48924c067344f92ac4"),
        "model": "qwen-plus",
        "max_tokens": 15000,
        "handler_class": "app.ai_models.bailian_handler.BailianHandler",
        "description": "阿里云百炼模型，支持离线处理"
    }
}

def get_model_config(provider: str) -> Dict[str, Any]:
    """获取特定模型的配置"""
    try:
        provider_enum = ModelProvider(provider.lower())
    except ValueError:
        provider_enum = GLOBAL_CONFIG["default_model_provider"]
    print("provider_enum是：", provider_enum)
    return MODEL_CONFIGS.get(provider_enum, MODEL_CONFIGS[GLOBAL_CONFIG["default_model_provider"]])

def get_available_models():
    """获取所有可用模型的信息"""
    return {
        provider.value: {
            "name": provider.value,
            "description": config.get("description", ""),
            "max_tokens": config.get("max_tokens", 0)
        }
        for provider, config in MODEL_CONFIGS.items()
    } 
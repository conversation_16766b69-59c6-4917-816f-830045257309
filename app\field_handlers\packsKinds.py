# 包装种类
PACKS_KINDS = {
    "00": "散装",
    "01": "裸装",
    "04": "球状罐类",
    "06": "包/袋",
    "22": "纸制或纤维板制盒/箱",
    "23": "木制或竹藤等植物性材料制盒/箱",
    "29": "其他材料制盒/箱",
    "32": "纸制或纤维板制桶",
    "33": "木制或竹藤等植物性材料制桶",
    "39": "其他材料制桶",
    "41": "中型散装容器",
    "42": "便携式罐体",
    "43": "可移动罐柜",
    "92": "再生木托",
    "93": "天然木托",
    "98": "植物性铺垫材料",
    "99": "其他包装",
}

def packs_kinds_handler(input_packs_kinds):
    if "其他" in input_packs_kinds or "其它" in input_packs_kinds:
        return "99","其他包装"
    for code,packs_kinds in PACKS_KINDS.items():
        if input_packs_kinds == packs_kinds or input_packs_kinds == code:
            return code,packs_kinds
    return None,input_packs_kinds

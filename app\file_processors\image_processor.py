import logging
import os
from typing import Dict, Any, Optional
from PIL import Image
from .base import BaseFileProcessor
from ..ai_models.base import BaseAIModel
from fastapi import Depends
from app.dependencies import get_ocr_initializer
from app.ocr.ocr_initializer import OCRInitializer

class ImageProcessor(BaseFileProcessor):
    """图像文件处理器"""
    
    def __init__(self, ai_model: BaseAIModel, ocr_initializer: OCRInitializer = Depends(get_ocr_initializer)):
        super().__init__(ai_model)
        try:
            from ..ocr import OCRProcessor
            self.ocr_processor = OCRProcessor(ocr_initializer)
            self.has_ocr = True
        except Exception as e:
            logging.error(f"OCR初始化失败: {str(e)}")
            self.has_ocr = False
    
    async def process(self, file_path: str,flag = True,ie_flag="E") -> Dict[str, Any]:
        """处理图像文件"""
        try:
            logging.info(f"开始处理图像文件: {file_path}")
            
            if not self.has_ocr:
                return {
                    "error": "OCR功能不可用，无法处理图像",
                    "file_name": os.path.basename(file_path)
                }
            # 使用OCR提取文本内容
            ocr_text = ""
            result = await self.ocr_processor.process_image(file_path)
            if result["status"] == "success":
                    ocr_text = result["text"]
            # content = ocr_text
            if not ocr_text:
                return {
                    "error": "图像文本提取失败",
                    "file_name": os.path.basename(file_path)
                }
            formatted = {
                    "单据类型": "其他",
                    "单据内容": ocr_text
                }    
            # 返回提取的内容和文件信息
            return {
                "contents":formatted,
                "ocr_text":ocr_text,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_type": "image"
                }
            }
            
        except Exception as e:
            logging.error(f"图像处理失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "file_name": os.path.basename(file_path)
            }
import logging
import re
import time
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from PIL import Image
import cv2
import torch
from paddleocr import PaddleOCR
from transformers import DetrImageProcessor, TableTransformerForObjectDetection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ocr_processor.log')
    ]
)
logger = logging.getLogger(__name__)

class OCRConfig:
    """OCR处理器配置类"""
    def __init__(self):
        # 性能配置
        self.batch_size = 4
        self.target_image_size = (1600, 1600)  # 适合A4纸的尺寸
        
        # 文本处理
        self.min_confidence = 0.6
        self.enable_text_correction = True
        self.text_correction_rules = {
            r'[oO0]': '0',
            r'[lL1]': '1',
            r'[，]': ',',
            r'[。]': '.'
        }
        
        # 表格处理
        self.table_iou_threshold = 0.5
        self.min_table_area = 1000
        self.cell_padding = 5  # 单元格识别时的边距
        
        # 系统
        self.max_retry = 2
        self.gpu_fallback = True

class OCRProcessor:
    """改进的OCR文本识别处理器"""
    def __init__(self, ocr_initializer):
        """
        接收已初始化的OCR资源
        ocr_initializer: OCRInitializer实例
        """
        # 从初始化器中获取必要资源
        self.poppler_path = ocr_initializer.poppler_path
        self.ocr = ocr_initializer.ocr
        self.table_model = ocr_initializer.table_model
        self.processor = ocr_initializer.processor
        self.config = OCRConfig()

        # 设备设置
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        if self.device == "cuda":
            self.table_model.to(self.device)
            self.ocr.use_gpu = True
        logger.info(f"初始化完成，运行设备: {self.device.upper()}")
        
        # 验证资源是否已初始化
        if not all([hasattr(self, 'poppler_path'), 
                    hasattr(self, 'ocr'), 
                    hasattr(self, 'table_model')]):
            raise RuntimeError("OCR资源未正确初始化")

    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        # 统一图像尺寸
        h, w = image.shape[:2]
        if h > self.config.target_image_size[1] or w > self.config.target_image_size[0]:
            image = cv2.resize(image, self.config.target_image_size, 
                             interpolation=cv2.INTER_LANCZOS4)
        
        # 增强对比度
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
            
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        if len(image.shape) == 3:
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2RGB)
            
        return enhanced

    def recognize_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        增强的文本识别方法
        
        Args:
            image: 输入图像(numpy数组)
            
        Returns:
            文本识别结果列表，每个元素包含text, bbox和confidence
        """
        try:
            # 预处理
            processed_img = self.preprocess_image(image)
            
            # OCR识别
            result = self.ocr.ocr(processed_img, cls=True)
            if not result or not result[0]:
                return []
            
            text_data = []
            for line in result[0]:
                if not line or len(line) < 2:
                    continue
                    
                bbox = line[0]
                text, confidence = line[1] if isinstance(line[1], (list, tuple)) else (line[1], 0.0)
                
                if confidence < self.config.min_confidence:
                    continue
                    
                # 文本后处理
                processed_text = self._post_process_text(text)
                
                text_data.append({
                    "text": processed_text,
                    "bbox": bbox,
                    "confidence": float(confidence)
                })
            
            # 合并被错误分割的文本行
            return self._merge_text_lines(text_data)
            
        except Exception as e:
            logger.error(f"文本识别失败: {str(e)}", exc_info=True)
            return []

    def _post_process_text(self, text: str) -> str:
        """文本后处理"""
        if not self.config.enable_text_correction:
            return text.strip()
            
        # 常见OCR错误修正
        for pattern, replacement in self.config.text_correction_rules.items():
            text = re.sub(pattern, replacement, text)
        
        # 去除特殊字符但保留中文、英文、数字和常用标点
        text = re.sub(r'[^\w\s,.，。、\u4e00-\u9fa5-]', '', text)
        return text.strip()

    def _merge_text_lines(self, text_data: List[Dict]) -> List[Dict]:
        """合并被错误分割的文本行"""
        if not text_data:
            return []
            
        # 按Y坐标然后X坐标排序
        text_data.sort(key=lambda x: (x["bbox"][0][1], x["bbox"][0][0]))
        
        merged = []
        i = 0
        while i < len(text_data):
            current = text_data[i]
            j = i + 1
            
            while j < len(text_data):
                next_item = text_data[j]
                
                # 检查是否应该合并(垂直位置相近且水平位置连续)
                y_diff = abs(current["bbox"][0][1] - next_item["bbox"][0][1])
                x_gap = next_item["bbox"][0][0] - current["bbox"][1][0]
                
                if y_diff < 10 and x_gap < 20:
                    # 合并文本
                    current["text"] += " " + next_item["text"]
                    
                    # 合并bbox (取并集)
                    current["bbox"] = [
                        [min(current["bbox"][0][0], next_item["bbox"][0][0]),
                         min(current["bbox"][0][1], next_item["bbox"][0][1])],
                        [max(current["bbox"][1][0], next_item["bbox"][1][0]),
                         max(current["bbox"][1][1], next_item["bbox"][1][1])]
                    ]
                    j += 1
                else:
                    break
                    
            merged.append(current)
            i = j
            
        return merged

    def detect_tables(self, image: np.ndarray) -> List[Dict]:
        """检测图像中的表格区域"""
        try:
            # 预处理
            processed_img = self.preprocess_image(image)
            pil_img = Image.fromarray(processed_img)
            
            # 表格检测
            inputs = self.processor(images=pil_img, return_tensors="pt").to(self.device)
            with torch.no_grad():
                outputs = self.table_model(**inputs)
                
            target_sizes = torch.tensor([pil_img.size[::-1]]).to(self.device)
            results = self.processor.post_process_object_detection(
                outputs, target_sizes=target_sizes, threshold=0.8)[0]
                
            tables = []
            for score, label, box in zip(results["scores"], results["labels"], results["boxes"]):
                if score < 0.8 or label != 1:  # 只处理表格(label=1)
                    continue
                    
                box = box.cpu().tolist()
                area = (box[2] - box[0]) * (box[3] - box[1])
                if area < self.config.min_table_area:
                    continue
                    
                tables.append({
                    "bbox": box,
                    "score": float(score)
                })
                
            return tables
            
        except RuntimeError as e:
            if "CUDA out of memory" in str(e) and self.config.gpu_fallback:
                logger.warning("GPU内存不足，尝试使用CPU模式")
                self.device = "cpu"
                self.table_model.to("cpu")
                return self.detect_tables(image)
            raise
        except Exception as e:
            logger.error(f"表格检测失败: {str(e)}", exc_info=True)
            return []

    def extract_table_content(self, image: np.ndarray, table_bbox: List[float]) -> List[List[Dict]]:
        """提取表格内容"""
        try:
            # 裁剪表格区域
            x1, y1, x2, y2 = map(int, table_bbox)
            table_img = image[y1:y2, x1:x2]
            
            # 检测单元格
            gray = cv2.cvtColor(table_img, cv2.COLOR_RGB2GRAY)
            thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]
            
            # 检测线条
            kernel_len = max(5, min(table_img.shape[:2]) // 20)
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kernel_len, 1))
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, kernel_len))
            
            horizontal = cv2.erode(thresh, horizontal_kernel, iterations=3)
            vertical = cv2.erode(thresh, vertical_kernel, iterations=3)
            
            # 合并线条
            mask = cv2.add(horizontal, vertical)
            contours = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)[0]
            
            # 提取单元格
            cells = []
            for cnt in contours:
                x, y, w, h = cv2.boundingRect(cnt)
                if w > 20 and h > 10:  # 过滤小噪点
                    # 添加边距
                    x = max(0, x - self.config.cell_padding)
                    y = max(0, y - self.config.cell_padding)
                    w = min(table_img.shape[1] - x, w + 2 * self.config.cell_padding)
                    h = min(table_img.shape[0] - y, h + 2 * self.config.cell_padding)
                    
                    cell_img = table_img[y:y+h, x:x+w]
                    cell_text = self.recognize_text(cell_img)
                    text = " ".join([t["text"] for t in cell_text]) if cell_text else ""
                    
                    cells.append({
                        "bbox": [x + x1, y + y1, x + x1 + w, y + y1 + h],  # 相对于原图的坐标
                        "text": text
                    })
            
            # 按行列组织单元格
            return self._organize_cells(cells)
            
        except Exception as e:
            logger.error(f"表格内容提取失败: {str(e)}", exc_info=True)
            return []

    def _organize_cells(self, cells: List[Dict]) -> List[List[Dict]]:
        """将单元格组织为行列结构"""
        if not cells:
            return []
            
        # 按Y坐标分组(行)
        cells.sort(key=lambda c: (c["bbox"][1], c["bbox"][0]))
        rows = []
        current_row = [cells[0]]
        
        for cell in cells[1:]:
            # 如果Y坐标相近，视为同一行
            if abs(cell["bbox"][1] - current_row[-1]["bbox"][1]) < 10:
                current_row.append(cell)
            else:
                rows.append(current_row)
                current_row = [cell]
                
        if current_row:
            rows.append(current_row)
            
        # 每行内按X坐标排序
        organized = []
        for row in rows:
            row.sort(key=lambda c: c["bbox"][0])
            organized.append(row)
            
        return organized

    def process_page(self, image: np.ndarray, page_num: int = 0) -> Dict[str, Any]:
        """
        处理单页图像
        
        Args:
            image: 输入图像(numpy数组)
            page_num: 页码(用于日志)
            
        Returns:
            包含文本和表格的识别结果
        """
        start_time = time.time()
        result = {
            "page": page_num + 1,
            "text": [],
            "tables": [],
            "status": "success",
            "time_elapsed": 0
        }
        
        try:
            logger.info(f"开始处理第 {page_num+1} 页 (尺寸: {image.shape})")
            
            # 1. 检测表格
            tables = self.detect_tables(image)
            logger.info(f"检测到 {len(tables)} 个表格")
            
            # 2. 创建非表格区域的掩码
            mask = np.ones(image.shape[:2], dtype=np.uint8) * 255
            for table in tables:
                x1, y1, x2, y2 = map(int, table["bbox"])
                cv2.rectangle(mask, (x1, y1), (x2, y2), 0, -1)
                
            # 3. 识别非表格文本
            non_table_img = cv2.bitwise_and(image, image, mask=mask)
            text_data = self.recognize_text(non_table_img)
            result["text"] = [t["text"] for t in text_data]
            
            # 4. 处理表格内容
            for table in tables:
                table_content = self.extract_table_content(image, table["bbox"])
                result["tables"].append({
                    "bbox": table["bbox"],
                    "content": table_content
                })
                
            # 5. 性能统计
            result["time_elapsed"] = time.time() - start_time
            logger.info(f"第 {page_num+1} 页处理完成 (耗时: {result['time_elapsed']:.2f}s)")
            
        except Exception as e:
            logger.error(f"第 {page_num+1} 页处理失败: {str(e)}", exc_info=True)
            result.update({
                "status": "failed",
                "error": str(e),
                "text": [],
                "tables": []
            })
            
        return result

    def process_batch(self, images: List[np.ndarray]) -> List[Dict[str, Any]]:
        """批量处理图像"""
        results = []
        for idx, img in enumerate(images):
            for retry in range(self.config.max_retry + 1):
                try:
                    result = self.process_page(img, idx)
                    results.append(result)
                    break
                except Exception as e:
                    if retry == self.config.max_retry:
                        logger.error(f"处理第 {idx+1} 页失败(已达最大重试次数)")
                        results.append({
                            "page": idx + 1,
                            "status": "failed",
                            "error": str(e)
                        })
                    else:
                        logger.warning(f"第 {idx+1} 页处理失败，重试 {retry+1}/{self.config.max_retry}")
                        
        return results
    
    async def process_image(self, image) -> Dict[str, Any]:
        """
        处理单张图片（保持原有接口不变）
        
        Args:
            image: 图片路径(str)或PIL Image对象或numpy数组
        
        Returns:
            Dict[str, Any]: {
                "text": List[str],       # 识别出的文本列表
                "status": str,          # "success"或"failed"
                "tables": List[Dict],   # 表格数据(新增)
                "error": Optional[str]  # 失败时的错误信息
            }
        """
        start_time = time.time()
        result = {
            "text": [],
            "tables": [],
            "status": "success",
            "error": None
        }

        try:
            # 1. 统一输入格式为numpy数组
            image_np = await self._convert_to_numpy(image)
            if image_np is None:
                raise ValueError("无法解析输入图像")

            # 2. 执行文本和表格识别
            page_result = self.process_page(image_np, 0)
            
            # 3. 保持输出兼容性
            result["text"] = page_result["text"]
            result["tables"] = page_result["tables"]
            
            if page_result["status"] != "success":
                result["status"] = "failed"
                result["error"] = page_result.get("error", "unknown error")

        except Exception as e:
            logger.error(f"图片处理失败: {str(e)}", exc_info=True)
            result.update({
                "status": "failed",
                "error": str(e),
                "text": [],
                "tables": []
            })
        
        logger.info(f"处理完成 (状态: {result['status']}, 耗时: {time.time()-start_time:.2f}s)")
        return result

    async def _convert_to_numpy(self, image_input) -> Optional[np.ndarray]:
        """将各种输入格式统一转换为numpy数组"""
        try:
            if isinstance(image_input, str):
                if not os.path.exists(image_input):
                    raise FileNotFoundError(f"图片路径不存在: {image_input}")
                img = cv2.imread(image_input)
                return cv2.cvtColor(img, cv2.COLOR_BGR2RGB) if img is not None else None
            
            elif isinstance(image_input, Image.Image):
                return np.array(image_input)
            
            elif isinstance(image_input, np.ndarray):
                # 确保是3通道RGB格式
                if len(image_input.shape) == 2:
                    return cv2.cvtColor(image_input, cv2.COLOR_GRAY2RGB)
                elif image_input.shape[2] == 4:  # RGBA转RGB
                    return cv2.cvtColor(image_input, cv2.COLOR_RGBA2RGB)
                return image_input
            
            raise ValueError(f"不支持的输入类型: {type(image_input)}")
        
        except Exception as e:
            logger.error(f"图像转换失败: {str(e)}")
            return None

# 使用示例
if __name__ == "__main__":
    # 初始化组件
    paddle_ocr = PaddleOCR(use_angle_cls=True, lang="ch")
    processor = DetrImageProcessor.from_pretrained("microsoft/table-transformer-detection")
    table_model = TableTransformerForObjectDetection.from_pretrained("microsoft/table-transformer-detection")
    
    # 创建处理器实例
    ocr_processor = OCRProcessor(
        ocr_engine=paddle_ocr,
        table_model=table_model,
        processor=processor
    )
    
    # 加载测试图像
    image_path = "test_table.png"
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 处理图像
    result = ocr_processor.process_page(image)
    
    # 打印结果
    print("识别文本:", result["text"])
    for i, table in enumerate(result["tables"]):
        print(f"\n表格 {i+1}:")
        for row in table["content"]:
            print(" | ".join([cell["text"] for cell in row]))
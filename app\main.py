import logging
import os
import tempfile
import uuid
import zipfile
import shutil
import chardet
import asyncio
import copy
import json
from contextlib import asynccontextmanager
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, Depends
from fastapi.responses import JSONResponse, FileResponse, HTMLResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi import APIRouter, Body
from typing import List, Dict, Any, Set
from fastapi import Security
from fastapi.security import APIKeyHeader, APIKeyQuery
from .config import ENABLE_DOCUMENT_CHECK
# 使用新的文件处理器工厂
from .file_processors import FileProcessor
from .content_processor import ContentProcessor
from .dependencies import set_ocr_initializer, get_ocr_initializer
from app.ocr.ocr_initializer import OCRInitializer
import hashlib
from .middleware.auth import JWTBearer, create_access_token
from .file_processors.util import Util
from datetime import datetime
from .field_handlers import field_handler,public_field_handler,manifest_field,field_handler_I,special_field_handler
from starlette.datastructures import UploadFile as StarletteUploadFile
from .db.connector import MySQLConnector
from .i_content_processor import i_data_check
from .custom_JSONResponse import CustomJSONResponse


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

API_KEYS = {
    "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0",
    "9213bb4d5173a88f821f4f99505dd94d3f7e0cbc7df514e45fb44c6aa4cae7e0",
    "0be1d6e89de34876c745a21a5009f498160f23d0c6678c837944505032dc9e7d"
}
API_KEY = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0"
timestamp = APIKeyHeader(name="timestamp", auto_error=False)
sign = APIKeyHeader(name="sign", auto_error=False)
async def get_api_key(
    timestamp: str = Security(timestamp),
    sign: str = Security(sign),
):
    string_sign_temp = f"{API_KEY}{timestamp}"
    # 计算签名
    calculated_signature = hashlib.md5(string_sign_temp.encode()).hexdigest().upper()
    if calculated_signature == sign:
        return sign
    raise HTTPException(
        status_code=401,
        detail="Invalid or missing API Key",
    )

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化
    init_success = True

    # 1. 初始化OCR资源
    try:
        initializer = OCRInitializer()
        # 添加调试信息
        set_ocr_initializer(initializer)
        app.state.ocr_initializer = initializer
        print("✅ OCR资源初始化完成")
    except Exception as e:
        init_success = False
        logging.error(f"❌ OCR初始化失败: {str(e)}", exc_info=True)
    # 2. 初始化MySQL连接池
    try:
        # MySQLConnector.initialize()
        app.state.mysql_initialized = True
        print("✅ MySQL连接池初始化完成")
    except Exception as e:
        init_success = False
        logging.error(f"❌ MySQL连接池初始化失败: {str(e)}", exc_info=True)
    
    if not init_success:
        raise RuntimeError("应用初始化失败，请检查日志")
    yield
    # 关闭时清理
    if hasattr(app.state, "ocr_initializer"):
        del app.state.ocr_initializer

app = FastAPI(
    title="外贸文档处理平台", 
    lifespan=lifespan,
    description="海关单据识别系统",
    version="1.0.0"
)

# 添加CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件目录配置
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# 在app = FastAPI(...)之后添加
app.dependency_overrides[get_ocr_initializer] = lambda: app.state.ocr_initializer

def detect_filename_encoding(filename: str) -> str:
    """检测文件名编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030']
    for encoding in encodings:
        try:
            filename.encode(encoding).decode(encoding)
            return encoding
        except UnicodeError:
            continue
    return 'utf-8'  # 默认使用 UTF-8

def normalize_filename(filename: str) -> str:
    """标准化文件名编码"""
    try:
        # 如果是bytes，先检测编码
        if isinstance(filename, bytes):
            detected = chardet.detect(filename)
            encoding = detected['encoding'] if detected['encoding'] else 'utf-8'
            filename = filename.decode(encoding)
        
        # 如果是字符串，确保是正确的编码
        encoding = detect_filename_encoding(filename)
        return filename.encode(encoding).decode(encoding)
    except Exception as e:
        logging.warning(f"文件名编码处理失败: {str(e)}")
        return filename

@app.get("/")
async def root():
    """根路径重定向到登录页"""
    return RedirectResponse(url="/static/login.html")

@app.get("/smart-fill", response_class=HTMLResponse)
async def smart_fill():
    """返回HTML页面"""
    with open("frontend/smart-fill.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return html_content


@app.get("/index-manifest", response_class=HTMLResponse)
async def index_manifest():
    """返回HTML页面"""
    with open("frontend/index-manifest.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return html_content
# 0603 增加处理舱单接口
@app.post("/upload-manifest")
async def upload_manifest(
    files: List[UploadFile] = File(...),
    model_provider: str = Form("doubao"),
    ocr_initializer: OCRInitializer = Depends(get_ocr_initializer),
    api_key: str = Depends(get_api_key)
):
    if not files:
        raise HTTPException(status_code=400, detail="未提供文件")
     # 创建临时目录
    results = []
    ocr_results = []  # 存储OCR结果
    format_results = []  # 存储格式化结果
    temp_dir = tempfile.mkdtemp()
    logging.info(f"创建临时目录: {temp_dir} (权限: {oct(os.stat(temp_dir).st_mode)})")

     # 用于存储所有需要处理的文件路径
    all_files_to_process = []
    
    try:
        # 先处理压缩文件
        for file in files:
            print("file:",file)
            # 处理文件名编码
            normalized_filename = normalize_filename(file.filename)
            
            if file.filename.lower().endswith(('.zip', '.rar')):
                # 保存压缩文件
                zip_path = os.path.join(temp_dir, normalized_filename)
                content = await file.read()
                with open(zip_path, 'wb') as f:
                    f.write(content)
                
                # 创建解压目录
                extract_dir = os.path.join(temp_dir, f"extracted_{os.path.splitext(normalized_filename)[0]}")
                os.makedirs(extract_dir, exist_ok=True)
                
                # 解压文件
                if normalized_filename.lower().endswith('.zip'):
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        # 获取所有文件列表
                        name_list = zip_ref.namelist()
                        
                        # 解压每个文件
                        for name in name_list:
                            try:
                                # 尝试不同的编码方式
                                for encoding in ['cp437', 'gbk', 'utf-8']:
                                    try:
                                        # 对于 cp437 编码，需要先转换
                                        if encoding == 'cp437':
                                            real_name = name.encode('cp437').decode('gbk')
                                        else:
                                            real_name = name.encode('cp437').decode(encoding)
                                            
                                        # 构建解压路径
                                        target_path = os.path.join(extract_dir, real_name)
                                        
                                        # 确保目标目录存在
                                        os.makedirs(os.path.dirname(target_path), exist_ok=True)
                                        
                                        # 解压文件
                                        with zip_ref.open(name) as source, open(target_path, 'wb') as target:
                                            shutil.copyfileobj(source, target)
                                            
                                        # 如果成功就跳出编码尝试循环
                                        break
                                    except UnicodeError:
                                        continue
                                    except Exception as e:
                                        logging.error(f"解压文件 {name} 失败: {str(e)}")
                            except Exception as e:
                                logging.error(f"处理文件 {name} 失败: {str(e)}")
                                continue
                else:  # .rar
                    # 如果需要处理rar文件，需要安装rarfile库
                    import rarfile
                    with rarfile.RarFile(zip_path, 'r') as rar_ref:
                        rar_ref.extractall(extract_dir)
                
                # 收集解压后的文件
                for root, _, filenames in os.walk(extract_dir):
                    # 跳过 __MACOSX 目录
                    if '__MACOSX' in root:
                        continue
                    for filename in filenames:
                        # 跳过隐藏文件和系统文件
                        if filename.startswith('.') or filename.startswith('__'):
                            continue
                        # 跳过临时文件
                        if filename.startswith('~$') or filename.endswith('.tmp'):
                            continue
                        # 处理文件名编码
                        normalized_name = normalize_filename(filename)
                        file_path = os.path.join(root, filename)
                        all_files_to_process.append({
                            "path": file_path,
                            "name": normalized_name
                        })
            else:
                # 非压缩文件直接保存
                file_path = os.path.join(temp_dir, normalized_filename)
                content = await file.read()
                with open(file_path, 'wb') as f:
                    f.write(content)
                all_files_to_process.append({
                    "path": file_path,
                    "name": normalized_filename
                })
        
        # 创建处理器
        processor = FileProcessor(ocr_initializer=ocr_initializer, model_provider=model_provider)
        
        print("一共上传了多少个文件：",len(all_files_to_process))

        # 并发处理所有文件
        tasks = [handle_file_manifest(processor,file_info) for file_info in all_files_to_process]
        all_results = await asyncio.gather(*tasks)
        # 遍历处理结果
        for i, result in enumerate(all_results):
            if not result:
                continue
            file_info = all_files_to_process[i]
            original_name = file_info["name"]

            if isinstance(result, dict):
                if "ocr_text" in result:
                    ocr_results.append({
                        "file_name": original_name,
                        "content": result["ocr_text"]
                    })
                if "formatted_content" in result:
                    for formatted_content in result["formatted_content"]:
                        format_results.append({
                            "file_name": original_name,
                            "content": formatted_content
                        })
                result["original_filename"] = original_name
                results.append(result)

            elif isinstance(result, list):
                for item in result:
                    if isinstance(item, dict):
                        item["original_filename"] = f"{original_name}:{item.get('file_path', '')}"
                        results.append(item)
            
    except Exception as e:
        logging.error(f"处理文件失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"处理文件失败: {str(e)}"}
        )
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            logging.error(f"清理临时文件失败: {str(e)}")
       
    # 合并去重前检查results是否为空
    if not results:
        return JSONResponse([])
    
    merged = await processor.merge_manifest_jsons(format_results)

    merged = manifest_field(merged)
    print(merged["content"])
    # 构建新的返回结构
    response_data = {
        "detail_ocr": ocr_results, #OCR识别结果
        "detail_format": format_results, #格式化结果
        "result": merged #最终的合并去重结果
    }
    
    return JSONResponse(content=response_data)

# 处理一个文件的方法
async def handle_file_manifest(processor,file_info):
    file_path = file_info["path"]
    original_name = file_info["name"]

    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return None

    logging.info(f"处理文件: {original_name} (大小: {os.path.getsize(file_path)} 字节)")

    try:
        return await processor.process_file_manifest(file_path)
    except Exception as e:
        logging.error(f"处理文件失败: {original_name}，错误: {str(e)}", exc_info=True)
        return None    

@app.post("/upload")
async def upload_files(
    files: List[UploadFile] = File(...),
    model_provider: str = Form("doubao"),
    ie_flag:str = Form("E"), # 进出口标识；I:进口，E:出口
    ocr_initializer: OCRInitializer = Depends(get_ocr_initializer),
    api_key: str = Depends(get_api_key),
    enable_processing: bool = Form(False),
    sys_rules:str = Form(None), # 特殊规则
    opt_unit_name:str = Form(None), # 境内收发货人
    dec_rules:str = Form(None) # 制单规则信息（json）
):
    print("sys_rules:",sys_rules)
    #0529 临时修改，默认使用doubao模型
    model_provider = "doubao"
    """上传并处理多个文件，返回合并后的结果"""
    if not files:
        raise HTTPException(status_code=400, detail="未提供文件")
    
    # 创建临时目录
    results = []
    ocr_results = []  # 存储OCR结果
    format_results = []  # 存储格式化结果
    temp_dir = tempfile.mkdtemp()
    logging.info(f"创建临时目录: {temp_dir} (权限: {oct(os.stat(temp_dir).st_mode)})")
    


    # 1. 创建按日期组织的目录结构
    current_time = datetime.now()
    date_dir = current_time.strftime("%Y-%m-%d")  # 日期目录 如：2023-11-15
    hour_dir = current_time.strftime("%H")       # 小时目录 如：14
    uuid_dir = str(uuid.uuid4())                 # 唯一UUID目录
    
    # 2. 构建完整保存路径
    base_save_dir = "uploads"  # 基础保存目录，可按需修改
    save_path = os.path.join(base_save_dir, ie_flag, date_dir, hour_dir, uuid_dir)
    
    # 3. 确保目录存在（不存在则创建）
    os.makedirs(save_path, exist_ok=True)
    
    # 4. 保存所有上传的文件
    saved_files = []
    for file in files:
        # 处理文件名（防止路径遍历安全问题）
        safe_filename = os.path.basename(file.filename)
        save_file_path = os.path.join(save_path, safe_filename)
        
        # 异步写入文件
        with open(save_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        saved_files.append(save_file_path)
    # 用于存储所有需要处理的文件路径
    all_files_to_process = []
    
    try:
        # 先处理压缩文件
        for file_path in saved_files:
            file = StarletteUploadFile(
                filename=os.path.basename(file_path),  # 原始文件名
                file=open(file_path, "rb")            # 重新打开文件
            )
            print("file:",file)
            # 处理文件名编码
            normalized_filename = normalize_filename(file.filename)
            
            if file.filename.lower().endswith(('.zip', '.rar')):
                # 保存压缩文件
                zip_path = os.path.join(temp_dir, normalized_filename)
                content = await file.read()
                with open(zip_path, 'wb') as f:
                    f.write(content)
                
                # 创建解压目录
                extract_dir = os.path.join(temp_dir, f"extracted_{os.path.splitext(normalized_filename)[0]}")
                os.makedirs(extract_dir, exist_ok=True)
                
                # 解压文件
                if normalized_filename.lower().endswith('.zip'):
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        # 获取所有文件列表
                        name_list = zip_ref.namelist()
                        
                        # 解压每个文件
                        for name in name_list:
                            try:
                                # 尝试不同的编码方式
                                for encoding in ['cp437', 'gbk', 'utf-8']:
                                    try:
                                        # 对于 cp437 编码，需要先转换
                                        if encoding == 'cp437':
                                            real_name = name.encode('cp437').decode('gbk')
                                        else:
                                            real_name = name.encode('cp437').decode(encoding)
                                            
                                        # 构建解压路径
                                        target_path = os.path.join(extract_dir, real_name)
                                        
                                        # 确保目标目录存在
                                        os.makedirs(os.path.dirname(target_path), exist_ok=True)
                                        
                                        # 解压文件
                                        with zip_ref.open(name) as source, open(target_path, 'wb') as target:
                                            shutil.copyfileobj(source, target)
                                            
                                        # 如果成功就跳出编码尝试循环
                                        break
                                    except UnicodeError:
                                        continue
                                    except Exception as e:
                                        logging.error(f"解压文件 {name} 失败: {str(e)}")
                            except Exception as e:
                                logging.error(f"处理文件 {name} 失败: {str(e)}")
                                continue
                else:  # .rar
                    # 如果需要处理rar文件，需要安装rarfile库
                    import rarfile
                    with rarfile.RarFile(zip_path, 'r') as rar_ref:
                        rar_ref.extractall(extract_dir)
                
                # 收集解压后的文件
                for root, _, filenames in os.walk(extract_dir):
                    # 跳过 __MACOSX 目录
                    if '__MACOSX' in root:
                        continue
                    for filename in filenames:
                        # 跳过隐藏文件和系统文件
                        if filename.startswith('.') or filename.startswith('__'):
                            continue
                        # 跳过临时文件
                        if filename.startswith('~$') or filename.endswith('.tmp'):
                            continue
                        # 处理文件名编码
                        normalized_name = normalize_filename(filename)
                        file_path = os.path.join(root, filename)
                        all_files_to_process.append({
                            "path": file_path,
                            "name": normalized_name
                        })
            else:
                # 非压缩文件直接保存
                file_path = os.path.join(temp_dir, normalized_filename)
                content = await file.read()
                with open(file_path, 'wb') as f:
                    f.write(content)
                all_files_to_process.append({
                    "path": file_path,
                    "name": normalized_filename
                })
        
        #0625 临时增加进口使用python处理
        # if ie_flag == "I":
        #     import time
        #     time.sleep(10)
        #     for file in all_files_to_process:
        #         file_data,merged = await i_data_check(file["path"],file["name"])
        #         # 构建新的返回结构
        #         response_data = {
        #             "detail_ocr": "", #OCR识别结果
        #             "detail_format": file_data, #格式化结果
        #             "result": merged, #最终的合并去重结果
        #             "result_logic": merged #最终的合并去重结果
        #         }

        #         # 返回之前，将结果存到本地
        #         json_file_path = os.path.join(save_path, "response.json")

        #         # Save the response_data to JSON file
        #         with open(json_file_path, 'w', encoding='utf-8') as f:
        #             json.dump(response_data, f, ensure_ascii=False, indent=4)
                
        #         return JSONResponse(content=response_data)


        # 创建处理器
        processor = FileProcessor(ocr_initializer=ocr_initializer, model_provider=model_provider)
        
        print("一共上传了多少个文件：",len(all_files_to_process))

        # 并发处理所有文件
        # 增加进出口判断
        tasks = [handle_file(processor,file_info,ie_flag) for file_info in all_files_to_process]
        all_results = await asyncio.gather(*tasks)
        # 遍历处理结果
        for i, result in enumerate(all_results):
            if not result:
                continue
            file_info = all_files_to_process[i]
            original_name = file_info["name"]

            if isinstance(result, dict):
                if "ocr_text" in result:
                    ocr_results.append({
                        "file_name": original_name,
                        "content": result["ocr_text"]
                    })
                if "formatted_content" in result:
                    for formatted_content in result["formatted_content"]:
                        format_results.append({
                            "file_name": original_name,
                            "content": formatted_content
                        })
                result["original_filename"] = original_name
                results.append(result)

            elif isinstance(result, list):
                for item in result:
                    if isinstance(item, dict):
                        item["original_filename"] = f"{original_name}:{item.get('file_path', '')}"
                        results.append(item)
        # 合并去重前检查results是否为空
        if not results:
            return CustomJSONResponse(
                code=500,
                message={"error": f"处理文件失败: {str(e)}"}
            )
        print("处理之前的格式化结果：",format_results)
        # 如果启用了文档检查，则对格式化后的内容使用大模型进行业务逻辑的校验
        if ENABLE_DOCUMENT_CHECK:
            # 使用大模型进行去重以及业务逻辑校验
            merged = await processor.merge_contents(format_results)
        else:
            merged = await processor.merge_jsons(format_results)
        print("处理之后的格式化结果：",format_results)
        # 处理公共字段
        merged = public_field_handler(merged,ie_flag,opt_unit_name)
        # 处理业务逻辑字段
        # 使用copy.deepcopy()
        print("原始 merged 的值:", merged)  # 检查是否为 None
        new_merged = copy.deepcopy(merged)
        print("处理业务逻辑字段之前的new_merged：",new_merged)
        if enable_processing:
            new_merged = await field_handler(new_merged)
            # 0707 增加特殊企业处理规则
            new_merged = await special_field_handler(new_merged,sys_rules)
        if ie_flag == "I":
            merged = await field_handler_I(merged)
    
        # 构建新的返回结构
        response_data = {
            "detail_ocr": ocr_results, #OCR识别结果
            "detail_format": format_results, #格式化结果
            "result": merged, #最终的合并去重结果
            "result_logic": new_merged #最终的合并去重结果
        }

        # 返回之前，将结果存到本地
        json_file_path = os.path.join(save_path, "response.json")

        # Save the response_data to JSON file
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(response_data, f, ensure_ascii=False, indent=4)
        
        return CustomJSONResponse(code=200,data=response_data)    
    except Exception as e:
        logging.error(f"处理文件失败: {str(e)}", exc_info=True)
        return CustomJSONResponse(
            code=500,
            message={"error": f"处理文件失败: {str(e)}"}
        )
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            logging.error(f"清理临时文件失败: {str(e)}")
       
    

# 处理一个文件的方法
async def handle_file(processor,file_info,ie_flag):
    file_path = file_info["path"]
    original_name = file_info["name"]

    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return None

    logging.info(f"处理文件: {original_name} (大小: {os.path.getsize(file_path)} 字节)")

    try:
        return await processor.process_file(file_path,ie_flag)
    except Exception as e:
        logging.error(f"处理文件失败: {original_name}，错误: {str(e)}", exc_info=True)
        return None
    

@app.get("/api/models")
async def get_models():
    """返回所有可用的AI模型"""
    from .config import get_available_models
    
    models = get_available_models()
    # 添加调试日志
    logging.info(f"获取可用模型: {models}")
    
    # 确保返回非空结果
    if not models:
        logging.warning("模型列表为空，返回默认模型")
        return JSONResponse(content={
            "ollama": {
                "name": "ollama",
                "description": "本地Ollama模型 (默认)",
                "max_tokens": 15000
            }
        })
    
    return JSONResponse(content=models) 

@app.post("/api/smart-fill")
async def smart_fill_endpoint(data: dict = Body(...),ocr_initializer: OCRInitializer = Depends(get_ocr_initializer),api_key: str = Depends(get_api_key)):
    """智能字段提取专用接口"""
    model_provider = "doubao"
    processor = FileProcessor(ocr_initializer=ocr_initializer, model_provider=model_provider)
    try:
        text = data.get("text", "")
        split_rules = data.get("split_rules", "")
        sbys_required = data.get("sbysRequired", "")
        if not text:
            return {"error": "请输入需要分析的文本内容"}
        if not split_rules:
            return {"error": "请输入字段拆分规则（用分号分隔）"}
        
        # 直接调用处理逻辑
        result = await processor.smart_fill(text, split_rules,sbys_required)
        return result
    except Exception as e:
        return {"error": str(e)} 
    
#增加新接口，用于文档JSON数据的校验，传入JSON List格式的数据，返回校验结果
@app.post("/api/verify")
async def verify_endpoint(data: dict = Body(...),ocr_initializer: OCRInitializer = Depends(get_ocr_initializer),api_key: str = Depends(get_api_key)):
    model_provider = "doubao"
    processor = FileProcessor(ocr_initializer=ocr_initializer, model_provider=model_provider)
    """文档JSON数据的校验"""
    detail_format_list = data.get("detail_format_list", [])

    #0516 在进行校验之前，先对json进行处理
    # 1、删除JSON中的file_name字段
    # 2、删除JSON中的空字段
    def remove_empty(data):
        """递归移除空值字段"""
        if isinstance(data, dict):
            return {
                k: remove_empty(v)
                for k, v in data.items()
                if v not in (None, "", [], {}) and remove_empty(v) not in (None, "", [], {})
            }
        elif isinstance(data, list):
            return [remove_empty(v) for v in data if v not in (None, "", [], {})]
        else:
            return data
    result_list = []
    for detail_format in detail_format_list:
        detail_format = remove_empty(detail_format)
        # del detail_format["file_name"]
        result_list.append(detail_format)

    # 并发处理所有文件
    tasks = [handle_json(processor,detail_format) for detail_format in result_list]
    all_results = await asyncio.gather(*tasks)
    #进行文件之间的逻辑校验
    result = await processor.verify_list_json(result_list)
    result["file_name"] = "校验结果"
    all_results.append(result)
    return all_results


# 处理一个文件的方法
async def handle_json(processor,detail_format):
    file_name = detail_format["file_name"]
    content = detail_format["content"]

    logging.info(f"处理文件: {file_name}")

    try:
        result = await processor.verify_json(content)
        result['file_name'] = file_name
        return result
    except Exception as e:
        logging.error(f"处理文件失败: {file_name}，错误: {str(e)}", exc_info=True)
        return None

# 添加登录路由
@app.post("/api/login")
async def login(data: dict = Body(...)):
    """用户登录"""
    username = data.get("username")
    password = data.get("password")
    
    if username == "admin" and password == "admin123":
        token = create_access_token({"sub": username})
        return {"token": token}
    else:
        return {"error": "用户名或密码错误"}

# 添加受保护的路由
@app.get("/api/protected")
async def protected_route(token: str = Depends(JWTBearer())):
    """受保护的测试路由"""
    return {"message": "访问成功"}
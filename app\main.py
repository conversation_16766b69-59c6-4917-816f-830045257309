import logging
import os
import tempfile
import uuid
import zipfile
import shutil
import chardet
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, Depends
from fastapi.responses import JSONResponse, FileResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi import APIRouter, Body
from typing import List, Dict, Any, Set
from .config import ENABLE_DOCUMENT_CHECK
# 使用新的文件处理器工厂
from .file_processors import FileProcessor
from .content_processor import ContentProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from .dependencies import set_ocr_initializer, get_ocr_initializer
from app.ocr.ocr_initializer import OCRInitializer

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化
    try:
        initializer = OCRInitializer()
        # 添加调试信息
        print(f"OCRInitializer类型: {type(initializer)}")
        print(f"poppler_path属性: {hasattr(initializer, 'poppler_path')}")
        set_ocr_initializer(initializer)
        app.state.ocr_initializer = initializer
        print("✅ OCR资源初始化完成")
    except Exception as e:
        print(f"❌ OCR初始化失败: {str(e)}")
        raise
    yield
    # 关闭时清理
    if hasattr(app.state, "ocr_initializer"):
        del app.state.ocr_initializer

app = FastAPI(title="外贸文档处理平台", lifespan=lifespan)

# 添加CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件目录配置
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# 在app = FastAPI(...)之后添加
app.dependency_overrides[get_ocr_initializer] = lambda: app.state.ocr_initializer

def detect_filename_encoding(filename: str) -> str:
    """检测文件名编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030']
    for encoding in encodings:
        try:
            filename.encode(encoding).decode(encoding)
            return encoding
        except UnicodeError:
            continue
    return 'utf-8'  # 默认使用 UTF-8

def normalize_filename(filename: str) -> str:
    """标准化文件名编码"""
    try:
        # 如果是bytes，先检测编码
        if isinstance(filename, bytes):
            detected = chardet.detect(filename)
            encoding = detected['encoding'] if detected['encoding'] else 'utf-8'
            filename = filename.decode(encoding)
        
        # 如果是字符串，确保是正确的编码
        encoding = detect_filename_encoding(filename)
        return filename.encode(encoding).decode(encoding)
    except Exception as e:
        logging.warning(f"文件名编码处理失败: {str(e)}")
        return filename

@app.get("/", response_class=HTMLResponse)
async def root():
    """返回HTML页面"""
    with open("frontend/index.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return html_content

@app.get("/smart-fill", response_class=HTMLResponse)
async def smart_fill():
    """返回HTML页面"""
    with open("frontend/smart-fill.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return html_content

@app.post("/upload")
async def upload_files(
    files: List[UploadFile] = File(...),
    model_provider: str = Form("doubao"),
    ocr_initializer: OCRInitializer = Depends(get_ocr_initializer)
):
    """上传并处理多个文件，返回合并后的结果"""
    if not files:
        raise HTTPException(status_code=400, detail="未提供文件")
    
    # 创建临时目录
    results = []
    ocr_results = []  # 存储OCR结果
    format_results = []  # 存储格式化结果
    temp_dir = tempfile.mkdtemp()
    logging.info(f"创建临时目录: {temp_dir} (权限: {oct(os.stat(temp_dir).st_mode)})")
    
    # 用于存储所有需要处理的文件路径
    all_files_to_process = []
    
    try:
        # 先处理压缩文件
        for file in files:
            # 处理文件名编码
            normalized_filename = normalize_filename(file.filename)
            
            if file.filename.lower().endswith(('.zip', '.rar')):
                # 保存压缩文件
                zip_path = os.path.join(temp_dir, normalized_filename)
                content = await file.read()
                with open(zip_path, 'wb') as f:
                    f.write(content)
                
                # 创建解压目录
                extract_dir = os.path.join(temp_dir, f"extracted_{os.path.splitext(normalized_filename)[0]}")
                os.makedirs(extract_dir, exist_ok=True)
                
                # 解压文件
                if normalized_filename.lower().endswith('.zip'):
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        # 获取所有文件列表
                        name_list = zip_ref.namelist()
                        
                        # 解压每个文件
                        for name in name_list:
                            try:
                                # 尝试不同的编码方式
                                for encoding in ['cp437', 'gbk', 'utf-8']:
                                    try:
                                        # 对于 cp437 编码，需要先转换
                                        if encoding == 'cp437':
                                            real_name = name.encode('cp437').decode('gbk')
                                        else:
                                            real_name = name.encode('cp437').decode(encoding)
                                            
                                        # 构建解压路径
                                        target_path = os.path.join(extract_dir, real_name)
                                        
                                        # 确保目标目录存在
                                        os.makedirs(os.path.dirname(target_path), exist_ok=True)
                                        
                                        # 解压文件
                                        with zip_ref.open(name) as source, open(target_path, 'wb') as target:
                                            shutil.copyfileobj(source, target)
                                            
                                        # 如果成功就跳出编码尝试循环
                                        break
                                    except UnicodeError:
                                        continue
                                    except Exception as e:
                                        logging.error(f"解压文件 {name} 失败: {str(e)}")
                            except Exception as e:
                                logging.error(f"处理文件 {name} 失败: {str(e)}")
                                continue
                else:  # .rar
                    # 如果需要处理rar文件，需要安装rarfile库
                    import rarfile
                    with rarfile.RarFile(zip_path, 'r') as rar_ref:
                        rar_ref.extractall(extract_dir)
                
                # 收集解压后的文件
                for root, _, filenames in os.walk(extract_dir):
                    # 跳过 __MACOSX 目录
                    if '__MACOSX' in root:
                        continue
                    for filename in filenames:
                        # 跳过隐藏文件和系统文件
                        if filename.startswith('.') or filename.startswith('__'):
                            continue
                        # 跳过临时文件
                        if filename.startswith('~$') or filename.endswith('.tmp'):
                            continue
                        # 处理文件名编码
                        normalized_name = normalize_filename(filename)
                        file_path = os.path.join(root, filename)
                        all_files_to_process.append({
                            "path": file_path,
                            "name": normalized_name
                        })
            else:
                # 非压缩文件直接保存
                file_path = os.path.join(temp_dir, normalized_filename)
                content = await file.read()
                with open(file_path, 'wb') as f:
                    f.write(content)
                all_files_to_process.append({
                    "path": file_path,
                    "name": normalized_filename
                })
        
        # 创建处理器
        print("传入的模型是：", model_provider)
        processor = FileProcessor(ocr_initializer=ocr_initializer, model_provider=model_provider)
        


        # 并发处理所有文件
        tasks = [handle_file(processor,file_info) for file_info in all_files_to_process]
        all_results = await asyncio.gather(*tasks)
        # 遍历处理结果
        for i, result in enumerate(all_results):
            if not result:
                continue
            file_info = all_files_to_process[i]
            original_name = file_info["name"]

            if isinstance(result, dict):
                if "ocr_text" in result:
                    ocr_results.append({
                        "file_name": original_name,
                        "content": result["ocr_text"]
                    })
                if "formatted_content" in result:
                    format_results.append({
                        "file_name": original_name,
                        "content": result["formatted_content"]
                    })
                result["original_filename"] = original_name
                results.append(result)

            elif isinstance(result, list):
                for item in result:
                    if isinstance(item, dict):
                        item["original_filename"] = f"{original_name}:{item.get('file_path', '')}"
                        results.append(item)
            
            

        '''
        # 处理所有文件
        for file_info in all_files_to_process:
            file_path = file_info["path"]
            original_name = file_info["name"]
            
            if os.path.exists(file_path):
                logging.info(f"处理文件: {original_name} (大小: {os.path.getsize(file_path)} 字节)")
            else:
                logging.error(f"文件不存在: {file_path}")
                continue
            
            # 处理文件，包括文件内容识别、格式化、业务逻辑校验
            result = await processor.process_file(file_path)
            
            # 确保结果不为None
            if result is None:
                logging.warning(f"文件处理返回None: {original_name}")
                continue
                
            # 添加原始文件名
            if isinstance(result, dict):
                # 保存OCR和格式化结果
                if "ocr_text" in result:
                    ocr_results.append({
                        "file_name": original_name,
                        "content": result["ocr_text"]
                    })
                if "formatted_content" in result:
                    format_results.append({
                        "file_name": original_name,
                        "content": result["formatted_content"]
                    })
                result["original_filename"] = original_name
                results.append(result)
            elif isinstance(result, list):
                for item in result:
                    if isinstance(item, dict):
                        item["original_filename"] = f"{original_name}:{item.get('file_path', '')}"
                        results.append(item)
        '''
    except Exception as e:
        logging.error(f"处理文件失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"处理文件失败: {str(e)}"}
        )
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            logging.error(f"清理临时文件失败: {str(e)}")
    
    #print("results:",results)    
    # 合并去重前检查results是否为空
    if not results:
        return JSONResponse([])
    
    # 如果启用了文档检查，则对格式化后的内容使用大模型进行业务逻辑的校验
    if ENABLE_DOCUMENT_CHECK:
        # 使用大模型进行去重以及业务逻辑校验
        print("format_results:",format_results)
        merged = await processor.merge_contents(format_results)
    else:
        merged = await processor.merge_jsons(format_results)

    # 构建新的返回结构
    response_data = {
        "detail_ocr": ocr_results, #OCR识别结果
        "detail_format": format_results, #格式化结果
        "result": merged #最终的合并去重结果
    }
    
    return JSONResponse(content=response_data)

# 处理一个文件的方法
async def handle_file(processor,file_info):
    file_path = file_info["path"]
    original_name = file_info["name"]

    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return None

    logging.info(f"处理文件: {original_name} (大小: {os.path.getsize(file_path)} 字节)")

    try:
        return await processor.process_file(file_path)
    except Exception as e:
        logging.error(f"处理文件失败: {original_name}，错误: {str(e)}", exc_info=True)
        return None

@app.get("/api/models")
async def get_models():
    """返回所有可用的AI模型"""
    from .config import get_available_models
    
    models = get_available_models()
    # 添加调试日志
    logging.info(f"获取可用模型: {models}")
    
    # 确保返回非空结果
    if not models:
        logging.warning("模型列表为空，返回默认模型")
        return JSONResponse(content={
            "ollama": {
                "name": "ollama",
                "description": "本地Ollama模型 (默认)",
                "max_tokens": 15000
            }
        })
    
    return JSONResponse(content=models) 

@app.post("/api/smart-fill")
async def smart_fill_endpoint(data: dict = Body(...),ocr_initializer: OCRInitializer = Depends(get_ocr_initializer)):
    """智能字段提取专用接口"""
    model_provider = "doubao"
    print("传入的模型是：", model_provider)
    processor = FileProcessor(ocr_initializer=ocr_initializer, model_provider=model_provider)
    try:
        text = data.get("text", "")
        if not text:
            return {"error": "请输入有效文本"}
            
        # 直接调用处理逻辑
        result = await processor.smart_fill(text)
        return result
    except Exception as e:
        return {"error": str(e)} 
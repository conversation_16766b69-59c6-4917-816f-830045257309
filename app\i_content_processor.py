
import openpyxl
import re
from decimal import Decimal, ROUND_HALF_UP
import pandas as pd

def country_change(input_text):
    input_text = "".join(input_text.split()).replace("'","")
    input_text = re.sub(r"[^\w\u4e00-\u9fff]", "", input_text)  # 移除非字母、数字、中文
    input_text = re.sub(r"[^\u4e00-\u9fa5a-zA-Z]", "", input_text)

    country = {
        "阿鲁巴":"ABW",
        "阿富汗":"AFG",
        "安哥拉":"AGO",
        "安圭拉":"AIA",
        "奥兰群岛":"ALA",
        "阿尔巴尼亚":"ALB",
        "安道尔":"AND",
        "阿联酋":"ARE",
        "阿根廷":"ARG",
        "亚美尼亚":"ARM",
        "美属萨摩亚":"ASM",
        "南极洲":"ATA",
        "法属南方领地":"ATF",
        "安提瓜和巴布达":"ATG",
        "澳大利亚":"AUS",
        "奥地利":"AUT",
        "阿塞拜疆":"AZE",
        "布隆迪":"BDI",
        "比利时":"BEL",
        "贝宁":"BEN",
        "博纳尔，圣俄斯塔休斯和萨巴":"BES",
        "布基纳法索":"BFA",
        "孟加拉国":"BGD",
        "保加利亚":"BGR",
        "巴林":"BHR",
        "巴哈马":"BHS",
        "波斯尼亚和黑塞哥维那":"BIH",
        "圣巴泰勒米":"BLM",
        "白俄罗斯":"BLR",
        "伯利兹":"BLZ",
        "百慕大":"BMU",
        "玻利维亚":"BOL",
        "巴西":"BRA",
        "巴巴多斯":"BRB",
        "文莱":"BRN",
        "不丹":"BTN",
        "布维岛":"BVT",
        "博茨瓦纳":"BWA",
        "中非":"CAF",
        "加那利群岛":"CAI",
        "加拿大":"CAN",
        "科科斯（基林）群岛":"CCK",
        "塞卜泰（休达）":"CEU",
        "瑞士":"CHE",
        "智利":"CHL",
        "中国":"CHN",
        "科特迪瓦":"CIV",
        "喀麦隆":"CMR",
        "刚果民主共和国":"COD",
        "刚果共和国":"COG",
        "库克群岛":"COK",
        "哥伦比亚":"COL",
        "科摩罗":"COM",
        "佛得角":"CPV",
        "哥斯达黎加":"CRI",
        "古巴":"CUB",
        "库拉索":"CUW",
        "圣诞岛":"CXR",
        "开曼群岛":"CYM",
        "塞浦路斯":"CYP",
        "捷克":"CZE",
        "德国":"DEU",
        "吉布提":"DJI",
        "多米尼克":"DMA",
        "丹麦":"DNK",
        "多米尼加":"DOM",
        "阿尔及利亚":"DZA",
        "厄瓜多尔":"ECU",
        "埃及":"EGY",
        "厄立特里亚":"ERI",
        "西撒哈拉":"ESH",
        "西班牙":"ESP",
        "爱沙尼亚":"EST",
        "埃塞俄比亚":"ETH",
        "芬兰":"FIN",
        "斐济":"FJI",
        "福克兰群岛（马尔维纳斯）":"FLK",
        "法国":"FRA",
        "法罗群岛":"FRO",
        "密克罗尼西亚联邦":"FSM",
        "加蓬":"GAB",
        "盖比群岛":"GAM",
        "英国":"GBR",
        "格鲁吉亚":"GEO",
        "格恩西":"GGY",
        "加纳":"GHA",
        "直布罗陀":"GIB",
        "几内亚":"GIN",
        "瓜德罗普":"GLP",
        "冈比亚":"GMB",
        "几内亚比绍":"GNB",
        "赤道几内亚":"GNQ",
        "希腊":"GRC",
        "格林纳达":"GRD",
        "格陵兰":"GRL",
        "危地马拉":"GTM",
        "法属圭亚那":"GUF",
        "关岛":"GUM",
        "圭亚那":"GUY",
        "中国香港":"HKG",
        "赫德岛和麦克唐纳岛":"HMD",
        "洪都拉斯":"HND",
        "原产地不明（按相关成员最高税率）":"HRA",
        "原产地不明（按所有成员最高税率）":"HRB",
        "克罗地亚":"HRV",
        "海地":"HTI",
        "匈牙利":"HUN",
        "印度尼西亚":"IDN",
        "马恩岛":"IMN",
        "印度":"IND",
        "英属印度洋领地":"IOT",
        "爱尔兰":"IRL",
        "伊朗":"IRN",
        "伊拉克":"IRQ",
        "冰岛":"ISL",
        "以色列":"ISR",
        "意大利":"ITA",
        "牙买加":"JAM",
        "泽西":"JEY",
        "约旦":"JOR",
        "日本":"JPN",
        "哈萨克斯坦":"KAZ",
        "肯尼亚":"KEN",
        "吉尔吉斯斯坦":"KGZ",
        "柬埔寨":"KHM",
        "基里巴斯":"KIR",
        "圣基茨和尼维斯":"KNA",
        "韩国":"KOR",
        "科威特":"KWT",
        "老挝":"LAO",
        "黎巴嫩":"LBN",
        "利比里亚":"LBR",
        "利比亚":"LBY",
        "圣卢西亚":"LCA",
        "列支敦士登":"LIE",
        "斯里兰卡":"LKA",
        "莱索托":"LSO",
        "立陶宛":"LTU",
        "卢森堡":"LUX",
        "拉脱维亚":"LVA",
        "中国澳门":"MAC",
        "法属圣马丁":"MAF",
        "马克萨斯群岛":"MAI",
        "摩洛哥":"MAR",
        "摩纳哥":"MCO",
        "摩尔多瓦":"MDA",
        "马达加斯加":"MDG",
        "马尔代夫":"MDV",
        "梅利利亚":"MEL",
        "墨西哥":"MEX",
        "马绍尔群岛":"MHL",
        "北马其顿":"MKD",
        "马里":"MLI",
        "马耳他":"MLT",
        "缅甸":"MMR",
        "黑山":"MNE",
        "蒙古":"MNG",
        "北马里亚纳群岛":"MNP",
        "莫桑比克":"MOZ",
        "毛里塔尼亚":"MRT",
        "蒙特塞拉特":"MSR",
        "马提尼克":"MTQ",
        "毛里求斯":"MUS",
        "马拉维":"MWI",
        "马来西亚":"MYS",
        "马约特":"MYT",
        "纳米比亚":"NAM",
        "新喀里多尼亚":"NCL",
        "尼日尔":"NER",
        "诺福克岛":"NFK",
        "尼日利亚":"NGA",
        "尼加拉瓜":"NIC",
        "纽埃":"NIU",
        "荷兰":"NLD",
        "挪威":"NOR",
        "尼泊尔":"NPL",
        "瑙鲁":"NRU",
        "新西兰":"NZL",
        "阿曼":"OMN",
        "巴基斯坦":"PAK",
        "巴拿马":"PAN",
        "皮特凯恩":"PCN",
        "秘鲁":"PER",
        "菲律宾":"PHL",
        "帕劳":"PLW",
        "巴布亚新几内亚":"PNG",
        "波兰":"POL",
        "波多黎各":"PRI",
        "朝鲜":"PRK",
        "葡萄牙":"PRT",
        "巴拉圭":"PRY",
        "巴勒斯坦":"PSE",
        "法属波利尼西亚":"PYF",
        "卡塔尔":"QAT",
        "留尼汪":"REU",
        "罗马尼亚":"ROU",
        "俄罗斯":"RUS",
        "卢旺达":"RWA",
        "沙特阿拉伯":"SAU",
        "苏丹":"SDN",
        "塞内加尔":"SEN",
        "新加坡":"SGP",
        "南乔治亚岛和南桑德韦奇岛":"SGS",
        "圣赫勒拿":"SHN",
        "斯瓦尔巴群岛和扬马延岛":"SJM",
        "所罗门群岛":"SLB",
        "塞拉利昂":"SLE",
        "萨尔瓦多":"SLV",
        "圣马力诺":"SMR",
        "社会群岛":"SOC",
        "索马里":"SOM",
        "圣皮埃尔和密克隆":"SPM",
        "塞尔维亚":"SRB",
        "南苏丹":"SSD",
        "圣多美和普林西比":"STP",
        "苏里南":"SUR",
        "斯洛伐克":"SVK",
        "斯洛文尼亚":"SVN",
        "瑞典":"SWE",
        "斯威士兰":"SWZ",
        "荷属圣马丁":"SXM",
        "塞舌尔":"SYC",
        "叙利亚":"SYR",
        "特克斯和凯科斯群岛":"TCA",
        "乍得":"TCD",
        "多哥":"TGO",
        "泰国":"THA",
        "塔吉克斯坦":"TJK",
        "托克劳":"TKL",
        "土库曼斯坦":"TKM",
        "东帝汶":"TLS",
        "汤加":"TON",
        "特立尼达和多巴哥":"TTO",
        "土阿莫土群岛":"TUA",
        "土布艾群岛":"TUB",
        "突尼斯":"TUN",
        "土耳其":"TUR",
        "图瓦卢":"TUV",
        "中国台湾":"TWN",
        "坦桑尼亚":"TZA",
        "乌干达":"UGA",
        "乌克兰":"UKR",
        "美国本土外小岛屿":"UMI",
        "乌拉圭":"URY",
        "美国":"USA",
        "乌兹别克斯坦":"UZB",
        "梵蒂冈":"VAT",
        "圣文森特和格林纳丁斯":"VCT",
        "委内瑞拉":"VEN",
        "英属维尔京群岛":"VGB",
        "美属维尔京群岛":"VIR",
        "越南":"VNM",
        "瓦努阿图":"VUT",
        "瓦利斯和富图纳":"WLF",
        "萨摩亚":"WSM",
        "也门":"YEM",
        "南非":"ZAF",
        "非洲其他国家(地区)":"ZAO",
        "亚洲其他国家(地区)":"ZAS",
        "欧洲其他国家(地区)":"ZEU",
        "赞比亚":"ZMB",
        "北美洲其他国家(地区)":"ZNA",
        "大洋洲其他国家(地区)":"ZOC",
        "拉丁美洲其他国家(地区)":"ZSA",
        "联合国及机构和国际组织":"ZUN",
        "津巴布韦":"ZWE",
        "国家（地区）不明":"ZZZ",
        "AD":"AND",
        "AE":"ARE",
        "AF":"AFG",
        "AG":"ATG",
        "AI":"AIA",
        "AL":"ALB",
        "AM":"ARM",
        "AO":"AGO",
        "AQ":"ATA",
        "AR":"ARG",
        "AS":"ASM",
        "AT":"AUT",
        "AU":"AUS",
        "AW":"ABW",
        "AX":"ALA",
        "AZ":"AZE",
        "BA":"BIH",
        "BB":"BRB",
        "BD":"BGD",
        "BE":"BEL",
        "BF":"BFA",
        "BG":"BGR",
        "BH":"BHR",
        "BI":"BDI",
        "BJ":"BEN",
        "BL":"BLM",
        "BM":"BMU",
        "BN":"BRN",
        "BO":"BOL",
        "BQ":"BES",
        "BR":"BRA",
        "BS":"BHS",
        "BT":"BTN",
        "BV":"BVT",
        "BW":"BWA",
        "BY":"BLR",
        "BZ":"BLZ",
        "CA":"CAN",
        "CC":"CCK",
        "CF":"CAF",
        "CH":"CHE",
        "CL":"CHL",
        "CM":"CMR",
        "CO":"COL",
        "CR":"CRI",
        "CU":"CUB",
        "CV":"CPV",
        "CX":"CXR",
        "CY":"CYP",
        "CZ":"CZE",
        "DE":"DEU",
        "DJ":"DJI",
        "DK":"DNK",
        "DM":"DMA",
        "DO":"DOM",
        "DZ":"DZA",
        "EC":"ECU",
        "EE":"EST",
        "EG":"EGY",
        "EH":"ESH",
        "ER":"ERI",
        "ES":"ESP",
        "FI":"FIN",
        "FJ":"FJI",
        "FK":"FLK",
        "FM":"FSM",
        "FO":"FRO",
        "FR":"FRA",
        "GA":"GAB",
        "GD":"GRD",
        "GE":"GEO",
        "GF":"GUF",
        "GH":"GHA",
        "GI":"GIB",
        "GL":"GRL",
        "GN":"GIN",
        "GP":"GLP",
        "GQ":"GNQ",
        "GR":"GRC",
        "GS":"SGS",
        "GT":"GTM",
        "GU":"GUM",
        "GW":"GNB",
        "GY":"GUY",
        "HK":"HKG",
        "HM":"HMD",
        "HN":"HND",
        "HR":"HRV",
        "HT":"HTI",
        "HU":"HUN",
        "ID":"IDN",
        "IE":"IRL",
        "IL":"ISR",
        "IM":"IMN",
        "IN":"IND",
        "IO":"IOT",
        "IQ":"IRQ",
        "IR":"IRN",
        "IS":"ISL",
        "IT":"ITA",
        "JE":"JEY",
        "JM":"JAM",
        "JO":"JOR",
        "JP":"JPN",
        "KH":"KHM",
        "KI":"KIR",
        "KM":"COM",
        "KW":"KWT",
        "KY":"CYM",
        "LB":"LBN",
        "LI":"LIE",
        "LK":"LKA",
        "LR":"LBR",
        "LS":"LSO",
        "LT":"LTU",
        "LU":"LUX",
        "LV":"LVA",
        "LY":"LBY",
        "MA":"MAR",
        "MC":"MCO",
        "MD":"MDA",
        "ME":"MNE",
        "MF":"MAF",
        "MG":"MDG",
        "MH":"MHL",
        "MK":"MKD",
        "ML":"MLI",
        "MM":"MMR",
        "MO":"MAC",
        "MQ":"MTQ",
        "MR":"MRT",
        "MS":"MSR",
        "MT":"MLT",
        "MV":"MDV",
        "MW":"MWI",
        "MX":"MEX",
        "MY":"MYS",
        "NA":"NAM",
        "NE":"NER",
        "NF":"NFK",
        "NG":"NGA",
        "NI":"NIC",
        "NL":"NLD",
        "NO":"NOR",
        "NP":"NPL",
        "NR":"NRU",
        "OM":"OMN",
        "PA":"PAN",
        "PE":"PER",
        "PF":"PYF",
        "PG":"PNG",
        "PH":"PHL",
        "PK":"PAK",
        "PL":"POL",
        "PN":"PCN",
        "PR":"PRI",
        "PS":"PSE",
        "PW":"PLW",
        "PY":"PRY",
        "QA":"QAT",
        "RE":"REU",
        "RO":"ROU",
        "RS":"SRB",
        "RU":"RUS",
        "RW":"RWA",
        "SB":"SLB",
        "SC":"SYC",
        "SD":"SDN",
        "SE":"SWE",
        "SG":"SGP",
        "SI":"SVN",
        "SJ":"SJM",
        "SK":"SVK",
        "SL":"SLE",
        "SM":"SMR",
        "SN":"SEN",
        "SO":"SOM",
        "SR":"SUR",
        "SS":"SSD",
        "ST":"STP",
        "SV":"SLV",
        "SY":"SYR",
        "SZ":"SWZ",
        "TC":"TCA",
        "TD":"TCD",
        "TG":"TGO",
        "TH":"THA",
        "TK":"TKL",
        "TL":"TLS",
        "TN":"TUN",
        "TO":"TON",
        "TR":"TUR",
        "TV":"TUV",
        "TZ":"TZA",
        "UA":"UKR",
        "UG":"UGA",
        "US":"USA",
        "UY":"URY",
        "VA":"VAT",
        "VE":"VEN",
        "VG":"VGB",
        "VI":"VIR",
        "VN":"VNM",
        "WF":"WLF",
        "WS":"WSM",
        "YE":"YEM",
        "YT":"MYT",
        "ZA":"ZAF",
        "ZM":"ZMB",
        "ZW":"ZWE",
        "CN":"CHN",
        "CG":"COG",
        "CD":"COD",
        "MZ":"MOZ",
        "GG":"GGY",
        "GM":"GMB",
        "MP":"MNP",
        "ET":"ETH",
        "NC":"NCL",
        "VU":"VUT",
        "TF":"ATF",
        "NU":"NIU",
        "UM":"UMI",
        "CK":"COK",
        "GB":"GBR",
        "TT":"TTO",
        "VC":"VCT",
        "TW":"TWN",
        "NZ":"NZL",
        "SA":"SAU",
        "LA":"LAO",
        "KP":"PRK",
        "KR":"KOR",
        "PT":"PRT",
        "KG":"KGZ",
        "KZ":"KAZ",
        "TJ":"TJK",
        "TM":"TKM",
        "UZ":"UZB",
        "KN":"KNA",
        "PM":"SPM",
        "SH":"SHN",
        "LC":"LCA",
        "MU":"MUS",
        "CI":"CIV",
        "KE":"KEN",
        "MN":"MNG"
    }
    for key, value in country.items():
        if key == input_text or value == input_text:
            return value
    return input_text  # 如果没有匹配到国家代码或名称，返回原始输入
def currency_change(input_text):
    currency = {
        "AED":"阿联酋迪拉姆",
        "AUD":"澳大利亚元",
        "CAD":"加拿大元",
        "CHF":"瑞士法郎",
        "CNY":"人民币",
        "DKK":"丹麦克朗",
        "EUR":"欧元",
        "GBP":"英镑",
        "HKD":"港币",
        "HUF":"匈牙利福林",
        "IDR":"印度尼西亚卢比",
        "JPY":"日本元",
        "KHR":"柬埔寨瑞尔",
        "KRW":"韩国圆",
        "KZT":"哈萨克斯坦竖戈",
        "MNT":"蒙古图格里克",
        "MOP":"澳门元",
        "MXN":"墨西哥比索",
        "MYR":"马来西亚林吉特",
        "NOK":"挪威克朗",
        "NZD":"新西兰元",
        "PLN":"波兰兹罗提",
        "RUB":"俄罗斯卢布",
        "SAR":"沙特里亚尔",
        "SEK":"瑞典克朗",
        "SGD":"新加坡元",
        "THB":"泰国铢",
        "TRY":"土耳其里拉",
        "USD":"美元",
        "ZAR":"南非兰特"
    }
    for key, value in currency.items():
        if key == input_text or value == input_text:
            return key
    return input_text  # 如果没有匹配到货币代码或符号，返回原始输入
def extract_currency_from_headers(df):
    results = {
        'invoice_no': None,
        'invoice_date': None,
        'currency': None
    }
    """从表头字段提取币制（如EUR/USD/CNY）"""
    currency_pattern = r"""
        (?:PRICE|VALUE|AMOUNT)\s*   # 匹配PRICE或VALUE关键字
        \(([A-Z]{3})\)       # 捕获括号内的3个大写字母
    """
    print("正在提取币制...:",df.columns)
    for col in df.columns:
        col_str = str(col).strip()
        match = re.search(currency_pattern, col_str, re.X | re.I)
        if match:
            currency = match.group(1).upper()
            results['currency']= currency
    
    # 遍历所有单元格
    # 1. 先尝试按列名匹配
    header_row = df.iloc[0]
    invoice_col = None
    date_col = None
    
    # 正则模式（匹配标签）
    invoice_pattern = re.compile(r'(?:发票号|Invoice\s*No\.?|INVOICE\s*NO[:：]?)', re.IGNORECASE)
    date_pattern = re.compile(r'(?:日期|Date|DATE[:：]?)', re.IGNORECASE)
    
    for i in range(min(len(df), 10)):  # 前10行
        for j in range(len(df.columns)):
            cell_value = str(df.iat[i, j]).strip()
            cleaned_value = "".join(cell_value.split()).replace(":", "").replace("：", "")
            
            # 检查是否是发票号标签
            if invoice_pattern.search(cleaned_value) and not results['invoice_no']:
                # 尝试取右侧单元格的值
                if j + 1 < len(df.columns):
                    invoice_no = str(df.iat[i, j + 1]).strip()
                    if invoice_no and invoice_no.lower() not in ["nan", "none", ""]:
                        results['invoice_no'] = invoice_no
                # 如果右侧无值，尝试下方单元格
                elif i + 1 < len(df):
                    invoice_no = str(df.iat[i + 1, j]).strip()
                    if invoice_no and invoice_no.lower() not in ["nan", "none", ""]:
                        results['invoice_no'] = invoice_no
            
            # 检查是否是日期标签
            if date_pattern.search(cleaned_value) and not results['invoice_date']:
                # 尝试取右侧单元格的值
                if j + 1 < len(df.columns):
                    invoice_date = str(df.iat[i, j + 1]).strip()
                    if invoice_date and invoice_date.lower() not in ["nan", "none", ""]:
                        results['invoice_date'] = invoice_date
                # 如果右侧无值，尝试下方单元格
                elif i + 1 < len(df):
                    invoice_date = str(df.iat[i + 1, j]).strip()
                    if invoice_date and invoice_date.lower() not in ["nan", "none", ""]:
                        results['invoice_date'] = invoice_date
    
    # 2. 如果列名匹配失败，再尝试正则匹配
    if not results['invoice_no'] or not results['invoice_date']:
        for i in range(min(len(df), 20)):
            for j in range(len(df.columns)):
                cell_value = str(df.iat[i, j])
                if not results['invoice_no']:
                    no_match = re.search(r'(?:发票号|Invoice\s*No\.?|INVOICE\s*NO[:：]?|NO[:：])\s*([A-Z0-9-]+)', cell_value, re.I)
                    if no_match:
                        results['invoice_no'] = no_match.group(1).strip()
                if not results['invoice_date']:
                    date_match = re.search(r'(?:日期|Date|DATE[:：]?)\s*([\d/]{8,10}|[A-Za-z]{3}\s*\d{2}[,\s]*\d{4})', cell_value, re.I)
                    if date_match:
                        results['invoice_date'] = date_match.group(1).strip()
    
    
    return results  # 默认值
def find_column(df, possible_names):
    """
    增强版列名查找函数
    功能：
    1. 处理列名中的隐藏字符
    2. 支持更灵活的名称匹配
    3. 提供详细的调试信息
    """
    # 调试：打印原始列名和清洗后的列名
    for col in df.columns:
        # 深度清洗列名
        col_clean = str(col).strip().upper()
        col_clean = ''.join(c for c in col_clean if c.isalnum() or c in ('_', '-'))
        
        for name in possible_names:
            # 深度清洗目标名称
            name_clean = str(name).strip().upper()
            name_clean = ''.join(c for c in name_clean if c.isalnum() or c in ('_', '-'))
            
            # 多种匹配方式
            if (name_clean == col_clean):       # 模糊匹配
                print(f"匹配成功: 标准名 '{name}' -> 实际列名 '{col}'")
                return col
    
    # 未找到时的调试信息
    return None
def safe_get(row, col_name, default=""):
    """超安全的数据访问方法"""
    try:
        # 尝试直接访问
        val = row[col_name]
        if pd.isna(val):
            return default
        return str(val)
    except KeyError:
        # 尝试忽略大小写访问
        for actual_col in row.index:
            if str(actual_col).upper() == str(col_name).upper():
                val = row[actual_col]
                return default if pd.isna(val) else str(val)
        return default
def read_excel(file_path):    
    try:
        # wb = openpyxl.load_workbook(file_path, data_only=True)
        all_sheets = pd.read_excel(file_path, sheet_name=None)
        # 1. 提取币制
        # currency = extract_currency_from_headers(all_sheets)
        result = []
        # 收集所有Sheet内容
        for sheet_name, df in all_sheets.items():
            if sheet_name.startswith(("XLR_", "Chart")) or df.empty:
                continue
            doc_type = "".join(sheet_name.split()).lower()  # 获取Sheet标题并转换为小写
            _doc_type=None
            if doc_type in ["进口发票", "发票", "IV", "INVOICE","INV","PI","CI","COMMERCIAL INVOICE","inv","iv","invoice"]:
                _doc_type = "进口发票"
                min_row = 14
                file = extract_currency_from_headers(df)
                currency = file["currency"]
                invoice_no = file["invoice_no"]
                invoice_date = file["invoice_date"]
                df = pd.read_excel(file_path, sheet_name=sheet_name,header=12)
                file = extract_currency_from_headers(df)
                currency = file["currency"]
                col_mapping = {
                    '项号': find_column(df, ['ITEM', '项号']),
                    '物料号': find_column(df, ['Part Number', '物料号','Part. No.']),
                    '商品名称': find_column(df, ['DESCRIPTION', '商品描述']),
                    '数量': find_column(df, ['QUANTITY', '数量']),
                    '单位': find_column(df, ['Unit', '单位']),
                    '单价': find_column(df, ['PRICE (EUR)', '单价','UNIT PRICE']),
                    '总价': find_column(df, ['VALUE (EUR)', '总价','AMOUNT(EUR)']),
                    '净重': find_column(df, ['WEIGHT (KGS)', '净重','N/W(KGS)','NET WEIGHT (KGS)']),
                    '原产国': find_column(df, ['ULA', '原产国','Country of orgin'])
                }

            elif doc_type in ["进口箱单", "箱单", "PACKING LIST", "PACKING","PL","pl","packing list","packing","packinglist"]:
                _doc_type = "进口箱单"
                df = pd.read_excel(file_path, sheet_name=sheet_name,header=12)
                file = extract_currency_from_headers(df)
                currency = file["currency"]
                col_mapping = {
                    '项号': find_column(df, ['ITEM', '项号']),
                    '物料号': find_column(df, ['Part Number', '物料号','Part. No.']),
                    '商品名称': find_column(df, ['DESCRIPTION', '商品描述']),
                    '数量': find_column(df, ['QUANTITY', '数量']),
                    '单位': find_column(df, ['Unit', '单位']),
                    '单价': find_column(df, ['PRICE (EUR)', '单价','UNIT PRICE']),
                    '总价': find_column(df, ['VALUE (EUR)', '总价','AMOUNT(EUR)']),
                    '净重': find_column(df, ['WEIGHT (KGS)', '净重','N/W(KGS)','NET WEIGHT (KGS)']),
                    '原产国': find_column(df, ['ULA', '原产国','Country of orgin'])
                }
                
            elif doc_type in ["进口合同", "合同", "CONTRACT","CL","SALES CONTRACT","contract","cl","salescontract"]:
                _doc_type = "进口合同"
                df = pd.read_excel(file_path, sheet_name=sheet_name,header=10)
                file = extract_currency_from_headers(df)
                currency = file["currency"]
                col_mapping = {
                    '项号': find_column(df, ['ITEM', '项号']),
                    '物料号': find_column(df, ['Part Number', '物料号','Part. No.']),
                    '商品名称': find_column(df, ['DESCRIPTION', '商品描述']),
                    '数量': find_column(df, ['QUANTITY', '数量']),
                    '单位': find_column(df, ['Unit', '单位']),
                    '单价': find_column(df, ['PRICE (EUR)', '单价','UNIT PRICE']),
                    '总价': find_column(df, ['VALUE (EUR)', '总价','AMOUNT(EUR)']),
                    '净重': find_column(df, ['WEIGHT (KGS)', '净重','N/W(KGS)','NET WEIGHT (KGS)']),
                    '原产国': find_column(df, ['ULA', '原产国','Country of orgin'])
                }
                
            elif doc_type in ["历史申报记录","进口申报记录","申报记录"]:
                _doc_type = "历史申报记录"
                df = pd.read_excel(file_path, sheet_name=sheet_name,header=0)
                file = extract_currency_from_headers(df)
                col_mapping = {
                    '项号': find_column(df, ['ITEM', '项号']),
                    '物料号': find_column(df, ['Part Number', '物料号','Material no#']),
                    '商品名称': find_column(df, ['DESCRIPTION', '商品描述','中文品名']),
                    '数量': find_column(df, ['QUANTITY', '数量','Quantity']),
                    '单位': find_column(df, ['Unit', '单位']),
                    '单价': find_column(df, ['PRICE (EUR)', '单价','Price']),
                    '总价': find_column(df, ['VALUE (EUR)', '总价']),
                    '净重': find_column(df, ['WEIGHT (KGS)', '净重']),
                    '原产国': find_column(df, ['ULA', '原产国','COO']),
                    '币制': find_column(df, ['Currency', '币制'])
                }
            if _doc_type:
                doc_result = {
                    "单据类型": _doc_type
                }
                if _doc_type == "进口发票":
                    doc_result["发票号"]= invoice_no
                    doc_result["发票日期"] = invoice_date
                items = []
                # 清理数据
                df = df.dropna(how='all')  # 删除全空行
                for _, row in df.iterrows():
                    if not any(row):  # 跳过空行
                        continue
                    item = {}
                    # if isinstance(row[0], str) and "TOTAL" in row[0].strip().upper():
                    #     break
                    # if not row[0] or not row[1]:  # 跳过没有物料号或项号的行
                    #     break
                    if "TOTAL" in safe_get(row, col_mapping['项号']) or not safe_get(row, col_mapping['物料号']) or not safe_get(row, col_mapping['商品名称']):
                        break
                    # 构建商品信息的字典
                    if _doc_type == "进口发票":
                        item = {
                            "项号": safe_get(row, col_mapping['项号']),
                            "物料号": safe_get(row, col_mapping['物料号']).replace(".0", ""),
                            "商品名称": safe_get(row, col_mapping['商品名称']),
                            "数量": safe_get(row, col_mapping['数量']).replace(".0", ""),
                            "单位": safe_get(row, col_mapping['单位']),
                            "数量及单位":safe_get(row, col_mapping['数量'])+safe_get(row, col_mapping['单位']),
                            "单价": safe_get(row, col_mapping['单价']),
                            "总价": safe_get(row, col_mapping['总价']),
                            "净重": safe_get(row, col_mapping['净重']),
                            "币制": currency_change(currency),
                            "原产国": country_change(safe_get(row, col_mapping['原产国']))
                        }
                    if _doc_type == "进口箱单":
                        item = {
                            "项号": safe_get(row, col_mapping['项号']),
                            "物料号": safe_get(row, col_mapping['物料号']).replace(".0", ""),
                            "商品名称": safe_get(row, col_mapping['商品名称']),
                            "数量": safe_get(row, col_mapping['数量']).replace(".0", ""),
                            "单位": safe_get(row, col_mapping['单位']),
                            "单价": safe_get(row, col_mapping['单价']),
                            "总价": safe_get(row, col_mapping['总价']),
                            "净重": safe_get(row, col_mapping['净重']),
                            "币制": currency_change(currency),
                            "原产国": country_change(safe_get(row, col_mapping['原产国']))
                        }
                    if _doc_type == "进口合同":
                        item = {
                            "项号": safe_get(row, col_mapping['项号']),
                            "物料号": safe_get(row, col_mapping['物料号']).replace(".0", ""),
                            "商品名称":safe_get(row, col_mapping['商品名称']),
                            "数量": safe_get(row, col_mapping['数量']).replace(".0", ""),
                            "单位": safe_get(row, col_mapping['单位']),
                            "单价": safe_get(row, col_mapping['单价']),
                            "总价": safe_get(row, col_mapping['总价']),
                            "币制": currency_change(currency)
                        }
                    if _doc_type == "历史申报记录":
                        item = {
                            "物料号": safe_get(row, col_mapping['物料号']).replace(".0", ""),
                            "商品名称": safe_get(row, col_mapping['商品名称']),
                            "数量": safe_get(row, col_mapping['数量']),
                            "币制": currency_change(safe_get(row, col_mapping['币制'])),
                            "单价": safe_get(row, col_mapping['单价']),
                            "原产国": country_change(safe_get(row, col_mapping['原产国']))
                        }
                    items.append(item)

                doc_result["商品信息"] = items
                result.append(doc_result)    
        return result

    except Exception as e:
            import traceback
            traceback.print_exc()
            return {"error": f"Excel(.xlsx)处理失败: {str(e)}"}


# print(aa)
def check_document_consistency(documents):
    # 提取发票、箱单、合同数据
    invoice = next((doc for doc in documents if doc["单据类型"] == "进口发票"), None)
    packing = next((doc for doc in documents if doc["单据类型"] == "进口箱单"), None)
    contract = next((doc for doc in documents if doc["单据类型"] == "进口合同"), None)
    
    result_str = ""
    available_docs = []
    
    # 收集可用的单据
    if invoice:
        available_docs.append(("发票", invoice))
    if packing:
        available_docs.append(("箱单", packing))
    if contract:
        available_docs.append(("合同", contract))
    
    # 检查是否有至少两个单据可用
    if len(available_docs) < 2:
        result_str = "错误：至少需要两个单据进行比对！"
        return result_str
    
    # 获取所有单据的商品信息
    doc_items = {}
    for name, doc in available_docs:
        doc_items[name] = doc["商品信息"]
    
    # 检查商品数量是否一致（在所有可用单据中）
    item_counts = [len(items) for items in doc_items.values()]
    if len(set(item_counts)) > 1:
        result_str = "警告：商品数量不一致！\n"
        return result_str
    
    # 定义要比对的字段
    fields_to_check = ["单价", "数量", "单位", "总价", "币制", "原产国"]  # 可自定义
    
    # 逐项比对
    for i in range(item_counts[0]):
        # 收集当前项在所有单据中的信息
        current_items = {}
        for name in doc_items:
            current_items[name] = doc_items[name][i]
        
        # 获取基本信息
        first_doc_name = available_docs[0][0]
        it_no = current_items[first_doc_name].get("项号")
        part_no = current_items[first_doc_name].get("物料号")
        item_no = current_items[first_doc_name].get("商品名称")
        
        # 比较所有单据对
        for j in range(len(available_docs)):
            for k in range(j+1, len(available_docs)):
                doc1_name, doc1 = available_docs[j]
                doc2_name, doc2 = available_docs[k]
                
                item1 = current_items[doc1_name]
                item2 = current_items[doc2_name]
                
                for field in fields_to_check:
                    if field in item1 and field in item2:
                        val1 = item1[field]
                        val2 = item2[field]
                        if val1 and val2 and val1 != val2:
                            result_str += f"项号{it_no}物料号{part_no}商品{item_no}的{field}在{doc1_name}和{doc2_name}中不一致:{doc1_name}: {val1}, {doc2_name}: {val2};\n"
    
    return result_str
# 示例使用
# print(check_document_consistency(aa))

from collections import defaultdict
# 构建验证规则
def build_validation_rules(history_data):
    rules = defaultdict(lambda: {
        "单价范围": [float('inf'), float('-inf')],  # 初始化为无效范围
        "允许币制": set(),
        "允许原产国": set()
    })
    
    for item in history_data:
        material_id = item.get("物料号")
        if not material_id:
            continue
        
        rule = rules[material_id]
        
        # 处理单价（严格跳过无效值）
        if "单价" in item:
            try:
                unit_price = float(item["单价"])
                rule["单价范围"][0] = min(rule["单价范围"][0], unit_price)
                rule["单价范围"][1] = max(rule["单价范围"][1], unit_price)
            except (TypeError, ValueError):
                pass  # 静默跳过无效单价
        
        # 处理币制
        if "币制" in item:
            rule["允许币制"].add(item["币制"])
        
        # 处理原产国
        if "原产国" in item:
            rule["允许原产国"].add(item["原产国"])
    
    # 转换集合为列表
    for material_id in rules:
        # 如果所有单价均无效，则重置范围为[0,0]（或根据业务需求调整）
        if rules[material_id]["单价范围"] == [float('inf'), float('-inf')]:
            rules[material_id]["单价范围"] = [0, 0]  # 或设置为None
            
        rules[material_id]["允许币制"] = list(rules[material_id]["允许币制"])
        rules[material_id]["允许原产国"] = list(rules[material_id]["允许原产国"])
    
    return dict(rules)

# 验证进口发票商品信息
def validate_invoice_items(documents,validation_rules):
    results = []
    invoice_items = next((doc["商品信息"] for doc in documents if doc["单据类型"] == "进口发票"), [])
    # print(invoice_items)
    for item in invoice_items:
        material_id = item.get("物料号")
        if not material_id or material_id not in validation_rules:
            # errors.append(f"物料号 {material_id} 未在历史数据中找到")
            continue
        errors = []
        rule = validation_rules[material_id]
        if material_id == 8058469:
            print(item)
            print(rule)
        # 检查单价
        if "单价" in item and "单价范围" in rule:
            try:
                unit_price = float(item["单价"])
                min_price, max_price = rule["单价范围"]
                if not (min_price <= unit_price <= max_price):
                    errors.append(f"单价 {unit_price} 超出历史范围 [{min_price}, {max_price}]")
            except (TypeError, ValueError):
                errors.append(f"单价 {item['单价']} 无效")
        
        # 检查币制
        if "币制" in item and "允许币制" in rule:
            if item["币制"] not in rule["允许币制"]:
                errors.append(f"币制 {item['币制']} 不在历史允许范围内 {rule['允许币制']}")
        
        # 检查原产国
        if "原产国" in item and "允许原产国" in rule:
            if item["原产国"] not in rule["允许原产国"]:
                errors.append(f"原产国 {item['原产国']} 不在历史允许范围内 {rule['允许原产国']}")
        status = "通过" if not errors else "失败"
        if errors: 
            results.append({
                "物料号": material_id,
                "商品名称": item["商品名称"],
                "状态": status,
                "错误": errors
            })
    return results





async def i_data_check(file_path,file_name):
    file_data = read_excel(file_path)
    detail_format=[]
    for file in file_data:
        file_da={}
        file_da["file_name"]=file_name
        file_da["content"] = file
        detail_format.append(file_da)
    # 单据之间验证
    validate = check_document_consistency(file_data)
    # 构建验证规则
    history_items = next((doc["商品信息"] for doc in file_data if doc["单据类型"] == "历史申报记录"), [])
    validation_rules = build_validation_rules(history_items)
    # print(validation_rules)
    # 验证进口发票商品信息
    errors = validate_invoice_items(file_data, validation_rules)
    print("验证结果:",errors)
    result = {}
    inv_product = next((doc["商品信息"] for doc in file_data if doc["单据类型"] == "进口发票"), [])
    result["单据类型"]="进口发票"
    result["商品信息"] = inv_product
    result["单证校验"] = [{
        "error":validate,
        "file_name":"校验结果"
    }]
    result["验证结果"] = errors
    merged = {
        "file_name":file_name,
        "content":result
    }
    return detail_format,merged



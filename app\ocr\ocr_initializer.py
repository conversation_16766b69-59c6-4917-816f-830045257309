import logging
from typing import Dict, Any, List
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR
import subprocess
import torch
from transformers import DetrImageProcessor, TableTransformerForObjectDetection
import platform
import os

class OCRInitializer:
    
    @classmethod
    def __init__(self):
        """初始化OCR处理器"""
        try:

            # 自动检测系统并设置poppler路径
            if platform.system() == "Darwin":
                try:
                    # 尝试通过brew获取路径（兼容Intel和Apple Silicon）
                    brew_path = subprocess.check_output(["brew", "--prefix", "poppler"], stderr=subprocess.DEVNULL).decode().strip()
                    detected_poppler_path = f"{brew_path}/bin"
                except (subprocess.CalledProcessError, FileNotFoundError):
                    # 如果brew不可用，尝试常见安装路径
                    common_paths = [
                        "/opt/homebrew/opt/poppler/bin",  # Apple Silicon
                        "/usr/local/opt/poppler/bin"      # Intel Mac
                    ]
                    for path in common_paths:
                        if os.path.exists(f"{path}/pdfimages"):
                            detected_poppler_path = path
                            break
                    else:
                        raise RuntimeError("未找到poppler路径，请通过brew安装: brew install poppler")
            elif platform.system() == "Linux":
                # 检查常见Linux路径
                linux_paths = [
                    "/usr/bin",          # Ubuntu/Debian默认路径
                    "/usr/local/bin",    # 自定义安装
                    "/opt/poppler/bin"   # 其他可能路径
                ]
                for path in linux_paths:
                    if os.path.exists(f"{path}/pdfimages"):
                        detected_poppler_path = path
                        break
                else:
                    # 尝试通过which查找
                    try:
                        pdfimages_path = subprocess.check_output(["which", "pdfimages"], stderr=subprocess.DEVNULL).decode().strip()
                        detected_poppler_path = os.path.dirname(pdfimages_path)
                    except subprocess.CalledProcessError:
                        raise RuntimeError("未找到poppler-utils，请安装: sudo apt-get install poppler-utils")

            self.ocr = PaddleOCR(
                use_angle_cls=True, 
                lang="ch", 
                use_gpu=True,
                rec_model_dir='./model/v3.0/ch_PP-OCRv3_rec_infer/',
                cls_model_dir='./model/v3.0/ch_ppocr_mobile_v2.0_cls_infer/',
                det_model_dir='./model/v3.0/ch_PP-OCRv3_det_infer/'
            )
            
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            # 初始化表格检测模型
            self.processor = DetrImageProcessor.from_pretrained("./model/table-transformer-detection",local_files_only=True,trust_remote_code=True, use_safetensors=True)
            self.table_model = TableTransformerForObjectDetection.from_pretrained(
                "./model/table-transformer-detection",
                local_files_only=True,
                trust_remote_code=True,
                use_safetensors=True
            ).to(device)
            
            logging.info("OCR处理器初始化成功")

            # 确保属性正确赋值
            self.poppler_path = detected_poppler_path
            self.ocr = self.ocr
            self.table_model = self.table_model
            self.processor = self.processor
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"OCR处理器初始化失败: {str(e)}")
            raise RuntimeError("OCR系统初始化失败，请检查：\n1.模型文件是否完整\n2.文件路径是否正确\n3.依赖库版本是否匹配") from e


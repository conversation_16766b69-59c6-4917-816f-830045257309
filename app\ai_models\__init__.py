from importlib import import_module
from typing import Dict, Any, Type
import logging
import os

from .base import BaseAIModel
from ..config import ModelProvider, get_model_config
from .ollama_handler import OllamaHandler
from .doubao_handler import DoubaoHandler
from .deepseek_handler import DeepSeekHandler

def get_ai_model(provider: str = None) -> BaseAIModel:
    """获取AI模型处理器"""
    # 从配置中获取模型配置
    model_config = get_model_config(provider)
    try:
        # 动态导入处理器类
        handler_class_path = model_config["handler_class"]
        module_path, class_name = handler_class_path.rsplit(".", 1)
        
        module = import_module(module_path)
        handler_class = getattr(module, class_name)
        
        # 实例化处理器
        return handler_class(model_config)
    except (ImportError, AttributeError, ValueError) as e:
        logging.error(f"加载模型处理器失败 ({provider}): {str(e)}")
        # 回退到DeepSeek处理器
        return DoubaoHandler(get_model_config(ModelProvider.DOUBAO))

# 口岸
CUSTOMS_PORT = {
    "110001":"北京",
"110002":"北京平谷国际陆港",
"110003":"北京天竺综合保税区",
"110101":"首都国际机场",
"110201":"北京丰台货运",
"110301":"北京朝阳口岸",
"110801":"北京西站",
"120001":"天津",
"120002":"北疆港区",
"120003":"天津保税物流园区",
"120004":"天津港保税区",
"120011":"中国（天津）自由贸易试验区",
"120201":"天津出口加工区",
"120601":"天津滨海新区综合保税区",
"120801":"大沽口港区",
"121001":"南疆港区",
"121002":"渤中",
"121501":"东疆港区",
"121502":"天津邮轮母港",
"121503":"天津新港客运码头",
"121504":"天津东疆保税港区",
"121701":"天津滨海国际机场货邮",
"121702":"天津滨海国际机场T1通道",
"121703":"天津滨海国际机场T2通道",
"130001":"石家庄",
"130002":"石家庄正定机场",
"130101":"秦皇岛",
"130102":"秦皇岛出口加工区",
"130200":"河北省唐山市",
"130201":"京唐港",
"130202":"唐山",
"130203":"曹妃甸港",
"130204":"曹妃甸综合保税区",
"130300":"河北省秦皇岛市",
"130400":"河北省邯郸市",
"130500":"河北省邢台市",
"130600":"河北省保定市",
"130700":"河北省张家口市",
"130701":"廊坊出口加工区",
"130800":"河北省承德市",
"130900":"河北省沧州市",
"131000":"河北省廊坊市",
"131100":"河北省衡水市",
"131101":"黄骅",
"131201":"石家庄综合保税区",
"140001":"太原",
"140002":"太原武宿机场",
"140003":"太原武宿综合保税区",
"140200":"山西省大同市",
"140300":"山西省阳泉市",
"140400":"山西省长治市",
"140500":"山西省晋城市",
"140600":"山西省朔州市",
"140700":"山西省晋中市",
"140800":"山西省运城市",
"140900":"山西省忻州市",
"141000":"山西省临汾市",
"141100":"山西省吕梁市",
"150001":"呼和浩特",
"150002":"呼和浩特白塔机场",
"150003":"呼和浩特出口加工区",
"150004":"鄂尔多斯伊金霍洛机场",
"150101":"满洲里西郊机场",
"150102":"满洲里铁路",
"150103":"二卡",
"150104":"满洲里十八里",
"150105":"阿日哈沙特",
"150106":"满洲里综合保税区",
"150200":"内蒙古自治区包头市",
"150201":"二连浩特铁路",
"150202":"二连浩特公路",
"150300":"内蒙古自治区乌海市",
"150301":"海拉尔东山机场",
"150302":"额布都格",
"150303":"胡列也吐",
"150400":"内蒙古自治区赤峰市",
"150401":"满都拉",
"150500":"内蒙古自治区通辽市",
"150600":"内蒙古自治区鄂尔多斯市",
"150700":"内蒙古自治区呼伦贝尔市",
"150800":"内蒙古自治区巴彦淖尔市",
"150801":"黑山头",
"150802":"室韦",
"150900":"内蒙古自治区乌兰察布市",
"150901":"策克",
"151001":"珠恩嘎达布其",
"151101":"甘其毛都",
"151102":"巴格毛都",
"151201":"阿尔山",
"152200":"内蒙古自治区兴安盟",
"152500":"内蒙古自治区锡林郭勒盟",
"152900":"内蒙古自治区阿拉善盟",
"210001":"沈阳",
"210101":"大连港大窑湾港区",
"210102":"大连北良港区",
"210103":"大连港油品码头港区",
"210104":"大连大窑湾保税港区",
"210200":"辽宁省大连市",
"210300":"辽宁省鞍山市",
"210301":"沈阳桃仙国际机场",
"210400":"辽宁省抚顺市",
"210401":"锦州",
"210500":"辽宁省本溪市",
"210600":"辽宁省丹东市",
"210700":"辽宁省锦州市",
"210701":"丹东港浪头港区",
"210702":"丹东铁路",
"210703":"丹东公路",
"210704":"丹东输油管道",
"210705":"丹东太平湾",
"210706":"丹东长甸河口",
"210707":"丹东哑巴沟",
"210708":"丹东马市过货点",
"210709":"丹东安民",
"210800":"辽宁省营口市",
"210801":"丹东港大东港区",
"210802":"丹东大鹿岛",
"210803":"丹东大台子",
"210804":"丹东一撮毛过货点",
"210805":"丹东丹纸码头",
"210900":"辽宁省阜新市",
"210901":"营口港老港区",
"211000":"辽宁省辽阳市",
"211001":"营口港鲅鱼圈港区",
"211002":"营口港仙人岛港区",
"211003":"营口港保税物流园区",
"211100":"辽宁省盘锦市",
"211200":"辽宁省铁岭市",
"211300":"辽宁省朝阳市",
"211400":"辽宁省葫芦岛市",
"211401":"盘锦港",
"211501":"葫芦岛港",
"211801":"大连周水子国际机场",
"211901":"大连港香炉礁港区",
"211902":"大连港老港区",
"211903":"大连港大连湾港区",
"211904":"大连港汽车码头港区",
"211905":"大连港矿石码头港区",
"211906":"旅顺新港",
"211907":"庄河港",
"211908":"大连长海四块石码头",
"211909":"大连出口加工区",
"211910":"大连保税区",
"212001":"长兴岛港",
"212101":"沈阳综合保税区",
"220001":"长春",
"220002":"长春龙嘉国际机场",
"220003":"长春铁路",
"220004":"临江",
"220005":"大安",
"220006":"图们公路",
"220007":"图们铁路",
"220008":"船营",
"220009":"档石",
"220010":"下三道沟",
"220011":"长春兴隆综合保税区",
"220101":"双目峰公务通道",
"220102":"延吉朝阳川机场",
"220103":"三合",
"220104":"开山屯",
"220105":"南坪",
"220106":"古城里",
"220200":"吉林省吉林市",
"220201":"珲春公路",
"220202":"珲春铁路",
"220203":"沙坨子",
"220204":"圈河",
"220205":"珲春出口加工区",
"220300":"吉林省四平市",
"220400":"吉林省辽源市",
"220500":"吉林省通化市",
"220501":"集安铁路",
"220502":"老虎哨",
"220503":"青石",
"220504":"秋皮村",
"220505":"集安过货通道",
"220600":"吉林省白山市",
"220601":"长白",
"220602":"十三道沟",
"220603":"八道沟",
"220604":"南尖头",
"220700":"吉林省松原市",
"220701":"吉林铁路",
"220800":"吉林省白城市",
"222400":"吉林省延边朝鲜族自治州",
"230001":"哈尔滨",
"230002":"哈尔滨太平国际机场",
"230003":"嘉荫",
"230004":"漠河",
"230005":"绥芬河综合保税区",
"230101":"齐齐哈尔三家子机场",
"230200":"黑龙江省齐齐哈尔市",
"230201":"大庆",
"230300":"黑龙江省鸡西市",
"230301":"牡丹江海浪机场",
"230400":"黑龙江省鹤岗市",
"230401":"绥芬河铁路",
"230402":"绥芬河公路",
"230500":"黑龙江省双鸭山市",
"230501":"虎林",
"230600":"黑龙江省大庆市",
"230601":"密山",
"230700":"黑龙江省伊春市",
"230701":"佳木斯港",
"230702":"佳木斯东郊机场",
"230703":"桦川",
"230800":"黑龙江省佳木斯市",
"230801":"饶河",
"230900":"黑龙江省七台河市",
"230901":"同江",
"231000":"黑龙江省牡丹江市",
"231001":"抚远",
"231100":"黑龙江省黑河市",
"231101":"黑河",
"231102":"孙吴港",
"231103":"孙吴边境通道",
"231104":"呼玛",
"231200":"黑龙江省绥化市",
"231201":"逊克",
"231401":"萝北",
"231501":"东宁",
"231601":"绥滨",
"231602":"富锦",
"231701":"哈尔滨港",
"231702":"哈尔滨站",
"232700":"黑龙江省大兴安岭地区",
"310001":"上海",
"310002":"龙吴",
"310011":"中国（上海）自由贸易试验区",
"310101":"上海嘉定出口加工区",
"310102":"上海青浦出口加工区",
"310201":"上海金桥出口加工区",
"310301":"上海虹桥国际机场",
"310302":"上海浦东国际机场",
"310303":"上海浦东机场综合保税区",
"310401":"罗泾",
"310402":"吴淞",
"310501":"崇明",
"310601":"上海闵行出口加工区",
"310701":"外高桥",
"310702":"上海外高桥保税物流园区",
"310703":"上海外高桥保税区",
"310901":"上海漕河泾出口加工区",
"311001":"浦东临港产业作业区",
"311002":"洋山港",
"311003":"洋山保税港区",
"311201":"上海站",
"311301":"上海松江出口加工区A区",
"311302":"上海松江出口加工区B区",
"320001":"南京",
"320101":"南京禄口国际机场",
"320102":"南京港",
"320103":"南京出口加工区",
"320104":"南京综合保税区",
"320200":"江苏省无锡市",
"320201":"苏州",
"320202":"苏州工业园综合保税区",
"320203":"苏州高新技术产业开发区综合保税区",
"320204":"吴中出口加工区",
"320300":"江苏省徐州市",
"320301":"吴江出口加工区",
"320400":"江苏省常州市",
"320401":"昆山综合保税区",
"320500":"江苏省苏州市",
"320501":"张家港",
"320502":"张家港保税港区",
"320600":"江苏省南通市",
"320601":"常熟",
"320602":"常熟出口加工区",
"320700":"江苏省连云港市",
"320701":"太仓",
"320702":"太仓港综合保税区",
"320800":"江苏省淮安市",
"320801":"无锡",
"320802":"苏南硕放国际机场",
"320803":"无锡高新区综合保税区",
"320804":"无锡出口加工区",
"320900":"江苏省盐城市",
"320901":"江阴",
"321000":"江苏省扬州市",
"321100":"江苏省镇江市",
"321101":"南通",
"321103":"如皋",
"321104":"南通综合保税区",
"321106":"南通机场",
"321200":"江苏省泰州市",
"321201":"连云港",
"321202":"连云港出口加工区",
"321300":"江苏省宿迁市",
"321301":"镇江",
"321302":"镇江出口加工区",
"321401":"徐州机场",
"321501":"淮安综合保税区",
"321502":"淮安出口加工区",
"321601":"常州",
"321602":"常州出口加工区",
"321603":"武进出口加工区",
"321604":"常州奔牛机场",
"321701":"盐城机场",
"321702":"射阳",
"321703":"盐城综合保税区",
"321704":"大丰",
"321801":"扬州",
"321802":"扬州出口加工区",
"321901":"高港",
"321902":"泰州",
"322201":"靖江",
"322202":"泰州出口加工区",
"322401":"启东",
"322501":"如东洋口",
"330001":"杭州",
"330002":"杭州萧山国际机场",
"330101":"温州龙湾国际机场",
"330102":"温州港瓯江港区七里作业区",
"330103":"温州港平阳港区",
"330104":"温州港状元岙港区",
"330105":"温州港乐清湾港区",
"330106":"温州港大小门岛港区",
"330200":"浙江省宁波市",
"330201":"金义综合保税区",
"330300":"浙江省温州市",
"330301":"舟山",
"330302":"舟山定海港区",
"330303":"舟山定海岙山油库",
"330304":"舟山定海万向油库",
"330305":"舟山老塘山港区",
"330306":"舟山老塘山三期码头",
"330307":"舟山老塘山五期码头",
"330308":"舟山老塘山册子油库",
"330309":"舟山金塘港区",
"330310":"舟山金塘集装箱码头",
"330311":"舟山沈家门港区",
"330312":"舟山沈家门半升洞油库",
"330313":"舟山六横港区",
"330314":"舟山六横煤电码头",
"330315":"舟山六横武港码头",
"330316":"舟山六横金润石油",
"330317":"舟山衢山港区",
"330318":"舟山马岙港区",
"330319":"舟山马岙太平洋化工",
"330320":"舟山马岙纳海油污",
"330321":"舟山马岙天禄能源",
"330322":"岱山高亭",
"330323":"舟山港综合保税区",
"330400":"浙江省嘉兴市",
"330401":"泗礁",
"330500":"浙江省湖州市",
"330501":"海门",
"330502":"台州",
"330503":"大麦屿",
"330600":"浙江省绍兴市",
"330700":"浙江省金华市",
"330701":"嘉兴",
"330702":"嘉兴港",
"330703":"嘉兴出口加工区A区",
"330704":"嘉兴出口加工区B区",
"330800":"浙江省衢州市",
"330801":"湖州",
"330802":"湖州南浔",
"330803":"湖州安吉川达",
"330900":"浙江省舟山市",
"331000":"浙江省台州市",
"331100":"浙江省丽水市",
"331101":"杭州保税物流园区（B型）",
"331201":"义乌",
"333301":"杭州出口加工区",
"333302":"杭州中和保税区",
"340001":"合肥",
"340002":"合肥新桥国际机场",
"340003":"合肥新站",
"340004":"六安",
"340005":"芜湖出口加工区",
"340006":"合肥出口加工区",
"340007":"合肥综合保税区",
"340101":"芜湖",
"340200":"安徽省芜湖市",
"340201":"安庆",
"340300":"安徽省蚌埠市",
"340301":"铜陵",
"340400":"安徽省淮南市",
"340401":"马鞍山",
"340500":"安徽省马鞍山市",
"340501":"蚌埠",
"340502":"淮南",
"340600":"安徽省淮北市",
"340601":"阜阳",
"340602":"亳州",
"340700":"安徽省铜陵市",
"340701":"黄山屯溪机场",
"340800":"安徽省安庆市",
"340801":"池州",
"340901":"滁州",
"341000":"安徽省黄山市",
"341001":"宣城",
"341100":"安徽省滁州市",
"341200":"安徽省阜阳市",
"341201":"淮北",
"341202":"宿州",
"341300":"安徽省宿州市",
"341500":"安徽省六安市",
"341600":"安徽省亳州市",
"341700":"安徽省池州市",
"341800":"安徽省宣城市",
"350001":"福州",
"350002":"福州港江阴港区非保税码头",
"350003":"福州港平潭金井码头",
"350004":"平潭港口岸澳前港区",
"350005":"福州港平潭澳前客滚码头",
"350006":"武夷山机场",
"350007":"武夷山陆地港",
"350008":"福州保税港区",
"350011":"中国（福建）自由贸易试验区",
"350101":"泉州港肖厝港区",
"350102":"泉州港泉州湾港区",
"350103":"泉州港围头湾港区",
"350104":"泉州港深沪湾港区",
"350105":"泉州港斗尾港区",
"350106":"泉州港石井客运码头",
"350107":"泉州港肖厝小额贸易点",
"350108":"泉州惠安崇武小额贸易点",
"350109":"泉州后渚小额贸易点",
"350110":"泉州石狮石湖小额贸易点",
"350111":"泉州晋江围头小额贸易点",
"350112":"泉州晋江深沪小额贸易点",
"350113":"泉州南安石井小额贸易点",
"350114":"泉州晋江陆地港",
"350115":"泉州晋江机场",
"350116":"泉州出口加工区",
"350200":"福建省厦门市",
"350201":"莆田湄洲岛小额贸易点",
"350202":"莆田港秀屿港区",
"350203":"莆田港湄洲岛客运码头",
"350204":"莆田港东吴港区",
"350300":"福建省莆田市",
"350301":"三明陆地港",
"350400":"福建省三明市",
"350401":"福州港马尾客运码头",
"350402":"福州港闽江口内港区",
"350403":"福州港马尾小额贸易点",
"350404":"福州港连江琯头小额贸易点",
"350405":"福州港罗源迹头小额贸易点",
"350406":"福州港罗源湾港区",
"350407":"福州港黄岐港区",
"350408":"福州港松下港区牛头湾作业区",
"350409":"福州港松下港区长乐松下小额贸易点",
"350410":"福州保税区",
"350411":"福州出口加工区",
"350500":"福建省泉州市",
"350501":"宁德港三都澳港区",
"350502":"宁德港三沙港区",
"350503":"宁德港沙埕港区",
"350504":"宁德港赛江港区",
"350505":"霞浦三沙小额贸易点",
"350600":"福建省漳州市",
"350601":"福州港松下港区元洪作业区",
"350602":"福州港松下港区南青屿小额贸易点",
"350700":"福建省南平市",
"350800":"福建省龙岩市",
"350900":"福建省宁德市",
"350901":"龙岩陆地港",
"351101":"东山湾东山港区",
"351102":"东山湾诏安港区",
"351103":"漳州东山铜陵小额贸易点",
"351104":"漳州云霄礁美小额贸易点",
"351105":"漳州诏安田厝小额贸易点",
"351201":"福州长乐国际机场",
"360001":"南昌",
"360002":"南昌昌北机场",
"360003":"南昌出口加工区",
"360101":"九江",
"360102":"九江出口加工区",
"360200":"江西省景德镇市",
"360300":"江西省萍乡市",
"360301":"赣州出口加工区",
"360302":"赣州综合保税区",
"360400":"江西省九江市",
"360500":"江西省新余市",
"360600":"江西省鹰潭市",
"360601":"井冈山出口加工区",
"360700":"江西省赣州市",
"360800":"江西省吉安市",
"360900":"江西省宜春市",
"361000":"江西省抚州市",
"361100":"江西省上饶市",
"370001":"济南",
"370002":"日照",
"370003":"岚山",
"370101":"青岛港",
"370102":"青岛出口加工区",
"370200":"山东省青岛市",
"370201":"黄岛",
"370202":"青岛前湾保税港区",
"370203":"青岛西海岸出口加工区",
"370300":"山东省淄博市",
"370301":"烟台莱山机场",
"370302":"烟台港",
"370303":"烟台保税港区A区",
"370304":"烟台保税港区B区",
"370400":"山东省枣庄市",
"370401":"石岛",
"370402":"龙眼",
"370500":"山东省东营市",
"370501":"龙口",
"370600":"山东省烟台市",
"370601":"莱州",
"370700":"山东省潍坊市",
"370701":"济南遥墙机场",
"370702":"济南综合保税区",
"370703":"济南出口加工区",
"370800":"山东省济宁市",
"370801":"济宁站",
"370900":"山东省泰安市",
"370901":"潍坊",
"370902":"潍坊综合保税区",
"371000":"山东省威海市",
"371100":"山东省日照市",
"371200":"山东省莱芜市",
"371201":"威海机场",
"371202":"威海港",
"371203":"威海出口加工区",
"371300":"山东省临沂市",
"371400":"山东省德州市",
"371500":"山东省聊城市",
"371600":"山东省滨州市",
"371700":"山东省菏泽市",
"371801":"临沂站",
"371802":"临沂综合保税区",
"371901":"东营",
"371902":"东营综合保税区",
"372301":"青岛流亭机场",
"372401":"蓬莱",
"380001":"宁波",
"380002":"宁波栎社机场",
"380003":"宁波甬江港区",
"380004":"宁波出口加工区",
"380005":"宁波保税区",
"380101":"宁波北仑港港区",
"380102":"宁波穿山港区",
"380201":"慈溪出口加工区",
"380701":"宁波象山石浦港区",
"380702":"宁波象山港区",
"380801":"宁波大榭港区",
"380901":"宁波梅山保税港区",
"381001":"宁波镇海港区",
"390001":"厦门",
"390002":"厦门邮轮中心",
"390003":"刘五店",
"390004":"大磴岛",
"390005":"厦门象屿保税物流园区",
"390007":"厦门翔安火炬保税物流园区",
"390008":"厦门保税区",
"390101":"厦门海沧港区",
"390102":"厦门海沧保税港区",
"390301":"漳州招银港区",
"390302":"漳州后石港区",
"390303":"漳州古雷港区",
"390304":"漳州旧镇港区",
"390305":"漳州石码港区",
"399101":"厦门高崎国际机场",
"399102":"厦门五通码头",
"399103":"厦门五缘湾码头",
"399501":"厦门东渡港区",
"410001":"郑州",
"410002":"郑州查验场",
"410003":"郑州新郑国际机场",
"410004":"郑州站",
"410005":"郑州出口加工区",
"410101":"洛阳北郊机场",
"410200":"河南省开封市",
"410300":"河南省洛阳市",
"410400":"河南省平顶山市",
"410500":"河南省安阳市",
"410501":"漯河查验场",
"410600":"河南省鹤壁市",
"410601":"南阳卧龙综合保税区",
"410700":"河南省新乡市",
"410800":"河南省焦作市",
"410900":"河南省濮阳市",
"410901":"郑州新郑综合保税区",
"411000":"河南省许昌市",
"411100":"河南省漯河市",
"411200":"河南省三门峡市",
"411300":"河南省南阳市",
"411400":"河南省商丘市",
"411500":"河南省信阳市",
"411600":"河南省周口市",
"411700":"河南省驻马店市",
"419000":"河南省省直辖县级行政区划",
"420001":"武汉",
"420002":"武汉天河机场",
"420003":"武汉阳逻水运港",
"420004":"武汉出口加工区",
"420005":"武汉东西湖保税物流园区",
"420006":"武汉东湖综合保税区",
"420007":"武汉新港空港综合保税区",
"420101":"荆州盐卡",
"420200":"湖北省黄石市",
"420201":"襄阳铁路",
"420202":"襄阳公路",
"420203":"十堰公路",
"420300":"湖北省十堰市",
"420301":"宜昌三峡机场",
"420302":"宜昌水运港",
"420401":"黄石水运港",
"420500":"湖北省宜昌市",
"420600":"湖北省襄阳市",
"420700":"湖北省鄂州市",
"420800":"湖北省荆门市",
"420900":"湖北省孝感市",
"421000":"湖北省荆州市",
"421100":"湖北省黄冈市",
"421200":"湖北省咸宁市",
"421300":"湖北省随州市",
"422800":"湖北省恩施土家族苗族自治州",
"429000":"湖北省省直辖县级行政区划",
"430001":"长沙",
"430002":"长沙黄花国际机场旅检通道",
"430003":"长沙黄花国际机场货场",
"430004":"张家界荷花国际机场",
"430005":"长沙霞凝港",
"430006":"长沙霞凝铁路",
"430007":"长沙金霞保税物流园区",
"430008":"湘潭综合保税区",
"430009":"长沙黄花国际机场国际快件监控中心",
"430101":"岳阳城陵矶水运",
"430103":"岳阳城陵矶综合保税区",
"430200":"湖南省株洲市",
"430201":"常德盐关水运",
"430300":"湖南省湘潭市",
"430400":"湖南省衡阳市",
"430401":"衡阳公路",
"430402":"衡阳综合保税区",
"430500":"湖南省邵阳市",
"430501":"郴州公路",
"430502":"郴州铁路",
"430503":"郴州出口加工区",
"430600":"湖南省岳阳市",
"430700":"湖南省常德市",
"430701":"湘潭公路",
"430702":"长沙黄花综合保税区",
"430800":"湖南省张家界市",
"430900":"湖南省益阳市",
"431000":"湖南省郴州市",
"431100":"湖南省永州市",
"431200":"湖南省怀化市",
"431300":"湖南省娄底市",
"433100":"湖南省湘西土家族苗族自治州",
"440001":"广州",
"440002":"黄埔港务码头",
"440003":"南沙粮食及通用码头",
"440004":"黄埔嘉利仓码头",
"440005":"佛山三水西南码头",
"440006":"佛山三水港码头",
"440007":"佛山三水车检场",
"440008":"东莞凤岗车检场",
"440009":"东莞长安车检场",
"440010":"广州香港马会马匹查验场",
"440011":"中国（广东）自由贸易试验区",
"440101":"广州新沙码头",
"440102":"广州新风码头",
"440103":"广州河南码头",
"440104":"广州石榴岗码头",
"440105":"广州造纸厂码头",
"440106":"广州石井滘心港码头",
"440107":"广州东朗码头",
"440108":"广州萝岗车检场",
"440200":"广东省韶关市",
"440201":"韶关新港码头",
"440202":"韶关铁路装卸点",
"440203":"乐昌铁路装卸点",
"440204":"韶关车检场",
"440300":"广东省深圳市",
"440301":"南海港客运码头",
"440302":"南海三山港",
"440303":"南海九江码头",
"440304":"南海北村码头",
"440305":"南海平洲南港码头",
"440306":"南海官窑车检场",
"440307":"南海桂江车检场",
"440400":"广东省珠海市",
"440401":"顺德港客运码头",
"440402":"顺德容奇货运码头",
"440403":"顺德食出码头",
"440404":"顺德北滘港货运码头",
"440405":"顺德勒流港货运码头",
"440406":"顺德陈村车检场",
"440407":"顺德勒流车检场",
"440408":"顺德容奇车检场",
"440409":"顺德乐从车检场",
"440410":"顺德北窖车检场",
"440500":"广东省汕头市",
"440501":"汕头广澳港港务公司码头",
"440502":"汕头暹罗石油气码头",
"440503":"汕头海门电厂码头",
"440504":"汕头西堤码头",
"440505":"汕头永泰码头",
"440506":"汕头大明石油气码头",
"440507":"汕头华润水泥码头",
"440508":"汕头港务四公司煤码头",
"440509":"汕头港务三公司煤码头",
"440510":"汕头国集码头",
"440511":"汕头海通码头",
"440512":"汕头莱芜码头",
"440513":"汕头前江码头",
"440514":"汕头铁路装卸点",
"440515":"汕头联成车检场",
"440516":"汕头濠江车检场",
"440517":"汕头澄海车检场",
"440518":"汕头潮阳车检场",
"440519":"揭阳潮汕机场",
"440520":"汕头保税区",
"440521":"汕头海门中转基地码头",
"440600":"广东省佛山市",
"440601":"佛山铁路客运站",
"440602":"佛山澜石码头",
"440603":"佛山滘口码头",
"440604":"佛山新港码头",
"440605":"佛山车检场",
"440700":"广东省江门市",
"440701":"江门港客运码头",
"440702":"江门鹤山港客运码头",
"440703":"江门台山核电重件码头 ",
"440704":"江门台山国华粤电煤码头 ",
"440705":"江门台山公益港客运码头",
"440706":"江门中外运外海货柜码头",
"440707":"江门国际货柜码头",
"440708":"江门荷塘码头",
"440709":"江门台山公益码头",
"440710":"江门恩平横板码头",
"440711":"江门鹤山港货运码头",
"440712":"江门车检场",
"440713":"江门台山车检场",
"440714":"江门鹤山车检场",
"440715":"江门恩平车检场",
"440800":"广东省湛江市",
"440801":"湛江机场",
"440802":"湛江港集团公司霞山港区码头",
"440803":"湛江南油码头",
"440804":"湛江港集团公司调顺港区码头",
"440805":"湛江调顺岛电力公司码头",
"440806":"湛江霞海港码头",
"440807":"湛江霞海中外运码头",
"440808":"湛江霞山长桥码头",
"440809":"湛江徐闻海安港码头",
"440810":"湛江廉江营仔港码头",
"440811":"湛江遂溪北潭港码头",
"440812":"湛江雷州流沙港码头",
"440813":"湛江霞海港车检场",
"440814":"湛江宝满港集装箱码头",
"440815":"湛江霞山散货码头",
"440816":"湛江东海岛宝钢基地码头",
"440817":"湛江东海岛宝钢成品码头",
"440818":"湛江吴川车检场",
"440819":"湛江龙腾码头",
"440820":"湛江遂溪车检场",
"440900":"广东省茂名市",
"440901":"茂名水东港港口公司双泊位码头",
"440902":"茂名水东港石化公司码头",
"440903":"茂名水东港30万吨级单点",
"440904":"茂名水东港天源化工码头",
"440905":"茂名水东港港口公司公用码头",
"440906":"茂名水东港隆港石油码头",
"440907":"茂名水东港长晟综合码头",
"440908":"茂名水东港天源煤炭码头",
"440909":"茂名博贺港码头",
"441001":"潮州车检场",
"441101":"潮州三百门港务码头",
"441102":"潮州三百门华丰油气码头",
"441103":"潮州三百门恒业码头",
"441104":"潮州金狮湾华丰油气码头",
"441105":"潮州金狮湾大唐电厂煤码头",
"441106":"潮州金狮湾亚太一期码头",
"441107":"饶平车检场",
"441200":"广东省肇庆市",
"441201":"肇庆铁路客运站",
"441202":"肇庆港客运码头",
"441203":"肇庆三榕港码头",
"441204":"肇庆新港码头",
"441205":"肇庆高要港码头",
"441206":"肇庆德庆康州码头",
"441207":"肇庆四会港码头",
"441208":"肇庆大旺车检场",
"441300":"广东省惠州市",
"441301":"惠州大澳塘码头 ",
"441302":"惠州平海电厂煤码头 ",
"441303":"惠州碧甲码头 ",
"441304":"惠州博罗宏兴码头",
"441305":"惠州石湾集装箱码头",
"441306":"惠州车检场",
"441307":"惠州淡水车检场",
"441308":"惠州惠东车检场",
"441309":"惠州红海车检场",
"441310":"惠州园洲车检场",
"441311":"惠州中海油基地码头",
"441400":"广东省梅州市",
"441401":"梅州机场",
"441402":"梅州车检场",
"441500":"广东省汕尾市",
"441501":"汕尾港务码头",
"441502":"汕尾电厂码头",
"441503":"汕尾万聪码头",
"441504":"汕尾乌坎码头",
"441505":"汕尾海丰车检场",
"441506":"汕尾陆丰车检场",
"441507":"汕尾车检场",
"441508":"汕尾华润海丰电厂码头",
"441600":"广东省河源市",
"441601":"河源车检场",
"441700":"广东省阳江市",
"441701":"阳江港务公司码头",
"441702":"阳江良港码头",
"441703":"阳江保丰码头",
"441704":"阳江闸坡码头",
"441705":"阳江东平码头",
"441706":"阳江溪头港码头",
"441707":"阳江车检场",
"441708":"华夏阳西电厂码头",
"441800":"广东省清远市",
"441801":"清远新港码头",
"441802":"清远英德码头",
"441803":"清远铁路装卸点",
"441804":"清远车检场",
"441900":"广东省东莞市",
"441901":"东莞铁路客运站",
"441902":"东莞虎门港客运码头",
"441903":"东莞海腾码头",
"441904":"东莞华润水泥码头 ",
"441905":"东莞金明石化码头 ",
"441906":"东莞国际货柜码头 ",
"441907":"东莞飞虎石化码头 ",
"441908":"东莞荣轩货柜码头 ",
"441909":"东莞同舟石化码头 ",
"441910":"东莞三江石化码头 ",
"441911":"东莞虎门港5、6号泊位",
"441912":"东莞九丰石化码头 ",
"441913":"东莞东洲油气化工码头 ",
"441914":"东莞虎门港7、8号泊位",
"441915":"东莞中海油立沙码头 ",
"441916":"东莞南粤码头 ",
"441917":"东莞东江口码头 ",
"441918":"东莞海昌煤码头 ",
"441919":"东莞深赤湾散杂货码头 ",
"441920":"东莞宏业货柜码头 ",
"441921":"东莞沙角A电厂煤码头 ",
"441922":"东莞沙角B电厂煤码头 ",
"441923":"东莞沙角C电厂煤码头 ",
"441924":"东莞龙通码头",
"441925":"东莞基业码头",
"441926":"东莞中外运石龙码头",
"441927":"东莞马士基码头",
"441928":"东莞永安码头",
"441929":"东莞联通码头",
"441930":"常平铁路装卸点",
"441931":"东莞虎门车检场",
"441932":"东莞寮步车检场",
"441933":"东莞立沙岛阳鸿石化码头",
"441934":"东莞立沙岛鸿源油品码头",
"441935":"东莞石龙铁路物流中心",
"441936":"东莞联兴化工码头",
"442000":"广东省中山市",
"442001":"中山港货运码头",
"442002":"中山港客运码头",
"442003":"中山港外贸码头",
"442004":"中山石岐纸厂码头",
"442005":"中山水出码头",
"442006":"中山小榄码头",
"442007":"中山神湾码头",
"442008":"中山小榄车检场",
"442009":"中山保税物流中心车检场",
"442010":"中山神湾游艇码头",
"442011":"中山黄圃多用途码头",
"442101":"黄埔石化码头",
"442102":"黄埔新港码头",
"442103":"黄埔集装箱码头",
"442104":"黄埔墩头西基码头",
"442105":"黄埔东江口码头",
"442106":"黄埔省物资码头",
"442107":"黄埔广保通码头",
"442108":"广州开发区东江仓码头",
"442109":"黄埔中外运东江仓码头",
"442110":"中外运黄埔仓码头",
"442111":"黄埔庙沙围码头",
"442112":"黄埔庙头建翔码头",
"442113":"黄埔集通码头",
"442114":"广州开发区车检场",
"442115":"广州保税物流园区",
"442116":"广州保税区",
"442117":"广州出口加工区",
"442201":"广州铁路客运站",
"442202":"广州东圃永业码头",
"442301":"广州白云国际机场",
"442302":"广州白云机场综合保税区",
"442401":"番禺莲花山客运港",
"442402":"番禺莲花山货运港",
"442403":"番禺沙湾车检场",
"442501":"花都港码头",
"442502":"花都车检场",
"442601":"增城新塘港客运码头",
"442602":"增城新塘东洲湾码头",
"442603":"增城新塘口岸码头",
"442604":"增城新塘食出码头",
"442605":"增城新塘车检场",
"442701":"从化车检场",
"442801":"江门天马码头 ",
"442802":"江门新会电厂码头 ",
"442803":"江门银湖修船码头 ",
"442804":"江门宜大化工码头 ",
"442805":"新会港客运码头",
"442806":"新会西河口码头",
"442807":"新会今古洲码头",
"442808":"新会睦洲糖纸厂码头",
"442809":"新会大敖集装箱厂码头",
"442810":"新会崖门沙石泥码头",
"442811":"新会崖西沙石泥码头",
"442812":"新会牛牯岭易燃品码头",
"442813":"新会车检场",
"442814":"新会航建码头",
"442901":"江门三埠港客运码头",
"442902":"江门三埠港货运码头",
"442903":"江门水口码头",
"442904":"开平车检场",
"443001":"高明港客运码头",
"443002":"高明食出码头",
"443003":"高明珠江码头",
"443004":"高明车检场",
"443101":"惠州港通用码头 ",
"443102":"惠州港油气码头 ",
"443103":"惠州大港石化码头 ",
"443104":"惠州泽华石化码头 ",
"443105":"惠州国际集装箱码头",
"443106":"惠州马鞭洲石化码头 ",
"443107":"惠州中海壳牌马鞭洲码头 ",
"443108":"惠州中海壳牌东联码头 ",
"443109":"惠州中海炼油马鞭洲码头",
"443110":"惠州中海炼油东联码头",
"443111":"大亚湾石化区公用石化码头",
"443201":"揭阳榕江泰丰码头",
"443202":"揭阳靖海惠来电厂码头",
"443203":"揭阳曲溪码头",
"443204":"揭阳车检场",
"443205":"揭阳惠来车检场",
"443206":"揭阳普宁车检场",
"443207":"揭阳神泉中海油码头",
"443301":"云浮新港",
"443302":"云浮罗定车检场",
"443303":"云浮车检场",
"443401":"南沙港客运码头",
"443402":"南沙南伟码头",
"443403":"南沙东发码头",
"443404":"南沙港一期码头",
"443405":"南沙港二期码头",
"443406":"南沙汽车码头",
"443407":"南沙港发石化码头",
"443408":"南沙小虎石化码头",
"443409":"南沙华润热电煤码头",
"443410":"南沙粤海石化码头",
"443411":"南沙珠江电厂码头",
"443412":"南沙中石油码头",
"443413":"南沙中船龙穴船舶维修码头",
"443414":"南沙港建液化气码头",
"443415":"南沙车检场",
"443416":"广州南沙保税港区",
"443417":"南沙港三期码头",
"445100":"广东省潮州市",
"445200":"广东省揭阳市",
"445300":"广东省云浮市",
"450001":"南宁",
"450002":"南宁吴圩机场",
"450101":"梧州",
"450200":"广西壮族自治区柳州市",
"450201":"北海福成机场",
"450202":"北海",
"450203":"石头埠",
"450204":"北海出口加工区",
"450300":"广西壮族自治区桂林市",
"450301":"防城",
"450302":"茅岭",
"450303":"企沙",
"450400":"广西壮族自治区梧州市",
"450401":"凭祥友谊关公路",
"450402":"凭祥友谊关通道",
"450403":"凭祥浦寨通道",
"450404":"凭祥弄尧通道",
"450405":"凭祥站",
"450406":"爱店",
"450407":"凭祥综合保税区",
"450500":"广西壮族自治区北海市",
"450501":"水口",
"450502":"硕龙",
"450600":"广西壮族自治区防城港市",
"450601":"东兴",
"450602":"江平",
"450603":"江山",
"450604":"峒中",
"450700":"广西壮族自治区钦州市",
"450701":"果子山",
"450702":"钦州",
"450800":"广西壮族自治区贵港市",
"450801":"桂林两江国际机场",
"450900":"广西壮族自治区玉林市",
"450901":"柳州",
"451000":"广西壮族自治区百色市",
"451100":"广西壮族自治区贺州市",
"451101":"贵港",
"451200":"广西壮族自治区河池市",
"451201":"岳圩",
"451202":"龙邦",
"451203":"平孟",
"451300":"广西壮族自治区来宾市",
"451301":"钦州保税港区",
"451400":"广西壮族自治区崇左市",
"451501":"南宁港",
"460001":"海口",
"460002":"海口港",
"460003":"马村港",
"460004":"海口综合保税区",
"460005":"洋浦保税港区",
"460101":"三亚凤凰国际机场",
"460102":"三亚港",
"460200":"海南省三亚市",
"460201":"八所港",
"460300":"海南省三沙市",
"460301":"洋浦港 ",
"460302":"洋浦神头港",
"460400":"海南省儋州市",
"460401":"清澜港",
"460402":"铺前",
"460403":"琼海潭门",
"460501":"海口美兰机场",
"469000":"海南省省直辖县级行政区划",
"470001":"深圳",
"470101":"蛇口",
"470102":"赤湾",
"470103":"东角头",
"470104":"妈湾1号泊位",
"470105":"妈湾2号泊位",
"470106":"妈湾3号泊位",
"470107":"妈湾4号泊位",
"470201":"皇岗",
"470202":"福田",
"470301":"罗湖",
"470401":"文锦渡",
"470501":"沙头角",
"470502":"中英街桥头",
"470503":"深圳沙头角保税区",
"470601":"盐田",
"470602":"下洞码头",
"470603":"LNG码头",
"470604":"沙鱼冲",
"470605":"深圳盐田保税物流园区",
"470606":"深圳盐田保税区",
"470701":"大亚湾",
"471001":"深圳宝安国际机场",
"471002":"福永码头",
"471003":"机场油轮码头",
"471101":"深圳福田保税区",
"471301":"深圳出口加工区",
"471401":"深圳湾",
"471601":"大铲湾",
"471801":"妈湾5号泊位",
"471802":"妈湾6号泊位",
"471803":"妈湾7号泊位",
"471804":"深圳前海湾保税港区",
"480001":"珠海",
"480002":"拱北货场",
"480003":"拱北旅检通道",
"480004":"香洲",
"480005":"九洲货运码头",
"480006":"九洲客运码头",
"480007":"九洲白石货场",
"480008":"湾仔豪通码头",
"480009":"湾仔客运码头",
"480010":"湾仔西域码头",
"480011":"珠海保税区加华码头",
"480012":"珠海保税区货场",
"480013":"珠澳跨境工业区（珠海园区）货场",
"480014":"珠澳跨境工业区（珠海园区）旅检通道",
"480015":"湾仔洪湾码头",
"480101":"斗门客运码头",
"480102":"斗门新环码头",
"480201":"高栏",
"480202":"平沙新码头",
"480301":"万山",
"480401":"横琴",
"500001":"重庆",
"500002":"重庆港",
"500101":"万州",
"500201":"九龙坡港",
"500401":"重庆两路寸滩保税港区水港",
"500402":"重庆两路寸滩保税港区空港",
"500501":"重庆西永综合保税区",
"500601":"重庆江北国际机场",
"510001":"成都",
"510002":"成都国际邮件互换局",
"510003":"成都双流国际机场T1航站楼",
"510004":"成都双流国际机场货场",
"510005":"成都双流国际机场国际快件",
"510006":"宜宾港",
"510007":"成都龙泉驿",
"510008":"成都青白江",
"510101":"攀枝花",
"510300":"四川省自贡市",
"510400":"四川省攀枝花市",
"510401":"乐山",
"510500":"四川省泸州市",
"510600":"四川省德阳市",
"510601":"绵阳",
"510602":"绵阳出口加工区",
"510700":"四川省绵阳市",
"510701":"泸州港",
"510800":"四川省广元市",
"510900":"四川省遂宁市",
"511000":"四川省内江市",
"511001":"成都高新综合保税区双流园区",
"511002":"成都高新综合保税区A区",
"511003":"成都高新综合保税区B区",
"511004":"成都高新综合保税区C区",
"511100":"四川省乐山市",
"511300":"四川省南充市",
"511400":"四川省眉山市",
"511500":"四川省宜宾市",
"511600":"四川省广安市",
"511700":"四川省达州市",
"511800":"四川省雅安市",
"511900":"四川省巴中市",
"512000":"四川省资阳市",
"513200":"四川省阿坝藏族羌族自治州",
"513300":"四川省甘孜藏族自治州",
"513400":"四川省凉山彝族自治州",
"520001":"贵阳",
"520002":"贵阳龙洞堡机场",
"520003":"贵阳综合保税区",
"520004":"贵安综合保税区",
"520200":"贵州省六盘水市",
"520300":"贵州省遵义市",
"520400":"贵州省安顺市",
"520500":"贵州省毕节市",
"520600":"贵州省铜仁市",
"522300":"贵州省黔西南布依族苗族自治州",
"522600":"贵州省黔东南苗族侗族自治州",
"522700":"贵州省黔南布依族苗族自治州",
"530001":"昆明",
"530002":"昆明出口加工区",
"530003":"昆明综合保税区",
"530101":"瑞丽",
"530102":"姐告",
"530103":"弄岛",
"530104":"畹町",
"530201":"芒市",
"530202":"盈江",
"530203":"章凤",
"530300":"云南省曲靖市",
"530301":"腾冲",
"530302":"猴桥",
"530303":"滇滩",
"530400":"云南省玉溪市",
"530401":"耿马",
"530402":"孟定清水河",
"530403":"沧源",
"530404":"南伞",
"530500":"云南省保山市",
"530501":"河口站",
"530502":"河口",
"530600":"云南省昭通市",
"530601":"西双版纳",
"530602":"西双版纳国际机场",
"530603":"景洪港",
"530604":"打洛",
"530605":"大勐龙",
"530700":"云南省丽江市　　　　　　　",
"530701":"普洱",
"530702":"孟连",
"530703":"思茅港",
"530800":"云南省普洱市",
"530801":"勐腊",
"530802":"磨憨",
"530803":"关累港",
"530900":"云南省临沧市",
"530901":"金平",
"530902":"金水河",
"530903":"红河综合保税区",
"531001":"麻栗坡",
"531002":"天保",
"531003":"田蓬",
"531101":"大理",
"531201":"昆明长水国际机场",
"531301":"丽江三义机场",
"531401":"勐康",
"531402":"龙富",
"531501":"怒江",
"531502":"片马",
"532300":"云南省楚雄彝族自治州",
"532500":"云南省红河哈尼族彝族自治州",
"532600":"云南省文山壮族苗族自治州",
"532800":"云南省西双版纳傣族自治州",
"532900":"云南省大理白族自治州",
"533100":"云南省德宏傣族景颇族自治州",
"533300":"云南省怒江傈僳族自治州",
"533400":"云南省迪庆藏族自治州",
"540001":"拉萨",
"540002":"拉萨贡嘎机场",
"540101":"吉隆",
"540102":"樟木",
"540103":"日屋",
"540200":"西藏自治区日喀则市",
"540201":"普兰",
"540300":"西藏自治区昌都市",
"540400":"西藏自治区林芝市",
"540500":"西藏自治区山南市",
"540600":"西藏自治区那曲市",
"542500":"西藏自治区阿里地区",
"610001":"西安",
"610002":"西安咸阳国际机场",
"610003":"宝鸡",
"610004":"汉中",
"610005":"榆林",
"610006":"延安",
"610007":"渭南",
"610008":"西安综合保税区",
"610009":"西安出口加工区",
"610010":"西安高新综合保税区",
"610200":"陕西省铜川市",
"610300":"陕西省宝鸡市",
"610400":"陕西省咸阳市",
"610500":"陕西省渭南市",
"610600":"陕西省延安市",
"610700":"陕西省汉中市",
"610800":"陕西省榆林市",
"610900":"陕西省安康市",
"611000":"陕西省商洛市",
"620001":"兰州",
"620002":"兰州中川机场",
"620003":"兰州新区综合保税区",
"620101":"酒泉",
"620102":"马鬃山",
"620200":"甘肃省嘉峪关市",
"620300":"甘肃省金昌市",
"620400":"甘肃省白银市",
"620500":"甘肃省天水市",
"620600":"甘肃省武威市",
"620700":"甘肃省张掖市",
"620800":"甘肃省平凉市",
"620900":"甘肃省酒泉市",
"621000":"甘肃省庆阳市",
"621100":"甘肃省定西市",
"621200":"甘肃省陇南市",
"622900":"甘肃省临夏回族自治州",
"623000":"甘肃省甘南藏族自治州",
"630001":"西宁",
"630002":"西宁曹家堡机场",
"630200":"青海省海东市",
"632200":"青海省海北藏族自治州",
"632300":"青海省黄南藏族自治州",
"632500":"青海省海南藏族自治州",
"632600":"青海省果洛藏族自治州",
"632700":"青海省玉树藏族自治州",
"632800":"青海省海西蒙古族藏族自治州",
"640001":"银川",
"640101":"银川河东机场",
"640200":"宁夏回族自治区石嘴山市",
"640300":"宁夏回族自治区吴忠市",
"640400":"宁夏回族自治区固原市",
"640500":"宁夏回族自治区中卫市",
"650001":"乌鲁木齐",
"650002":"乌鲁木齐地窝堡国际机场",
"650101":"阿勒泰",
"650102":"红山嘴",
"650103":"阿黑土别克",
"650104":"塔克什肯",
"650200":"新疆维吾尔自治区克拉玛依市",
"650201":"巴克图",
"650202":"巴克图国际汽车货运",
"650301":"阿拉山口公路",
"650302":"阿拉山口铁路",
"650303":"阿拉山口综合保税区",
"650400":"新疆维吾尔自治区吐鲁番市",
"650401":"伊犁",
"650402":"伊犁州国际汽车",
"650403":"木扎尔特",
"650404":"都拉塔",
"650500":"新疆维吾尔自治区哈密市",
"650501":"霍尔果斯",
"650701":"巴州",
"650801":"喀什",
"650802":"其尼瓦格国际汽车",
"650803":"喀什机场",
"650804":"红其拉甫",
"650805":"喀什综合保税区",
"650901":"伊尔克什坦",
"650902":"吐尔尕特",
"650903":"阿图什国际汽车",
"651001":"吉木乃",
"651101":"卡拉苏",
"651201":"奎屯",
"651301":"霍尔果斯国际边境合作中心(中方配套区)",
"651401":"老爷庙",
"651501":"乌拉斯台",
"651502":"乌鲁木齐铁路",
"651503":"乌鲁木齐碾子沟客运站",
"651504":"边疆宾馆国际汽车货运",
"651505":"乌鲁木齐出口加工区",
"652300":"新疆维吾尔自治区昌吉回族自治州",
"652700":"新疆维吾尔自治区博尔塔拉蒙古自治州",
"652800":"新疆维吾尔自治区巴音郭楞蒙古自治州",
"652900":"新疆维吾尔自治区阿克苏地区",
"653000":"新疆维吾尔自治区克孜勒苏柯尔克孜自治州",
"653100":"新疆维吾尔自治区喀什地区",
"653200":"新疆维吾尔自治区和田地区",
"654000":"新疆维吾尔自治区伊犁哈萨克自治州",
"654200":"新疆维吾尔自治区塔城地区",
"654300":"新疆维吾尔自治区阿勒泰地区",
"659000":"新疆维吾尔自治区自治区直辖县级行政单位",
"999999":"未列出的特殊监管区",
}

def customs_port_handler(input_text):
    for code,name in CUSTOMS_PORT.items():
        if code == input_text or name == input_text:
            return code,name
    return "",input_text


#关别与口岸的对应关系
CUSTOMS_TO_PORT = {
    "大兴机场（0106）":"北京大兴国际机场（115301）",
    "济机场关（4302）":"济南遥墙机场（370701）",
    "津机场办（0207）":"天津滨海国际机场货邮（121701）",
    "浦东机场（2233）":"上海浦东国际机场（310302）",
    "广州机场（5141）":"广州白云国际机场（442301）",
    "皇岗海关（5301）":"皇岗（470201）",
    "沙头角关（5303）":"沙头角（470501）",
    "机场海关（3715）":"厦门高崎国际机场（399101）",
    "郑机场关（4604）":"郑州新郑国际机场（410003）",
    "禄口机场（2317）":"南京禄口国际机场（320101）",
    "满铁路（0609）":"满洲里铁路（150102）",
    "青岛大港（4227）":"青岛港（370101）",
    "泉城海关（4301）":"济南(370001)",
    "外港海关（2225）":"外高桥（310701）",
    "上海快件（2244）":"上海浦东国际机场（310302）",
    "武关机场（4712）":"武汉天河机场（420002）",
    "杭州机场（2910）":"杭州萧山国际机场(330002)",
    "沈机场关(0807)":"沈阳桃仙国际机场(210301)",
    "广州机场(5141)":"广州白云国际机场(442301)",
    "洋山港区（2248）":"洋山港（311002）",
    "广州新沙海关(5212)":"广州新沙码头（440101）",
    "大连港湾（0901）":"大连港大连湾港区（211903）",
    "连大窑湾（0908）":"大连港大窑湾港区（210101）",
    "鲅鱼圈关（0950）":"营口港鲅鱼圈港区（211001）",
    "京唐港关（0422）":"京唐港（130201）",
    "曹妃甸关（0412）":"曹妃甸港（130203）",
    "太仓海关（2327）":"太仓（320701）",
    "蛇口海关（5304）":"蛇口（470101）",
    "烟台海关（4201）":"烟台港（370302）",
    "龙口海关（4203）":"龙口（370501）",
    "连云港关（2301）":"连云港（321201）",
    "东渡海关（3711）":"厦门东渡港区（399501）",
    "海沧港区（3708）":"厦门海沧港区（390101）",
    "榕福清办（3502）":"福州保税港区（350008）",
    "瑞丽海关（8603）":"瑞丽（530101）",
    "勐腊海关 (8612)":"磨憨(530802)",
    "二关铁路（0702）":"二连浩特铁路（150201）",
    "满十八里（0603）":"满洲里十八里（150104）",
    "济综保区（4303）":"济南综合保税区（370702）",
    "章锦综保区（4306）":"济南章锦综合保税区（405301）",
    "日照海关(4202)":"日照(370002)",
}

def customs_to_port_handler(IEPort,EntyPortCode):
    if IEPort and EntyPortCode:
        return IEPort,EntyPortCode
    elif IEPort:
        input_text = IEPort
    elif EntyPortCode:
        input_text = EntyPortCode
    else:
        return "",""
    for custome,port in CUSTOMS_TO_PORT.items():
        if custome == input_text or port == input_text:
            return custome,port
    return "",""